/*! Generated by <PERSON>ont Squirrel (https://www.fontsquirrel.com) on January 18, 2018 */

/*@font-face {*/
/*    font-family: 'stimupratlight';*/
/*    src: url('stimuprat-light-webfont.woff2') format('woff2'),*/
/*         url('stimuprat-light-webfont.woff') format('woff');*/
/*    font-weight: normal;*/
/*    font-style: normal;*/
/*}*/

/*@font-face {*/
/*    font-family: 'stimupratmedium';*/
/*    src: url('stimuprat-medium-webfont.woff2') format('woff2'),*/
/*         url('stimuprat-medium-webfont.woff') format('woff');*/
/*    font-weight: normal;*/
/*    font-style: normal;*/
/*}*/

/*@font-face {*/
/*    font-family: 'stimupratregular';*/
/*    src: url('stimuprat-regular-webfont.woff2') format('woff2'),*/
/*         url('stimuprat-regular-webfont.woff') format('woff');*/
/*    font-weight: normal;*/
/*    font-style: normal;*/
/*}*/

/*@font-face {*/
/*    font-family: 'stimupratbold';*/
/*    src: url('stimuprat-bold-webfont.woff2') format('woff2'),*/
/*         url('stimuprat-bold-webfont.woff') format('woff');*/
/*    font-weight: normal;*/
/*    font-style: normal;*/
/*}*/

/*@font-face {*/
/*    font-family: 'dinot';*/
/*    src: url('DINOT-Cond.otf') format('opentype');*/
/*    font-weight: normal;*/
/*    font-style: normal;*/
/*}*/

/*@font-face {*/
/*    font-family: 'dinotbold';*/
/*    src: url('DINOT-CondBold.otf') format('opentype');*/
/*    font-weight: normal;*/
/*    font-style: normal;*/
/*}*/

/*@font-face {*/
/*    font-family: 'dinotlight';*/
/*    src: url('DINOT-Light.otf') format('opentype');*/
/*    font-weight: normal;*/
/*    font-style: normal;*/
/*}*/

@font-face {
    font-family: 'nimbus_sans_lregular';
    src: url('NimbusSans-Regular.otf') format('opentype');
    font-weight: normal;
    font-style: normal;
}
