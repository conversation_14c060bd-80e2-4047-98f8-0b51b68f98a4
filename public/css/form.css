body {
    font-family: Arial, sans-serif;
    font-size: 16px;
}

header {
    text-transform: uppercase;
}

.bold {
    font-weight: bold;
    font-family: stimupratbold;
}

.blue {
    color: #107D85;
}

h1 {
    text-transform: uppercase;
    color: #107D85;
    font-family: stimupratlight, sans-serif;
    letter-spacing: -0.07em;
    margin-bottom: 10px;
    font-size: 40px;
}

h2 {
    text-transform: uppercase;
    font-family: stimupratbold, sans-serif;
    letter-spacing: -0.05em;
    margin-top: 0;
    font-size: 24px;
}

h3.arrow-title {
    background: #DBDEE0;
    color: #333333;
    padding: 6px;
    border-radius: 25px;
    text-transform: uppercase;
    font-size: 20px;
    position: relative;
    padding-left: 38px;
    font-family: dinotbold, sans-serif;
    margin-bottom: 20px;
}

h3 > img {
    margin-top: -4px;
    margin-right: 5px;
}

h3.arrow-title::before {
    content: "";
    background-image: url(../img/arrow.png);
    background-repeat: no-repeat;
    width: 17px;
    height: 17px;
    position: absolute;
    background-size: 16px;
    left: 14px;
    top: 9px;
}

#logo img {
    width: 150px;
}

#form-container.bg-form {
    background-image: url(../img/fd_eduprat_flou.jpg);
    background-repeat: no-repeat;
    background-position: top left;
}

hr {
    border-width: 2px;
    border-color: #dadee0;
}

footer {
    border-top: 2px solid #dadee0;
    padding-top: 10px;
}

footer .bold {
    font-family: stimupratbold, sans-serif;
    font-size: 16px;
}

footer img {
    margin-right: 20px;
    height: 42px;
}

footer .blue {
    font-size: 14px;
}

textarea#evaluation_global_comment_1, textarea#audit_description {
    background: #dadee0;
    color: #333;
    resize: vertical;
}

.text-uppercase {
    text-transform: uppercase;
}