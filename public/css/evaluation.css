h3 {
    background: #DBDEE0;
    color: #333333;
    padding: 6px;
    border-radius: 25px;
    text-transform: uppercase;
    font-size: 20px;
    position: relative;
    padding-left: 38px;
    font-family: dinotbold, sans-serif;
    margin-bottom: 20px;
}

h3::before {
    content: "";
    background-image: url(../img/arrow.png);
    background-repeat: no-repeat;
    width: 17px;
    height: 17px;
    position: absolute;
    background-size: 16px;
    left: 14px;
    top: 11px;
}

h4 {
    color: #333333;
    margin-left: 1em;
    font-family: stimupratbold, sans-serif;
}

h4 > .blue {
    color: #107D85;
    text-transform: uppercase;
    font-family: stimupratlight, sans-serif;
    font-weight: 400;
}

h5 {
    color: #107D85;
    text-transform: uppercase;
    font-family: stimupratlight, sans-serif;
    font-weight: 400;
}

.form-group {
    margin-left: 1.5em;
}

.reporting-container label.control-label.required::before {
    content: "●";
    color: #459494; /* or whatever color you prefer */
    margin-right: 4px;
    font-size: 16px;
}

.reporting-container label.control-label.required {
    margin-left: 7px;
    font-family: stimupratregular, sans-serif;
}

.question {
    margin-bottom: 1.5em;
    padding-bottom: 0;
    border-bottom: none;
}

.activity {
    margin-bottom: 1.5em;
    padding-bottom: 0;
    border-bottom: none;
}

.question-radio {
    max-width: 800px;
}

.question-radio, .question-range {
    margin-top: 10px;
}

.checkbox-inline, .radio-inline {
    font-family: stimupratregular, sans-serif;
    font-size: 16px;
}

.radio-yes-no {
    max-width: 200px;
}

label {
    font-weight: normal;
}

#evaluation-submit {
    margin-top: 2em;
}

#evaluation-submit > button{
    font-family: stimupratregular, sans-serif;
    font-size: 16px;
}

.pagination {
    font-family: stimupratregular, sans-serif;
    font-size: 16px;
}

#spiderweb {
    text-align: center;
}

.evaluation-number {
    text-align: center;
}

span.evaluation-number-number {
    border-bottom: 2px solid black;
    display: block;
    font-size: 26px;
    font-family: stimupratbold, sans-serif;
    margin-bottom: 5px;
}

.blue .evaluation-number-number {
    border-bottom: 2px solid #107D85;
}

span.evaluation-number-label {
    font-family: dinotlight;
    font-size: 9px;
    line-height: 13px;
    padding-top: 67px;
    text-transform: uppercase;
}

.evaluation-former {
    margin-left: 0;
    text-decoration: underline;
    margin-top: 0;
}