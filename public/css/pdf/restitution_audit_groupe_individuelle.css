
#programme {
    padding: 1em;
    background: #ddd;
    float: right;
    min-width: 330px;
    font-family: stimupratlight;
}

#coordinateur-formateur {
    font-size: 1em;
}

.newFormateur {
    position: absolute;
    left: 9cm;
    top: 3cm;
    max-width: 15cm;
    font-size: 1em;
}

#contact {
    position: absolute;
    left: 4cm;
    top: 0cm;
    max-width: 15cm;
    font-size: 1em;
}

.title {
    text-align: center;
    font-size: 1.9em;
    /*margin-top: 0.25cm;*/
}
.littletitle {
    text-align: center;
    font-size: 1.25em;
}
.centered {
    text-align: center;
    margin-top: 0.5cm;
    /*margin-bottom: 1cm;*/
}
.infos {
    margin-top: 0.25cm;
    font-weight: bold;
}
.restitution-row {
    page-break-inside: avoid;
    padding-top: 1.8em;
}

.restitution-row::after {
    content: '';
    clear: both;
    display: block;
}

.interpretation {
    letter-spacing: -1px;
    font-family: stimupratbold, sans-serif;
    margin-bottom: 2em;
}

.interpretation p {
    margin: 0;
}
.comments > ul {
    list-style-position: inside;
}
#legend {
    height: 6cm;
    padding-top: 1cm;
}
#legend ul {
    margin-left: 0;
}

#legend li {
    margin-bottom: 0.5em;
}

#legend li > img {
    margin-right: 1em;
}

.interpretation, .interpretation span, .interpretation p {
    font-size: 1em !important;
    margin-bottom: 1em;
}

#detail-title {
    font-size: 1.2em;
}

#spiderweb {
    margin: 2em 0 0.2em 0;
}

.highcharts-root, .highcharts-container {
    overflow: visible !important;
}
.section {
    padding-top: 1em;
    page-break-inside: avoid;
}

.section-title {
    margin-bottom: 0;
    font-size: 1.75em;
    text-transform: uppercase;
    padding-top: 0.125em;
    font-family: stimupratbold, sans-serif;
    font-weight: 600;
}

.progress {
    font-size: 0;
    height: 20px;
    margin-bottom: 3px;
    background-color: transparent;
}

.progress-meter {
    display: inline-block;
}

.progress-text {
    font-size: 10px;
}

.progress-meter-success {
    background: linear-gradient(to right, #8b5e92, #6086b9);
    background: -webkit-gradient(linear,left top,right bottom,color-stop(0%, #8b5e92),color-stop(100%, #6086b9));
    border-top-left-radius: 6px;
    border-bottom-left-radius: 6px;
}

.progress-meter-error {
    background-color: #d71903;
    border-top-right-radius: 6px;
    border-bottom-right-radius: 6px;
}

.restitution-question + p {
    margin-bottom: 0.5rem;
}

#barchart .bar {
    fill: #6c5d9f;
}
#barchart .axis path, #barchart .axis line {
    fill: none; stroke: #000; shape-rendering: crispEdges;
}

.page-break + .section {
    padding-top: 1cm;
}

.footer-fixed {
    position: absolute;
    top: 28.2cm;
    text-align: center;
    width: 100%;
    left:0;
    line-height: 1.3em;
    font-size: 0.8em;
}


input[type=checkbox] {
    display: inline-block;
    vertical-align: text-top;
    margin-bottom: 0;
    width: 0;
    position: relative;
}

.checkbox label:before {
    content: "";
    display: inline-block;
    width: 15px;
    height: 15px;
    vertical-align: text-top;
    background: #d8dde4;
}

/* Create the checkmark/indicator (hidden when not checked) */
input:checked:after {
    content: "";
    position: absolute;
    display: none;
}

/* Show the checkmark when checked */
.checkbox input:checked:after {
    display: block;
}

/* Style the checkmark/indicator */
.checkbox input:after {
    left: -10px;
    top: 0px;
    width: 6px;
    height: 12px;
    border: solid #000;
    border-width: 0 1px 1px 0;
    -webkit-transform: rotate(45deg);
    -ms-transform: rotate(45deg);
    transform: rotate(45deg);
}
.bold {
    font-family: inherit;
}

.boldOnly {
    font-weight: bold;
    font-family: 'Source Sans Pro', sans-serif !important;
}

.restitution-question.blue {
    font-size: 16px;
}

.restitution-question+p {
    font-size: 14px;
}

.answers label {
    font-size: 12px;
}

.answers-postulat {
    margin-top: 5px;
}

.answers-postulat p {
    margin-bottom: 0;
}

.restitution-patient {
    font-size: 16px;
    margin-top: 16px;
}

.restitution-row-pad {
    padding-left: 16px;
}

.radar-legend {
    display: block;
}

.radar-legend > span {
    width: 10px;
    height: 10px;
    border-radius: 50%;
    display: inline-block;
    margin-right: 5px;
}

.stats-chart-1-legend {
    background: linear-gradient(to right, #cda200, #dc6300);
    background: -webkit-gradient(linear,left top,right bottom,color-stop(0%, #cda200),color-stop(100%, #dc6300));
}

.stats-chart-2-legend {
    background: linear-gradient(to right, #307692, #52adb3);
    background: -webkit-gradient(linear,left top,right bottom,color-stop(0%, #307692),color-stop(100%, #52adb3));
}

.stats-chart-moy-legend {
    background: linear-gradient(to right, #8b5e92, #6086b9);
    background: -webkit-gradient(linear,left top,right bottom,color-stop(0%, #8b5e92),color-stop(100%, #6086b9));
}

.stats-chart-expert-legend {
    background: linear-gradient(to right, #e20000, #753f00);
    background: -webkit-gradient(linear,left top,right bottom,color-stop(0%, #e20000),color-stop(100%, #753f00));
}

.stats-chart-detail {
    height: 100px;
    width: 10px;
    background: #ddd;
    display: inline-block;
    border-radius: 5px;
    margin-left: 5px;
}

.stats-chart-fill {
    width: 100%;
    border-radius: 5px;
}

.stats-chart-1 .stats-chart-fill {
    background: linear-gradient(to bottom, #cda200, #dc6300);
    background: -webkit-gradient(linear,left top,right bottom,color-stop(0%, #cda200),color-stop(100%, #dc6300));
}

.stats-chart-2 .stats-chart-fill {
    background: linear-gradient(to bottom, #307692, #52adb3);
    background: -webkit-gradient(linear,left top,right bottom,color-stop(0%, #307692),color-stop(100%, #52adb3));
}

.stats-chart-moy .stats-chart-fill {
    background: linear-gradient(to bottom, #8b5e92, #6086b9);
    background: -webkit-gradient(linear,left top,right bottom,color-stop(0%, #8b5e92),color-stop(100%, #6086b9));
}

.stats-chart-expert .stats-chart-fill {
    background: linear-gradient(to bottom, #e20000, #753f00);
    background: -webkit-gradient(linear,left top,right bottom,color-stop(0%, #e20000),color-stop(100%, #753f00));
}

.section-radial {
    text-align: center;
}

.radial {
    width: 24%;
    display: inline-block;
    height: 150px;
    text-align: center;
}

.stats {
    width: 30%;
    display: inline-block;
    height: 150px;
    text-align: center;
    margin-bottom: 1em;
}

.stats > p {
    font-size: 16px;
}

.radial p {
    font-size: 1.1em;
    margin-bottom: 5px;
}

.radial-chart {
    width: 50%;
    margin: 0 auto;
}
.progressPath {
    stroke-opacity: 0;
}

#axes-amelioration {
    background: #E5EDF1;
    padding: 20px;
}

h5.synthese-category {
    color: #107D85;
    background: none;
    border-bottom: 2px solid #107D85;
    border-radius: 0;
    padding-left: 24px;
    margin-bottom: 0;
}

h5.synthese-category::before {
    left: 0px;
}

.section-synthese {
    padding-top: 20px;
    page-break-inside: avoid;
}
.synthese-row {
    padding-left: .9375rem;
}

.tcs-rendu-question ol, .tcs-rendu-question ul {
    padding-left: 20px;
}
