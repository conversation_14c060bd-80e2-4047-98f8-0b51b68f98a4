# In all environments, the following files are loaded if they exist,
# the latter taking precedence over the former:
#
#  * .env                contains default values for the environment variables needed by the app
#  * .env.local          uncommitted file with local overrides
#  * .env.$APP_ENV       committed environment-specific defaults
#  * .env.$APP_ENV.local uncommitted environment-specific overrides
#
# Real environment variables win over .env files.
#
# DO NOT DEFINE PRODUCTION SECRETS IN THIS FILE NOR IN ANY OTHER COMMITTED FILES.
# https://symfony.com/doc/current/configuration/secrets.html
#
# Run "composer dump-env prod" to compile .env files for production use (requires symfony/flex >=1.2).
# https://symfony.com/doc/current/best_practices.html#use-environment-variables-for-infrastructure-configuration

###> symfony/framework-bundle ###
APP_ENV=dev
APP_SECRET=4e29263680c0f0083182bfa74dc877b9
#TRUSTED_PROXIES=*********/8,10.0.0.0/8,**********/12,***********/16
#TRUSTED_HOSTS='^(localhost|example\.com)$'
###< symfony/framework-bundle ###

###> symfony/mailer ###
MAILER_DSN=smtp://localhost?encryption=ssl&verify_peer=0
###< symfony/mailer ###

###> doctrine/doctrine-bundle ###
# Format described at https://www.doctrine-project.org/projects/doctrine-dbal/en/latest/reference/configuration.html#connecting-using-a-url
# IMPORTANT: You MUST configure your server version, either here or in config/packages/doctrine.yaml
DATABASE_URL=mysql://root:root@mariadb:3306/anetdb?serverVersion=10.11.3-MariaDB&charset=utf8&collation=utf8_unicode_ci
###< doctrine/doctrine-bundle ###

###> knplabs/knp-snappy-bundle ###
WKHTMLTOPDF_PATH=/usr/local/bin/wkhtmltopdf
WKHTMLTOIMAGE_PATH=/usr/local/bin/wkhtmltoimage
###< knplabs/knp-snappy-bundle ###
###> nelmio/cors-bundle ###
CORS_ALLOW_ORIGIN=^https?://(localhost|127\.0\.0\.1)(:[0-9]+)?$
###< nelmio/cors-bundle ###
###> friendsofsymfony/elastica-bundle ###
ELASTICSEARCH_URL=http://localhost:9200/
###< friendsofsymfony/elastica-bundle ###

###< friendsofsymfony/elastica-bundle ###

###> CUSTOM ###
DOMAIN=eduprat.fr

# Nom des sous domaines, afin de gérer deux urls (crm/extranet) pour le meme site
SUBDOMAIN_EXTRANET=extranet
SUBDOMAIN_CRM=crm

# Adresses email qui envoie les mails depuis l'extranet
ALIENOR_USER_CONTACT_SENDER="{\"<EMAIL>\":\"Eduprat Formations\"}"
EDUPRAT_EMAIL_SENDER="{\"<EMAIL>\":\"Eduprat Formations\"}"

# Adresses email auquelles sont envoyé certains mails en relation avec Actalians
EDUPRAT_ACTALIANS_RECIPIENT=

# Adresses email auquelles sont envoyé la notification lorsque les questionnaires sont complétés
EDUPRAT_CLOSEDANDCOMPLETED_RECIPIENT=

# Adresses email auquelles sont envoyé la notification d'ajout ou de suppression d'un formateur
EDUPRAT_FORMER_ADDED_DELETED_RECIPIENT=<EMAIL>

ROUTER_REQUEST_CONTEXT_HOST=
ROUTER_REQUEST_CONTEXT_SCHEME=https

# Informations liées à au stockage elastic
ALIENOR_ELASTICSEARCH_HOST=
ALIENOR_ELASTICSEARCH_PORT=
ALIENOR_ELASTICSEARCH_INDEX_PREFIX=
ALIENOR_ELASTICSEARCH_SCHEME=
ALIENOR_ELASTICSEARCH_USERNAME=
ALIENOR_ELASTICSEARCH_PASSWORD=
ALIENOR_ELASTIC_DOCTRINE_ENABLED_SYNCHRO=true

# URL du dashboard (Semble inutilisé)
DASHBOARD_URL=

# Date de mise à disposition des nouvelles évaluations
EVALUATION_MIGRATION_DATE='2018-01-01'

# Adresses email auquelles sont envoyés les commentaires E-tutorat et des Evaluations globales
EVALUATION_QUALITE_EMAIL="{\"<EMAIL>\":\"Eduprat Formations\"}"

# TO COMMENT
ALIENOR_ELASTICA_CUSTOM_CONFIG=

# Informations liées au site Elearning Eduprat FMC (Inutilisé depuis les évols elearning PARCOURS_ELEARNING_MIGRATION_DATE)
E360LEARNING_CID=
E360LEARNING_KEY=
E360LEARNING_API=
ELEARNING_URL=
ELEARNING_TOKEN=

# TO COMMENT
CARTO_FTP_HOST=
CARTO_FTP_LOGIN=
CARTO_FTP_PASS=

# TO COMMENT
ZEFID_ENRICHISSEMENT_HOST=null

# Serveur de la boitre mail
INSCRIPTIONS_HOST=
# Login de la boite mail
INSCRIPTIONS_EMAIL=
# Mot de passe de la boitre mail
INSCRIPTIONS_PASSWORD=
# Dossier dans lequel sont lu les mails d'inscriptions
INSCRIPTIONS_FOLDER=
# Adresses email auquelles sont envoyés les erreurs d'inscriptions
INSCRIPTIONS_ERROR_EMAIL=

# Date de mise à disposition de l'évolution des honoraires
HONORAIRES_MIGRATION_DATE='2019-01-01'

# TO COMMENT
JWT_KEY_PASS_PHRASE=
JWT_TOKEN_TTL=3600


# Clé d'api sendinblue et ID des listes
SIB_API_KEY=
SIB_PARTICIPANT_LIST_ID=
SIB_COORDINATEUR_LIST_ID=
SIB_CALL_API=true

# Onglets activés dans le CRM
CRM_SECTIONS="[\"rapport\", \"analysis\", \"analysis_participants\", \"suivi_commercial\"]"

# Clés pour l'api vitrine et l'api mobile
API_VITRINE_KEY=
API_KEY=

# URLS de téléchargement de l'appli mobile
APP_DOWNLOAD_GOOGLE=https://play.google.com/store/apps/details?id=fr.heurisko.eduprat
APP_DOWNLOAD_APPLE=https://apps.apple.com/us/app/eduprat-formations/id1501527019#?platform=iphone

# Date de mise à disposition du parcours v3
PARCOURS_V3_MIGRATION_DATE='2021-10-01'

# Date de mise à disposition du module Vidéo pré session et post session
PARCOURS_VXA_VIDEO_MIGRATION_DATE='2023-04-20'
APP_VERSION=v1

# ID du coordinateur par défaut pour les Leads MFM
LEAD_MFM_REFERENT_ID=

# ID du coordinateur par défaut pour l'ajout d'un lead Union des podologues
LEAD_PODOLOGUE_REFERENT_ID=

# URL de l'api de geocodage des adresses
API_ADRESS_GOUV_URL="https://api-adresse.data.gouv.fr/search"
API_ADRESS_ACTIVE=true

###< CUSTOM ###

###> CUSTOM DEV ###
IDE_CONFIG=
DEV_MAILER_RECIPIENT=
###< CUSTOM DEV ###

###> symfony/lock ###
# Choose one of the stores below
# postgresql+advisory://db_user:db_password@localhost/db_name
LOCK_DSN=flock
###< symfony/lock ###

# EDUPRAT_FORMER_ADDED_DELETED_RECIPIENT1=
# EDUPRAT_FORMER_ADDED_DELETED_RECIPIENT2=
# PARCOURS_ELEARNING_MIGRATION_DATE=
# DEV_EMAIL=
PARCOURS_ELEARNING_MIGRATION_DATE='2022-09-19'

EMAIL_ERROR_ALERT=<EMAIL>,<EMAIL>,<EMAIL>,<EMAIL>
EMAIL_ERROR_ALERT_LIMIT=<EMAIL>,<EMAIL>,<EMAIL>,<EMAIL>

IS_PLAYWRIGHT_ENV=false

EDUPRAT_COORDINATEUR_PERSON_EL_ID=407

EDUPRAT_GOTENBERG_URL="http://gotenberg:3000"
MODULE_PROGRESSION_UPDATE_DATE='2024-04-18'
