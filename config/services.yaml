# This file is the entry point to configure your own services.
# Files in the packages/ subdirectory configure your dependencies.

# Put parameters here that don't need to change on each machine where the app is deployed
# https://symfony.com/doc/current/best_practices/configuration.html#application-related-configuration
parameters:
    locale: fr
    tva: 20
    domain: '%env(DOMAIN)%'
    subdomain_extranet: '%env(SUBDOMAIN_EXTRANET)%'
    subdomain_crm: '%env(SUBDOMAIN_CRM)%'
    router.request_context.host: '%env(ROUTER_REQUEST_CONTEXT_HOST)%'
    router.request_context.scheme: '%env(ROUTER_REQUEST_CONTEXT_SCHEME)%'
    router.request_context.base_url: ''
    asset.request_context.base_path: '%router.request_context.base_url%'
    asset.request_context.secure: true
    alienor_user_manager.class: Eduprat\AdminBundle\Services\UserManager
    alienor_user.templatesLocation: admin
    alienor_user.templatesLocationCrm: crm
    alienor_registerForm: \Eduprat\AdminBundle\Form\PersonType
    formation.upload_destination: '%kernel.project_dir%/public/uploads/plaquettes/'
    formation.uri_prefix: /public/uploads/plaquettes
    person.cv.upload_destination: '%kernel.project_dir%/uploads/cv/'
    person.cv.uri_prefix: /uploads/cv
    person.dli.upload_destination: '%kernel.project_dir%/uploads/dli/'
    person.dli.uri_prefix: /uploads/dli
    person.avatar.upload_destination: '%kernel.project_dir%/public/uploads/person/avatar/'
    person.avatar.uri_prefix: /uploads/person/avatar
    programme.picture.upload_destination: '%kernel.project_dir%/public/uploads/programme/picture/'
    programme.picture.uri_prefix: /uploads/programme/picture
    programme.firstAdditionalInfosPicture.upload_destination: '%kernel.project_dir%/public/uploads/programme/picture/'
    programme.firstAdditionalInfosPicture.uri_prefix: /uploads/programme/picture
    programme.secondAdditionalInfosPicture.upload_destination: '%kernel.project_dir%/public/uploads/programme/picture/'
    programme.secondAdditionalInfosPicture.uri_prefix: /uploads/programme/picture
    priseEnChargePicture.upload_destination: '%kernel.project_dir%/public/uploads/priseEnCharge/priseEnChargePicture/'
    priseEnChargePicture.uri_prefix: /uploads/priseEnCharge/priseEnChargePicture
    formation.emargement.upload_destination: '%kernel.project_dir%/uploads/formations/emargement'
    formation.emargement.uri_prefix: /uploads/formations/emargement
    formation.emargements.upload_destination: '%kernel.project_dir%/uploads/formations/emargement'
    formation.emargements.uri_prefix: /uploads/formations/emargement
    formation.conventions.upload_destination: '%kernel.project_dir%/uploads/formations/convention/'
    formation.conventions.uri_prefix: /uploads/formations/convention
    formation.facture_former.upload_destination: '%kernel.project_dir%/uploads/formations/factureFormer/'
    formation.facture_former.uri_prefix: /uploads/formations/factureFormer
    formation.facture_coordinator.upload_destination: '%kernel.project_dir%/uploads/formations/factureCoordinator/'
    formation.facture_coordinator.uri_prefix: /uploads/formations/factureCoordinator
    formation.facture_restauration.upload_destination: '%kernel.project_dir%/uploads/formations/factureRestauration/'
    formation.facture_restauration.uri_prefix: /uploads/formations
    formation.justificatif_suivi.upload_destination: '%kernel.project_dir%/uploads/formations/justificatifSuivi/'
    formation.justificatif_suivi.uri_prefix: /uploads/formations
    formation.contrat_formateur.upload_destination: '%kernel.project_dir%/uploads/formations/contratFormateur/'
    formation.contrat_formateur.uri_prefix: /uploads/formations
    formation.models.upload_destination: '%kernel.project_dir%/uploads/formations/models/'
    formation.powerpoints.upload_destination: '%kernel.project_dir%/uploads/formations/powerpoints/'
    formateur.contrat.upload_destination: '%kernel.project_dir%/uploads/formations/formateurs/contrat'
    formateur.contrat.uri_prefix: /uploads/formations/formateurs/contrat
    formateur.facture.upload_destination: '%kernel.project_dir%/uploads/formations/formateurs/facture/'
    formateur.facture.uri_prefix: /uploads/formations/formateurs/facture
    coordinator.factureCoordinator.upload_destination: '%kernel.project_dir%/uploads/formations/coordinators/facture'
    coordinator.factureCoordinator.uri_prefix: /uploads/formations/coordinators/facture
    topo.upload_destination: '%kernel.project_dir%/uploads/formations/topo'
    topo.uri_prefix: /uploads/formations/topo
    participantActionSheet.upload_destination: '%kernel.project_dir%/uploads/participant/participantActionSheet'
    participantActionSheet.uri_prefix: '/uploads/participant/participantActionSheet'
    participation.attestationHonneur.upload_destination: '%kernel.project_dir%/uploads/participation/attestationHonneur/'
    participation.attestationHonneur.uri_prefix: /uploads/participation/attestationHonneur
    activity.pdf.upload_destination: '%kernel.project_dir%/uploads/elearning/pdf/'
    activity.pdf.uri_prefix: /uploads/elearning/pdf
    activity.powerpoint.upload_destination: '%kernel.project_dir%/uploads/elearning/powerpoint/'
    activity.powerpoint.uri_prefix: /uploads/elearning/powerpoint
    activity.picture.upload_destination: '%kernel.project_dir%/public/uploads/elearning/picture/'
    activity.picture.uri_prefix: /uploads/elearning/picture
    activity.pictureText.upload_destination: '%kernel.project_dir%/public/uploads/elearning/picture/'
    activity.pictureText.uri_prefix: /uploads/elearning/picture
    activity.pictureQuestion.upload_destination: '%kernel.project_dir%/public/uploads/elearning/pictureQuestion/'
    audit.pdf.upload_destination: '%kernel.project_dir%/uploads/audit/pdf/'
    audit.pdf.uri_prefix: /uploads/audit/pdf
    activity.pictureQuestion.uri_prefix: /uploads/elearning/pictureQuestion
    survey_choice.picture.upload_destination: '%kernel.project_dir%/public/uploads/surveyChoicePicture/'
    survey_choice.picture.uri_prefix: /uploads/surveyChoicePicture
    question_tcs.picture.upload_destination: '%kernel.project_dir%/public/uploads/questionTCSPicture/'
    question_tcs.picture.uri_prefix: /uploads/questionTCSPicture
    dashboard.config_path: '%kernel.project_dir%/config/eduprat/dashboard.json'
    jwt_private_key_path: '%kernel.project_dir%/var/jwt/private.pem'
    jwt_public_key_path: '%kernel.project_dir%/var/jwt/public.pem'
    jwt_token_ttl: 3600000
    alienor.elasticsearch.host: "%env(ALIENOR_ELASTICSEARCH_HOST)%"
    alienor.elasticsearch.port: "%env(ALIENOR_ELASTICSEARCH_PORT)%"
    alienor.elasticsearch.index_prefix: "%env(ALIENOR_ELASTICSEARCH_INDEX_PREFIX)%"
    alienor.elasticsearch.scheme: "%env(ALIENOR_ELASTICSEARCH_SCHEME)%"
    alienor.elasticsearch.username: "%env(ALIENOR_ELASTICSEARCH_USERNAME)%"
    alienor.elasticsearch.password: "%env(ALIENOR_ELASTICSEARCH_PASSWORD)%"
    allParameters: ~
    inscriptions.host: "%env(INSCRIPTIONS_HOST)%"
    inscriptions.email: "%env(INSCRIPTIONS_EMAIL)%"
    inscriptions.password: "%env(INSCRIPTIONS_PASSWORD)%"
    inscriptions.folder: "%env(INSCRIPTIONS_FOLDER)%"
    inscriptions.error_email: "%env(json:INSCRIPTIONS_ERROR_EMAIL)%"
    inscription_client_id: "%env(OFFICE_CLIENT_ID)%"
    inscription_client_secret: "%env(OFFICE_CLIENT_SECRET)%"
    inscription_client_tenant: "%env(OFFICE_TENANT)%"
    inscription_client_refresh_token: "%env(OFFICE_REFRESH_TOKEN)%"
    inscription_client_redirect_uri: "%env(OFFICE_REDIRECT_URI)%"
    honoraires.migration_date: "%env(HONORAIRES_MIGRATION_DATE)%"
    alienor_user.contact_sender: "%env(json:ALIENOR_USER_CONTACT_SENDER)%"
    eduprat.email.sender: "%env(json:EDUPRAT_EMAIL_SENDER)%"
    eduprat.actalians.recipient: "%env(EDUPRAT_ACTALIANS_RECIPIENT)%"
    eduprat.closedAndCompleted.recipient: "%env(EDUPRAT_CLOSEDANDCOMPLETED_RECIPIENT)%"
    eduprat.formerAddedDeleted.recipient: "%env(EDUPRAT_FORMER_ADDED_DELETED_RECIPIENT)%"
    login_help_path: '/uploads/Guide de connexion.pdf'
    zefid.enrichissement.host: null
    eduprat.password_expiration_interval: "P6M"
    api_vitrine_key: "%env(API_VITRINE_KEY)%"
    api_key: "%env(API_KEY)%"
    app.download_apple: "%env(APP_DOWNLOAD_APPLE)%"
    app.download_google: "%env(APP_DOWNLOAD_GOOGLE)%"
    parcours_v3.migration_date: "%env(PARCOURS_V3_MIGRATION_DATE)%"
    parcours_elearning.migration_date: "%env(PARCOURS_ELEARNING_MIGRATION_DATE)%"
    parcours_vxa_video.migration_date: "%env(PARCOURS_VXA_VIDEO_MIGRATION_DATE)%"
    email_error_alert: "%env(EMAIL_ERROR_ALERT)%"
    sib_call_api: "%env(bool:SIB_CALL_API)%"
    playwrightEnv: "%env(bool:IS_PLAYWRIGHT_ENV)%"
    doctrine_enabled_synchro: "%env(bool:ALIENOR_ELASTIC_DOCTRINE_ENABLED_SYNCHRO)%"
    EDUPRAT_COORDINATEUR_PERSON_EL_ID: "%env(int:EDUPRAT_COORDINATEUR_PERSON_EL_ID)%"
    module_progression_update_date: "%env(MODULE_PROGRESSION_UPDATE_DATE)%"
    EDUPRAT_GOTENBERG_URL: "%env(EDUPRAT_GOTENBERG_URL)%"

services:
    # default configuration for services in *this* file
    _defaults:
        autowire: true      # Automatically injects dependencies in your services.
        autoconfigure: true # Automatically registers your services as commands, event subscribers, etc.
        bind:
            $projectDir: "%kernel.project_dir%"
            $dashboardUrl: "%env(DASHBOARD_URL)%"
            $configPath: "%dashboard.config_path%"
            $evaluationMigrationDate: "%env(EVALUATION_MIGRATION_DATE)%"
            $parcoursV3MigrationDate: "%parcours_v3.migration_date%"
            $parcoursElearningMigrationDate: "%parcours_elearning.migration_date%"
            $parcoursVignetteAuditVideoAndToolBoxMigrationDate: "%parcours_vxa_video.migration_date%"
            $pptFileDir: "%formation.powerpoints.upload_destination%"
            $elasticSerializer: "@alienor.elastic.serializer"
            $uploaderHelper: "@Vich\\UploaderBundle\\Templating\\Helper\\UploaderHelper"
            $migrationDate: "%honoraires.migration_date%"
            $packages: "@assets.packages"
            $elearningUrl: "%env(ELEARNING_URL)%"
            $elearningToken: "%env(ELEARNING_TOKEN)%"
            $tva: "%tva%"
            $contactSender: "%alienor_user.contact_sender%"
            $qualiteEmail: "%env(json:EVALUATION_QUALITE_EMAIL)%"
            $actaliansRecipient: "%eduprat.actalians.recipient%"
            $closedAndCompletedRecipient: "%eduprat.closedAndCompleted.recipient%"
            $edupratFormerAddedDeletedRecipient: "%eduprat.formerAddedDeleted.recipient%"
            $recipient: "%eduprat.actalians.recipient%"
            $senderEmail: "%eduprat.email.sender%"
            $profiler: "@?profiler"
            $passwordExpirationInterval: "%eduprat.password_expiration_interval%"
            $ugaFile: "%kernel.project_dir%/var/UGA.json"
            $ugas: "%crm.uga%"
            $leadMfmReferentId: "%env(LEAD_MFM_REFERENT_ID)%"
            $leadPodologueReferentId: "%env(LEAD_PODOLOGUE_REFERENT_ID)%"
            $moduleProgressionUpdateDate: "%module_progression_update_date%"

    # makes classes in src/ available to be used as services
    # this creates a service per class whose id is the fully-qualified class name
    #    App\:
    #        resource: '../src/*'
    #        exclude: '../src/{DependencyInjection,Entity,Migrations,Tests,Kernel.php}'

    # controllers are imported separately to make sure services can be injected
    # as action arguments even if you don't extend any base controller class
    #    App\Controller\:
    #        resource: '../src/Controller'
    #        tags: ['controller.service_arguments']
    #

    Eduprat\:
        resource: '../src/Eduprat/*'
        exclude: '../src/Eduprat/*/{DependencyInjection,Entity,Migrations,Tests,Kernel.php}'

    Eduprat\DomainBundle\Controller\:
        resource: '../src/Eduprat/DomainBundle/Controller'
        tags: ['controller.service_arguments']

    Eduprat\AdminBundle\Controller\:
        resource: '../src/Eduprat/AdminBundle/Controller'
        tags: ['controller.service_arguments']

    Eduprat\CrmBundle\Controller\:
        resource: '../src/Eduprat/CrmBundle/Controller'
        tags: ['controller.service_arguments']

    Eduprat\ApiBundle\Controller\:
        resource: '../src/Eduprat/ApiBundle/Controller'
        tags: ['controller.service_arguments']

    Eduprat\AuditBundle\Controller\:
        resource: '../src/Eduprat/AuditBundle/Controller'
        tags: ['controller.service_arguments']

    # add more service definitions when explicit configuration is needed
    # please note that last definitions always *replace* previous ones

    Eduprat\DomainBundle\Services\Normalizer\:
        resource: '../src/Eduprat/DomainBundle/Services/Normalizer/'
        tags:
            - { name: 'alienor.elastic.normalizer' }

    Eduprat\DomainBundle\Services\Ftp:
        arguments:
            $host: "%env(CARTO_FTP_HOST)%"
            $username: "%env(CARTO_FTP_LOGIN)%"
            $password: "%env(CARTO_FTP_PASS)%"

    Eduprat\DomainBundle\Services\AdressesService:
        arguments:
            $urlWSAdresse: "%env(API_ADRESS_GOUV_URL)%"
            $geocodageActive: "%env(bool:API_ADRESS_ACTIVE)%"

    Eduprat\AdminBundle\Services\UserManager:
        arguments:
            $formClass: Eduprat\AdminBundle\Form\PersonType

    Eduprat\DomainBundle\Services\SendinBlueManager:
        arguments:
            $apiKey: '%env(SIB_API_KEY)%'
            $participantListId: '%env(SIB_PARTICIPANT_LIST_ID)%'
            $coordinateurListId: '%env(SIB_COORDINATEUR_LIST_ID)%'

    Eduprat\ApiBundle\Services\Serializer:
        public: true
        arguments: [['@alienor.normalizer.datetime', '@alienor.normalizer.collection', '@alienor.normalizer.error', '@alienor.normalizer.object', '@serializer.normalizer.setget'], ['@serializer.encoder.json']]

    alienor.elastic.serializer: '@Eduprat\ApiBundle\Services\Serializer'

    Alienor\ElasticBundle\Services\Serializer:
      alias: Eduprat\ApiBundle\Services\Serializer
      public: true

    Eduprat\DomainBundle\Services\Populator\EvaluationPopulator:
        tags:
            - { name: alienor.elastic.populator }

    Eduprat\DomainBundle\Services\Populator\UGAPopulator:
        tags:
            - { name: alienor.elastic.populator }

    Eduprat\DomainBundle\Services\Populator\CoordinatorPopulator:
        tags:
            - { name: alienor.elastic.populator }

    Eduprat\DomainBundle\Services\Populator\FormationPopulator:
        tags:
            - { name: alienor.elastic.populator }

    Eduprat\DomainBundle\Services\AutomaticEmailSender:
        arguments: [!tagged_iterator alienor.email.checker, "@mailer", '@Twig\Environment']

    alienor.email.sender:
        public: true
        alias: Eduprat\DomainBundle\Services\AutomaticEmailSender

    ### EventListeners
    Eduprat\AuditBundle\EventListener\EvaluationListener:
        arguments: ['@Eduprat\DomainBundle\Services\EmailSender']
        tags:
          - { name: doctrine.event_listener, event: postPersist, lazy: true }

#    Eduprat\AdminBundle\Listener\UploadListener:
#        public: true
#        tags:
#            - { name: kernel.event_listener, event: vich_uploader.pre_remove, method: onVichUploaderPreRemove }

    Eduprat\DomainBundle\EventListener\VichUploaderListener:
        tags:
            - { name: kernel.event_listener, event: vich_uploader.post_upload }

    Eduprat\AdminBundle\Command\GenerateCSVParticipantMissingAttestationsCommand:
        arguments:
            $parcoursV3MigrationDate: '%env(PARCOURS_V3_MIGRATION_DATE)%'

    scheb_two_factor.security.email.default_code_generator:
        class: Eduprat\AdminBundle\Security\TwoFactor\Provider\Email\Generator\CodeGenerator
        lazy: true
        arguments:
            - '@scheb_two_factor.persister'
            - '@scheb_two_factor.security.email.auth_code_mailer'
            - '%scheb_two_factor.email.digits%'

    scheb_two_factor.security.email.provider:
        class: Eduprat\AdminBundle\Security\TwoFactor\Provider\Email\EmailTwoFactorProvider
        tags:
            - { name: 'scheb_two_factor.provider', alias: 'email' }
        arguments:
            - '@scheb_two_factor.security.email.code_generator'
            - '@scheb_two_factor.security.email.form_renderer'
