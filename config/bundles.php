<?php

use Symfony\Bundle\FrameworkBundle\FrameworkBundle;
use Symfony\Bundle\SecurityBundle\SecurityBundle;
use Symfony\Bundle\MonologBundle\MonologBundle;
use Symfony\Bundle\TwigBundle\TwigBundle;
use Twig\Extra\TwigExtraBundle\TwigExtraBundle;
use Doctrine\Bundle\DoctrineBundle\DoctrineBundle;
use Doctrine\Bundle\MigrationsBundle\DoctrineMigrationsBundle;
use Doctrine\Bundle\FixturesBundle\DoctrineFixturesBundle;
use Symfony\Bundle\MakerBundle\MakerBundle;
use Symfony\Bundle\WebProfilerBundle\WebProfilerBundle;
use Knp\Bundle\SnappyBundle\KnpSnappyBundle;
use Nelmio\CorsBundle\NelmioCorsBundle;
use Stfalcon\Bundle\TinymceBundle\StfalconTinymceBundle;
use Vich\UploaderBundle\VichUploaderBundle;
use FOS\ElasticaBundle\FOSElasticaBundle;
use Alienor\ElasticBundle\AlienorElasticBundle;
use Alienor\EmailBundle\AlienorEmailBundle;
use Alienor\FormBundle\AlienorFormBundle;
use Alienor\ApiBundle\AlienorApiBundle;
use Eduprat\DomainBundle\EdupratDomainBundle;
use Eduprat\AdminBundle\EdupratAdminBundle;
use Eduprat\CrmBundle\EdupratCrmBundle;
use Eduprat\AuditBundle\EdupratAuditBundle;
use Eduprat\ApiBundle\EdupratApiBundle;
use Eduprat\PdfBundle\EdupratPdfBundle;
use Symfony\WebpackEncoreBundle\WebpackEncoreBundle;
use Symfony\UX\Chartjs\ChartjsBundle;
use Nelmio\SecurityBundle\NelmioSecurityBundle;
use Zenstruck\Foundry\ZenstruckFoundryBundle;
use DAMA\DoctrineTestBundle\DAMADoctrineTestBundle;
use Alienor\SymfonyProfilerExtraGit\SymfonyProfilerExtraGit;
use Scheb\TwoFactorBundle\SchebTwoFactorBundle;
use Symfony\UX\StimulusBundle\StimulusBundle;
use Alienor\UserBundle\AlienorUserBundle;

return [
    FrameworkBundle::class => ['all' => true],
    SecurityBundle::class => ['all' => true],
    MonologBundle::class => ['all' => true],
    TwigBundle::class => ['all' => true],
    TwigExtraBundle::class => ['all' => true],
    DoctrineBundle::class => ['all' => true],
    DoctrineMigrationsBundle::class => ['all' => true],
    DoctrineFixturesBundle::class => ['dev' => true, 'test' => true],
    MakerBundle::class => ['dev' => true],
    WebProfilerBundle::class => ['dev' => true, 'test' => true],
    KnpSnappyBundle::class => ['all' => true],
    NelmioCorsBundle::class => ['all' => true],
    StfalconTinymceBundle::class => ['all' => true],
    VichUploaderBundle::class => ['all' => true],
    FOSElasticaBundle::class => ['all' => true],
    AlienorElasticBundle::class => ['all' => true],
    AlienorEmailBundle::class => ['all' => true],
    AlienorFormBundle::class => ['all' => true],
    AlienorApiBundle::class => ['all' => true],
    EdupratDomainBundle::class => ['all' => true],
    EdupratAdminBundle::class => ['all' => true],
    EdupratCrmBundle::class => ['all' => true],
    EdupratAuditBundle::class => ['all' => true],
    EdupratApiBundle::class => ['all' => true],
    EdupratPdfBundle::class => ['all' => true],
    WebpackEncoreBundle::class => ['all' => true],
    ChartjsBundle::class => ['all' => true],
    NelmioSecurityBundle::class => ['all' => true],
    ZenstruckFoundryBundle::class => ['dev' => true, 'test' => true],
    DAMADoctrineTestBundle::class => ['test' => true],
    SymfonyProfilerExtraGit::class => ['dev' => true],
    SchebTwoFactorBundle::class => ['all' => true],
    StimulusBundle::class => ['all' => true],
    AlienorUserBundle::class => ['all' => true],
];
