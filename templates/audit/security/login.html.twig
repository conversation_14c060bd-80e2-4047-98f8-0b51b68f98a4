{% extends 'admin/base_login.html.twig' %}

{% block body %}
    <p class="login-box-msg">Connexion</p>
    <div id="error-block" class="callout callout-danger {% if error is empty %}hide{% endif %}">
        <p>{% if error %}{{ error.message|trans({}, 'validators') }}{% endif %}</p>
    </div>
    <form class="login-form" action="{{ path('eduprat_audit_login_check') }}" method="post">
        <div class="form-group has-feedback">
            <input id="username" name="_username" class="form-control" placeholder="{{ "login.rpps_adeli"|trans }}" value="{{ last_username }}">
        </div>
        <div class="form-group has-feedback">
            <input id="password" name="_password" type="password" class="form-control" placeholder="{{ "login.password"|trans }}">
            <i class="togglePassword fa fa-eye"></i>
        </div>
        <div class="row">
            <div class="col-lg-6">
                <a href="{{ url('admin_user_reset',  { participant: "true" }) }}">Mot de passe oublié <br>ou création de mot de passe</a>
            </div>
            <div class="col-xs-4 pull-right">
                <button type="submit" class="btn btn-primary btn-block btn-flat">{{ "signin"|trans }}</button>
            </div>
        </div>
        <input type="hidden" name="_csrf_token" value="{{ csrf_token('authenticate') }}">
        <div style="margin-top: 20px;" class="text-center">
            <a class="btn btn-eduprat" target="_blank" href="{{ asset(login_help_path) }}"> <i class="fa fa-download"></i> Guide de connexion</a>
        </div>
    </form>
{% endblock body %}