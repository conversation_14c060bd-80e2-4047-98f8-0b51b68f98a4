<script>
	jQuery(document).ready(function() {
		var form = $('.login-form');
		var submit_btn = $('button[type=submit]');
		var username = $('input[name=_username]');
		var password = $('input[name=_password]');
		var token = $('input[name=_csrf_token]');
		var error_block = $('#error-block');

		if (form.length) {
			$(window).keydown(function(event){
				if(event.keyCode === 13) {
					submit_btn.click();
					event.preventDefault();
					return false;
				}
			});

			submit_btn.click(function(e) {
				e.preventDefault();
				var error = false;

				var user_group = username.closest('.form-group');
				user_group.removeClass('has-error');
				user_group.find('#username-error').remove();

				var pass_group = password.closest('.form-group');
				pass_group.removeClass('has-error');
				pass_group.find('#password-error').remove();

				if ( $.trim(username.val()) === "" ) {
					user_group.addClass('has-error');
					user_group.append('<span id="username-error" class="help-block">Ce champ est obligatoire</span>');
					error = true;
				}

				if ( $.trim(password.val()) === "" ) {
					pass_group.addClass('has-error');
					pass_group.append('<span id="password-error" class="help-block">Ce champ est obligatoire</span>');
					error = true;
				}

				if (!error) {
					error_block.addClass('hide');
					var url = $('.login-form').attr('action');
					$.ajax({
						type        : 'POST',
						url         : url,
						data        : form.serialize(),
						dataType    : "json",
						success     : function(data, status, object) {
							token.val(data.token);
							error_block.removeClass('hide').addClass('callout-success').removeClass('callout-danger');
							error_block.find('p').text("Connexion en cours ...");
							window.location = data.redirect;
						},
						error: function(data, status, object){
							error_block.removeClass('hide');
							error_block.find('p').text(data.responseJSON.message);
                            if (data.status === 429) {
                                form.remove();
                            }
						}

					});

				} else {
					error_block.removeClass('hide');
					error_block.find('p').text("Formulaire invalide");
				}
			});

			const togglePassword = document.querySelector('.togglePassword');
			const passwordInput = document.querySelector('#password');

			togglePassword.addEventListener('click', function (e) {
				// toggle the type attribute
				const type = passwordInput.getAttribute('type') === 'password' ? 'text' : 'password';
				passwordInput.setAttribute('type', type);
				// toggle the eye slash icon
				this.classList.toggle('fa-eye-slash');
			});
        }

	});
</script>
