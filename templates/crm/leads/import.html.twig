{% extends 'crm/base.html.twig' %}

{% block title %}Ajout de leads - {{ partner }}{% endblock %}

{% block body %}
    <div class="box box-solid box-primary">
        <div class="box-body">
            <a href="{{asset('uploads/Trame-nom-colonne.csv')}}">Trame du fichier</a>
            <div>
                {{ form_start(form, { attr: { class: "mbn form-inline"}}) }}
                {{ form_rest(form) }}
                <button class="mll btn btn-eduprat">Valider</button>
                {{ form_end(form) }}
            </div>
        </div>
    </div>

    {% if preview %}
        {{ form_start(formSubmit) }}
        <div class="box box-solid box-primary">
            <div class="box-header">
                <div class="box-title">Leads correspondants à des PS identifiés</div>
            </div>
            <div class="box-body">
                {% if preview["updated"] %}
                    {% for index, item in preview["updated"] %}
                        {% set leadAdvisor = false %}
                        {% for advisor in advisors %}
                            {% if item.data.advisor and item.data.advisor.id == advisor.id %}
                                {% set leadAdvisor = advisor %}
                            {% endif %}
                        {% endfor %}
                        <div class="box box-solid box-primary mbl">
                            
                            <div>
                                <div class="table-responsive">
                                        <table class="table table-lead table-lead-right">
                                            <thead class="back-lead">
                                            <tr class="tr-lead">
                                                <th width="6%"></th>
                                                <th>RPPS</th>
                                                <th>Nom</th>
                                                <th>Prénom</th>
                                                <th>Profession</th>
                                                <th>Spécialité</th>
                                                <th>UGA</th>
                                                <th>Coordinateur</th>
                                                <th>Code apporteur</th>
                                                <th>Adherent</th>
                                                <th width="8%"></th>
                                            </tr>
                                            </thead>
                                            <tbody class="back-lead">
                                            <tr>
                                                <td class="table-lead-left">Lead :</td>
                                                <td>{{ item.data.rpps }}</td>
                                                <td>{{ item.data.lastname }}</td>
                                                <td>{{ item.data.firstname }}</td>
                                                <td>
                                                    <select required disabled class="form-control form-referent-create" name="lead[{{ index }}][category]">
                                                        <option value="">Aucun</option>
                                                        {% for category in categories %}
                                                            <option value="{{ category }}" {% if item.data.category == category %}selected="selected"{% endif %}>{{ category }}</option>
                                                        {% endfor %}
                                                    </select>
                                                </td>
                                                <td>
                                                    <select required disabled class="form-control form-referent-create" name="lead[{{ index }}][speciality]">
                                                        <option value="">Aucun</option>
                                                        {% for speciality in specialities %}
                                                            <option value="{{ speciality }}" {% if item.data.speciality == speciality %}selected="selected"{% endif %}>{{ speciality }}</option>
                                                        {% endfor %}
                                                    </select>
                                                </td>
                                                <td>{{ item.data.uga }}</td>
                                                <td>
                                                    <select disabled class="form-control form-referent-create" name="lead[{{ index }}][leadReferent]">
                                                        <option value="">Aucun</option>
                                                        {% for coordinator in coordinators %}
                                                            <option value="{{ coordinator.id }}" {% if item.data.leadReferent and item.data.leadReferent.id == coordinator.id %}selected="selected"{% endif %}>{{ coordinator.fullname }}</option>
                                                        {% endfor %}
                                                    </select>
                                                </td>
                                                <td>
                                                    {{ item.data.codeApporteur }}
                                                    {% if leadAdvisor and leadAdvisor.codeApporteur != item.data.codeApporteur %} 
                                                        <i 
                                                            style="color:red; margin-left:2px" 
                                                            class="fa fa-exclamation-triangle" 
                                                            aria-hidden="true"
                                                            title="{% if codeApporteurList[item.data.codeApporteur] is defined %} Ce code apporteur est déjà associé au conseillé {{ codeApporteurList[item.data.codeApporteur].fullname }} {% else %} Le code apporteur inséré ne correspond pas au code apporteur du conseillé associé par défaut{% endif %}">
                                                        </i> 
                                                    {% endif %}
                                                </td>
                                                <td>{{ item.data.gpmMemberNumber }}</td>
                                                <td><a href="#" class="evalListLink action-btn lead-details">Voir détails <i class="fa fa-angle-right arrow-details"></i></a></td>
                                            </tr>
                                            <tr class="sessionsWrap tr-wrap-lead">
                                                <td colspan="10" style="border-top: none;padding: inherit;">
                                                    <div class="evalList">
                                                        <div class="">
                                                            <table class="table table-new-lead" style="margin-bottom:0%">
                                                                <tr>
                                                                    <th>Conseiller</th>
                                                                    <th>A recontacter le</th>
                                                                    <th>Commentaire Lead</th>
                                                                </tr>
                                                                <td>
                                                                    <select disabled class="form-control" name="lead[{{ index }}][advisor]">
                                                                        <option value="">Aucun</option>
                                                                        {% for advisor in advisors %}
                                                                            <option value="{{ advisor.id }}" {% if item.data.advisor and item.data.advisor.id == advisor.id %}selected="selected"{% endif %}>{{ advisor.lastname }} {{ advisor.firstname }}  - {{ advisor.codeApporteur }}</option>
                                                                        {% endfor %}
                                                                    </select>
                                                                </td>
                                                                <td>
                                                                    <input disabled type="text" class="form-control" name="lead[{{ index }}][leadContactDate]" value="{{ item.data.leadContactDate }}">
                                                                </td>
                                                                <td>
                                                                    <textarea disabled class="form-control" name="lead[{{ index }}][leadComment]" cols="30" rows="5" style="resize: vertical; height: 34px; min-height: 34px;">{{ item.data.leadComment }}</textarea>
                                                                </td>
                                                            </table>
                                                        </div>
                                                    </div>
                                                </td>
                                            </tr>
                                            </tbody>
                                        </table>
                                    <hr class="hr-lead">
                                    <table class="table table-striped table-lead">
                                        <thead>
                                        <tr>
                                            {% if not item.new %}
                                                <th style="font-size: 16px;">PS identifiés :</th>
                                            {% endif %}
                                            <th>RPPS</th>
                                            <th>Nom</th>
                                            <th>Prénom</th>
                                            <th>Profession</th>
                                            <th>Spécialité</th>
                                            <th>UGA</th>
                                            <th>Coordinateur</th>
                                            <td>Code apporteur</td>
                                        </tr>
                                        </thead>
                                        <tbody>
                                        {% for indexPs,participant in item.participants %}
                                            <tr {% if participant.isProspect %}class="table-row-participant-prospect"{% endif %}>
                                                {% if not item.new %}
                                                    <td>
                                                        <div style="display: flex; gap: 5px">
                                                            <input data-ref="{{ participant.id }}-{{ indexPs }}" type="radio" class="radio-lead radio-lead-update" id="lead_{{ index }}_choice_{{ participant.id }}" required name="lead[{{ index }}][import]" value="{{ participant.id }}">
                                                            <label for="lead_{{ index }}_choice_{{ participant.id }}">Importer</label>
                                                        </div>
                                                    </td>
                                                {% endif %}
                                                <td>
                                                    {{ participant.rpps }}
                                                    {% if item.data.rpps != participant.rpps %}
                                                        {% if not participant.rpps %}
                                                            {% if rppsList[item.data.rpps] is defined %} 
                                                                 <i 
                                                                    style="color:red; margin-left:2px" 
                                                                    class="fa fa-exclamation-triangle" 
                                                                    aria-hidden="true"
                                                                    title="Le code RPPS {{ item.data.rpps }} est déjà associé au participant {{ rppsList[item.data.rpps].fullname }}. Ce code ne sera donc pas importé dans ce participant.">
                                                                </i>
                                                            {% else %}
                                                                {{ item.data.rpps }}
                                                            {% endif %}
                                                        {% else %}
                                                            <i 
                                                                style="color:red; margin-left:2px" 
                                                                class="fa fa-exclamation-triangle" 
                                                                aria-hidden="true"
                                                                title="Le code RPPS {{ item.data.rpps }} ne sera pas associé au participant car il possède déjà un code RPPS.">
                                                            </i>
                                                        {% endif %}
                                                    {% endif %}
                                                </td>
                                                <td>
                                                    <a target="_blank" href="{{ participant.id ? url("admin_participant_show", { id: participant.id }) : "#" }}">
                                                        {{ participant.lastname }}
                                                    </a>
                                                </td>
                                                <td>{{ participant.firstname }}</td>
                                                <td>{{ participant.category }}</td>
                                                <td>{{ participant.speciality }}</td>
                                                <td>{{ participant.uga }}</td>
                                                <td>
                                                    <select data-refLead="{{ participant.id }}-{{ indexPs }}" disabled class="form-control form-referent-update" name="lead[{{ index }}][leadReferent]">
                                                        <option value="">Aucun</option>
                                                        {% for coordinator in coordinators %}
                                                            <option value="{{ coordinator.id }}" {% if participant.leadReferent and participant.leadReferent.id == coordinator.id %}selected="selected"{% endif %}>{{ coordinator.fullname }}</option>
                                                        {% endfor %}
                                                    </select>
                                                <td>
                                                    {{ participant.codeApporteur }}
                                                    {% if leadAdvisor and leadAdvisor.codeApporteur != participant.codeApporteur %} 
                                                       <i 
                                                            style="color:red; margin-left:2px" 
                                                            class="fa fa-exclamation-triangle" 
                                                            aria-hidden="true"
                                                            title="{% if codeApporteurList[participant.codeApporteur] is defined %} Ce code apporteur est déjà associé au conseillé {{ codeApporteurList[participant.codeApporteur].fullname }} {% else %} Le code apporteur inséré ne correspond pas au coordinateur associé par défaut{% endif %}">
                                                        </i> 
                                                    {% endif %}
                                                </td>
                                            </tr>
                                        {% endfor %}
                                        </tbody>
                                    </table>
                                </div>
                                <span class="mlm ml-radio-lead">
                                    <input type="radio" class="radio-lead radio-lead-disable" required id="lead_{{ index }}_noimport" name="lead[{{ index }}][import]" value="no_import">
                                    <label for="lead_{{ index }}_noimport">Ne pas importer ce lead</label>
                                </span><br>
                                <span class="mlm ml-radio-lead">
                                    <input type="radio" class="radio-lead radio-lead-create" required id="lead_{{ index }}_new" name="lead[{{ index }}][import]" value="">
                                    <label for="lead_{{ index }}_new">Créer un nouveau professionnel de santé</label>
                                    {% if rppsList[item.data.rpps] is defined %} 
                                            <i 
                                            style="color:red; margin-left:2px" 
                                            class="fa fa-exclamation-triangle" 
                                            aria-hidden="true"
                                            title="Le code RPPS {{ item.data.rpps }} est déjà associé au participant {{ rppsList[item.data.rpps].fullname }}. Ce code ne sera donc pas importé à la création de ce ps.">
                                        </i>
                                    {% endif %}
                                </span>
                            </div>
                        </div>
                    {% endfor %}
                {% endif %}
            </div>
         </div>

        {% if preview["created"] %}
            <div class="box box-solid box-primary mbl" style="border-color: #3F53D9">
                <div class="box-header" style="background: #3F53D9;">
                    <div class="box-title">
                        Nouveaux prospects
                    </div>
                </div>
                <div class="box-body">
                    <div class="table-responsive">
                        <table class="table">
                            <thead>
                            <tr>
                                <th></th>
                                <th>RPPS</th>
                                <th>Nom</th>
                                <th>Prénom</th>
                                <th>Catégorie professionnelle</th>
                                <th>Spécialité</th>
                                <th>Conseiller</th>
                                <th>A recontacter le</th>
                                <th>Commentaire Lead</th>
                                <th>UGA</th>
                                <th>Coordinateur</th>
                                <th>Code apporteur</th>
                                <th>Adherent</th>
                            </tr>
                            </thead>
                            <tbody>
                            {% for index, item in preview["created"] %}
                                {% set leadAdvisor = false %}
                                {% for advisor in advisors %}
                                    {% if item.data.advisor and item.data.advisor.id == advisor.id %}
                                        {% set leadAdvisor = advisor %}
                                    {% endif %}
                                {% endfor %}
                                <tr>
                                    <td>
                                        <div style="display: flex; gap: 5px">
                                            <input type="checkbox" id="lead_{{ index }}_create" class="checkbox-lead" name="lead[{{ index }}][import]" value="{{ item.data.id }}">
                                            <label for="lead_{{ index }}_create">Importer</label>
                                        </div>
                                    </td>
                                    <td>{{ item.data.rpps }}</td>
                                    <td>
                                        {{ item.data.lastname }}
                                    </td>
                                    <td>{{ item.data.firstname }}</td>
                                    <td>
                                        <select required disabled class="form-control" name="lead[{{ index }}][category]">
                                            <option value="">Aucun</option>
                                            {% for category in categories %}
                                                <option value="{{ category }}" {% if item.data.category == category %}selected="selected"{% endif %}>{{ category }}</option>
                                            {% endfor %}
                                        </select>
                                    </td>
                                    <td>
                                        <select required disabled class="form-control" name="lead[{{ index }}][speciality]">
                                            <option value="">Aucun</option>
                                            {% for speciality in specialities %}
                                                <option value="{{ speciality }}" {% if item.data.speciality == speciality %}selected="selected"{% endif %}>{{ speciality }}</option>
                                            {% endfor %}
                                        </select>
                                    </td>
                                    <td>
                                        <select disabled class="form-control" name="lead[{{ index }}][advisor]">
                                            <option value="">Aucun</option>
                                            {% for advisor in advisors %}
                                                <option value="{{ advisor.id }}" {% if item.data.advisor and item.data.advisor.id == advisor.id %}selected="selected"{% endif %}>{{ advisor.fullname }}</option>
                                            {% endfor %}
                                        </select>
                                    </td>
                                    <td>
                                        <input disabled type="text" class="form-control" name="lead[{{ index }}][leadContactDate]" value="{{ item.data.leadContactDate }}">
                                    </td>
                                    <td>
                                        <textarea disabled class="form-control" name="lead[{{ index }}][leadComment]" cols="30" rows="5" style="resize: vertical; height: 34px; min-height: 34px;">{{ item.data.leadComment }}</textarea>
                                    </td>
                                    <td>{{ item.data.uga }}</td>
                                    <td>
                                        <select disabled class="form-control" name="lead[{{ index }}][leadReferent]">
                                            <option value="">Aucun</option>
                                            {% for coordinator in coordinators %}
                                                <option value="{{ coordinator.id }}" {% if item.data.coordinator and item.data.coordinator.id == coordinator.id %}selected="selected"{% endif %}>{{ coordinator.fullname }}</option>
                                            {% endfor %}
                                        </select>
                                    </td>
                                    <td>
                                        {{ item.data.codeApporteur }}
                                        {% if leadAdvisor and leadAdvisor.codeApporteur != item.data.codeApporteur %} 
                                            <i 
                                                style="color:red; margin-left:2px" 
                                                class="fa fa-exclamation-triangle" 
                                                aria-hidden="true"
                                                title="{% if codeApporteurList[item.data.codeApporteur] is defined %} Ce code apporteur est déjà associé au conseillé {{ codeApporteurList[item.datacodeApporteur].fullname }} {% else %} Le code apporteur inséré ne correspond pas au code apporteur du conseillé associé par défaut{% endif %}">
                                            </i> 
                                        {% endif %}
                                    </td>
                                    <td>{{ item.data.gpmMemberNumber }}</td>
                                </tr>
                            {% endfor %}
                            </tbody>
                        </table>
                    </div>
                </div>
            </div>
        {% endif %}
        {{ form_rest(formSubmit) }}
        {# {% if preview["errors"]|length == 0 %} #}
            <button id="validate-import" class="btn btn-eduprat disabled" disabled>Valider</button>
        {# {% endif %} #}
        {{ form_end(formSubmit) }}
    {% endif %}

{% endblock %}

{% block javascripts %}
    <script>

        $(".radio-lead").click(function() {
			let $this = $(this);
	        let $controls = $this.closest(".box").find(".form-control");
	        $controls.attr("disabled", "disabled");

			if ($this.hasClass("radio-lead-create")) {
				$this.closest(".box").find(".table-lead").find(".form-control").removeAttr("disabled");
            }

			if ($this.hasClass("radio-lead-update")) {
                $('[data-refLead="' + $this.attr("data-ref") + '"]').removeAttr("disabled");
                $this.closest(".box").find(".table-new-lead").find(".form-control").removeAttr("disabled");
				//$this.closest("tr").find(".form-control").removeAttr("disabled");
				$this.closest(".box").find(".form-referent-create").attr("disabled", "disabled");
            }

            if ($this.hasClass("radio-lead-create")) {
				$this.closest("tr").find(".form-control").removeAttr("disabled");
				$this.closest(".box").find(".form-referent-update").attr("disabled", "disabled");
            }
			updateCanSubmit();
        });

        $(".checkbox-lead").click(function() {
	        let $this = $(this);
	        let $controls = $this.closest("tr").find(".form-control");
	        if ($this.prop("checked")) {
		        $controls.removeAttr("disabled");
	        } else {
		        $controls.attr("disabled", "disabled");
	        }
	        updateCanSubmit();
        });

		const $submitForm = $("form[name=submit]");
		const $validate = $("#validate-import");

		function updateCanSubmit() {
			let canSubmit = $submitForm.serializeArray().filter(value => {
				return value.name.startsWith("lead[");
            }).length > 0;
			if (canSubmit) {
				$validate.removeClass("disabled");
				$validate.removeAttr("disabled", "disabled");
            } else {
				$validate.addClass("disabled");
				$validate.attr("disabled", "disabled");
            }
        }
        updateCanSubmit();
    </script>
{% endblock %}