{% extends 'admin/base_login.html.twig' %}

{% block body %}
    <p class="login-box-msg">Connexion à l'interface de gestion</p>
    <div id="error-block" class="callout callout-danger {% if error is empty %}hide{% endif %}">
        <p>{% if error %}{{ error|trans({}, 'validators') }}{% endif %}</p>
    </div>
    <form class="login-form" action="{{ path('alienor_user_login_check') }}" method="post">
        <div class="form-group has-feedback">
            <input id="username" name="_username" class="form-control" placeholder="{{ "login.email"|trans }}" value="{{ last_username }}">
        </div>
        <div class="form-group has-feedback">
            <input id="password" name="_password" type="password" class="form-control" placeholder="{{ "login.password"|trans }}">
            <i class="togglePassword fa fa-eye"></i>
        </div>
        <div class="row">
            <div class="col-lg-6">
                <a href="{{ url('admin_user_reset') }}">Mot de passe oublié</a>
            </div>
            <div class="col-xs-4 pull-right">
                <button type="submit" class="btn btn-eduprat btn-block btn-flat">{{ "signin"|trans }}</button>
            </div>
        </div>
        <input type="hidden" name="_csrf_token" value="{{ csrf_token('authenticate') }}">
    </form>
{% endblock body %}