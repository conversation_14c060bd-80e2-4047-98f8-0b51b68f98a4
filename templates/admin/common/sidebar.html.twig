<aside class="left-side sidebar-offcanvas">

    <section class="sidebar">
        <ul class="sidebar-menu">
            <li class="treeview active acces-crm">
                <a href="{{ url('crm_index') }}">
                    <i class="fa fa-users"></i>
                    <span>CRM</span>
                </a>
            </li>
            {% if is_granted('ROLE_WEBMASTER') %}
                {% if app.user.isWebmasterCompta %}
                    <li class="treeview active">
                        <a href="#">
                            <i class="fa fa-folder"></i>
                            <span>Comptabilité</span>
                        </a>
                        <ul class="treeview-menu">
{#                            <li><a href="{{ url('admin_comptabilite_index') }}"><i class="fa fa-align-justify"></i> Liste </a></li>#}
                            {% set maxYear = "now"|date('Y') + 1 %}
                            {% for year in 2017..maxYear %}
                                <li><a href="{{ url('admin_comptabilite_index', {'year': year}) }}"><i class="fa fa-align-justify"></i> {{ year }}</a></li>
                            {% endfor %}
                        </ul>
                    </li>
                {% endif %}
            <li class="treeview active">
                <a href="#">
                    <i class="fa fa-folder"></i>
                    <span>Sessions</span>
                </a>
                <ul class="treeview-menu">
                    <li><a href="{{ url('admin_formation_index') }}"><i class="fa fa-align-justify"></i> Liste </a></li>
                    {% set maxYear = "now"|date('Y') + 1 %}
                    {% for year in 2017..maxYear %}
                        <li><a href="{{ url('admin_formation_index', {'year': year, 'archived' : 'non'}) }}"><i class="fa fa-align-justify"></i> {{ year }}</a></li>
                    {% endfor %}
                </ul>
            </li>
            <li class="treeview active">
                <a href="#">
                    <i class="fa fa-folder"></i>
                    <span>Formations</span>
                </a>
                <ul class="treeview-menu">
                    <li><a href="{{ url('admin_programme_index') }}"><i class="fa fa-align-justify"></i> Liste </a></li>
                    {% set maxYear = "now"|date('Y') + 1 %}
                    {% for year in 2017..maxYear %}
                        <li><a href="{{ url('admin_programme_index', {'year': year}) }}"><i class="fa fa-align-justify"></i> {{ year }}</a></li>
                    {% endfor %}
                    <li>
                        <a href="{{ url('admin_programme_create_select_type') }}" data-testid="programme-create">
                            <i class="fa fa-plus"></i>
                            <span>Ajouter</span>
                        </a>
                    </li>
                </ul>
            </li>
            {% if not app.user.isWebmasterCompta %}
            <li class="treeview active">
                <a href="#">
                    <i class="fa fa-folder"></i>
                    <span>Évaluations</span>
                </a>
                <ul class="treeview-menu">
                    <li>
                        <a href="#"><i class="fa fa-folder"></i> Topos
                            <span class="pull-right-container">
                              <i class="fa fa-angle-left pull-right"></i>
                            </span>
                        </a>
                        <ul class="treeview-menu">
                            {% set maxYear = "now"|date('Y') + 1 %}
                            {% for year in 2017..maxYear %}
                                <li><a href="{{ url('evaluation_global_topo_index', {'year': year}) }}"><i class="fa fa-align-justify"></i> {{ year }}</a></li>
                            {% endfor %}
                        </ul>
                    </li>
                    <li>
                        <a href="{{ url('evaluation_global_user_index') }}">
                            <i class="fa fa-user"></i>
                            <span>Collaborateurs</span>
                        </a>
                    </li>
                </ul>
            </li>
            <li class="treeview active">
                <a href="#">
                    <i class="fa fa-laptop"></i>
                    <span>E-learning</span>
                </a>
                <ul class="treeview-menu">
                    <li>
                        <a href="{{ url('admin_elearning_index') }}">
                            <i class="fa fa-align-justify"></i>
                            <span>Liste</span>
                        </a>
                    </li>
                    <li>
                        <a href="{{ url('admin_elearning_index', { archived: "true"}) }}">
                            <i class="fa fa-archive"></i>
                            <span>Archive</span>
                        </a>
                    </li>
                    <li>
                        <a href="{{ url('admin_elearning_create') }}">
                            <i class="fa fa-plus"></i>
                            <span>Ajouter</span>
                        </a>
                    </li>
                </ul>
            </li>
            <li class="treeview active">
                <a href="#">
                    <i class="fa fa-list-alt"></i>
                    <span>Formulaires</span>
                </a>
                <ul class="treeview-menu">
                    <li>
                        <a href="#"><i class="fa fa-files-o"></i> Audits
                            <span class="pull-right-container">
                              <i class="fa fa-angle-left pull-right"></i>
                            </span>
                        </a>
                        <ul class="treeview-menu">
                            <li>
                                <a href="{{ url('admin_audit_index', { type: "default" }) }}">
                                    <i class="fa fa-align-justify"></i>
                                    <span>Liste</span>
                                </a>
                            </li>
                            <li>
                                <a href="{{ url('admin_audit_index', { archived: "true", type: "default" }) }}">
                                    <i class="fa fa-archive"></i>
                                    <span>Archive</span>
                                </a>
                            </li>
                            <li>
                                <a href="{{ url('admin_audit_create', { type: "default" }) }}">
                                    <i class="fa fa-plus"></i>
                                    <span>Ajouter</span>
                                </a>
                            </li>

                            <li>
                                <a href="#"><i class="fa fa-circle-o"></i> Categories question
                                    <span class="pull-right-container">
                              <i class="fa fa-angle-left pull-right"></i>
                            </span>
                                </a>
                                <ul class="treeview-menu">
                                    <li><a href="{{ url('admin_audit_question_category_index') }}"><i class="fa fa-align-justify"></i> Liste</a></li>
                                    <li><a href="{{ url('admin_audit_question_category_create') }}"><i class="fa fa-plus"></i> Ajouter</a></li>
                                </ul>
                            </li>
                        </ul>
                    </li>
                    <li>
                        <a href="#"><i class="fa fa-files-o"></i> Cas cliniques
                            <span class="pull-right-container">
                              <i class="fa fa-angle-left pull-right"></i>
                            </span>
                        </a>
                        <ul class="treeview-menu">
                            <li>
                                <a href="{{ url('admin_audit_index', { type: "predefined" }) }}">
                                    <i class="fa fa-align-justify"></i>
                                    <span>Liste</span>
                                </a>
                            </li>
                            <li>
                                <a href="{{ url('admin_audit_index', { archived: "true", type: "predefined" }) }}">
                                    <i class="fa fa-archive"></i>
                                    <span>Archive</span>
                                </a>
                            </li>
                            <li>
                                <a href="{{ url('admin_audit_create', { type: "predefined" }) }}">
                                    <i class="fa fa-plus"></i>
                                    <span>Ajouter</span>
                                </a>
                            </li>
                        </ul>
                    </li>
                    <li>
                        <a href="#"><i class="fa fa-files-o"></i> Vignettes cliniques
                            <span class="pull-right-container">
                              <i class="fa fa-angle-left pull-right"></i>
                            </span>
                        </a>
                        <ul class="treeview-menu">
                            <li>
                                <a href="{{ url('admin_audit_index', { type: "vignette" }) }}">
                                    <i class="fa fa-align-justify"></i>
                                    <span>Liste</span>
                                </a>
                            </li>
                            <li>
                                <a href="{{ url('admin_audit_index', { archived: "true", type: "vignette" }) }}">
                                    <i class="fa fa-archive"></i>
                                    <span>Archive</span>
                                </a>
                            </li>
                            <li>
                                <a href="{{ url('admin_audit_create', { type: "vignette" }) }}">
                                    <i class="fa fa-plus"></i>
                                    <span>Ajouter</span>
                                </a>
                            </li>
                        </ul>
                    </li>
                     <li>
                        <a href="#"><i class="fa fa-files-o"></i> TCS
                            <span class="pull-right-container"><i class="fa fa-angle-left pull-right"></i></span>
                        </a>
                        <ul class="treeview-menu">
                            <li><a href="{{ url('app_admin_tcs_questionnaire_tcs_index') }}"><i class="fa fa-align-justify"></i> Liste</a></li>
                            <li><a href="{{ url('app_admin_tcs_questionnaire_tcs_archive_index') }}"><i class="fa fa-archive"></i> Archive</a></li>
                            <li><a href="{{ url('app_admin_tcs_questionnaire_tcs_new') }}"><i class="fa fa-plus"></i> Ajouter</a></li>
                            <hr style="margin:10px 0px">
                            <li><a href="{{ url('app_eduprat_admin_bundle_controller_expert_index') }}"><i class="fa fa-align-justify"></i> Liste Experts</a></li>
                            <li><a href="{{ url('app_eduprat_admin_bundle_controller_expert_new') }}"><i class="fa fa-plus"></i> Ajouter un expert</a></li>
                        </ul>
                    </li>
                    <li>
                        <a href="#"><i class="fa fa-list"></i> Questionnaires
                            <span class="pull-right-container">
                              <i class="fa fa-angle-left pull-right"></i>
                            </span>
                        </a>
                        <ul class="treeview-menu">
                            <li>
                                <a href="{{ url('admin_survey_index') }}">
                                    <i class="fa fa-align-justify"></i>
                                    <span>Liste</span>
                                </a>
                            </li>
                            <li>
                                <a href="{{ url('admin_survey_index', { archived: "true" }) }}">
                                    <i class="fa fa-archive"></i>
                                    <span>Archive</span>
                                </a>
                            </li>
                            <li>
                                <a href="{{ url('admin_survey_create') }}">
                                    <i class="fa fa-plus"></i>
                                    <span>Ajouter</span>
                                </a>
                            </li>

                            <li>
                                <a href="#"><i class="fa fa-circle-o"></i> Categories question
                                    <span class="pull-right-container">
                                      <i class="fa fa-angle-left pull-right"></i>
                                    </span>
                                </a>
                                <ul class="treeview-menu">
                                    <li><a href="{{ url('admin_survey_question_category_index') }}"><i class="fa fa-align-justify"></i> Liste</a></li>
                                    <li><a href="{{ url('admin_survey_question_category_create') }}"><i class="fa fa-plus"></i> Ajouter</a></li>
                                </ul>
                            </li>
                        </ul>
                        </li>
                        <li>
                            <a href="#"><i class="fa fa-circle-o"></i> Thématiques
                                <span class="pull-right-container">
                                  <i class="fa fa-angle-left pull-right"></i>
                                </span>
                            </a>
                            <ul class="treeview-menu">
                                <li><a href="{{ url('admin_audit_category_index') }}"><i class="fa fa-align-justify"></i> Liste</a></li>
                                <li><a href="{{ url('admin_audit_category_create') }}"><i class="fa fa-plus"></i> Ajouter</a></li>
                            </ul>
                        </li>
                    </ul>
                </li>
            {% endif %}
            {% if is_granted('ROLE_WEBMASTER') and not app.user.isWebmasterCompta %}
                <li class="treeview active">
                    <a href="#">
                        <i class="fa fa-folder"></i>
                        <span>Comptabilité</span>
                    </a>
                    <ul class="treeview-menu">
{#                        <li><a href="{{ url('admin_comptabilite_index') }}"><i class="fa fa-align-justify"></i> Liste </a></li>#}
                        {% set maxYear = "now"|date('Y') + 1 %}
                        {% for year in 2017..maxYear %}
                            <li><a href="{{ url('admin_comptabilite_index', {'year': year}) }}"><i class="fa fa-align-justify"></i> {{ year }}</a></li>
                        {% endfor %}
                    </ul>
                </li>
            {% endif %}
            <li class="treeview active">
                <a href="#">
                    <i class="fa fa-euro"></i>
                    <span>Modes de financement</span>
                </a>
                <ul class="treeview-menu">
                    <li>
                        <a href="{{ url('admin_finance_mode_index') }}">
                            <i class="fa fa-align-justify"></i>
                            <span>Liste</span>
                        </a>
                    </li>
                    <li>
                        <a href="{{ url('admin_finance_mode_create') }}">
                            <i class="fa fa-plus"></i>
                            <span>Ajouter</span>
                        </a>
                    </li>
                </ul>
                <ul class="treeview-menu">
                    <li>
                        <a href="#"><i class="fa fa-euro"></i> Sous-modes de financement
                        <span class="pull-right-container">
                          <i class="fa fa-angle-left pull-right"></i>
                        </span>
                        </a>
                        <ul class="treeview-menu">
                            <li>
                                <a href="{{ url('admin_finance_sous_mode_index') }}">
                                    <i class="fa fa-align-justify"></i>
                                    <span>Liste</span>
                                </a>
                            </li>
                            <li>
                                <a href="{{ url('admin_finance_sous_mode_create') }}">
                                    <i class="fa fa-plus"></i>
                                    <span>Ajouter</span>
                                </a>
                            </li>
                        </ul>
                    </li>
                </ul>
            </li>
            {% if not app.user.isWebmasterCompta %}
            <li class="treeview active">
                <a href="#">
                    <i class="fa fa-cogs"></i>
                    <span>Configuration</span>
                </a>
                <ul class="treeview-menu">
                    <li>
                        <a href="#">
                            <i class="fa fa-question-circle"></i> FAQ
                            <span class="pull-right-container">
                              <i class="fa fa-angle-left pull-right"></i>
                            </span>
                        </a>
                        <ul class="treeview-menu">
                            <li>
                                <a href="{{ url('admin_faq_index') }}">
                                    <i class="fa fa-align-justify"></i>
                                    <span>Liste</span>
                                </a>
                            </li>
                            <li>
                                <a href="{{ url('admin_faq_create') }}">
                                    <i class="fa fa-plus"></i>
                                    <span>Ajouter</span>
                                </a>
                            </li>
                        </ul>
                    </li>
                    <li>
                        <a href="{{ url('admin_config_emails') }}">
                            <i class="fa fa-envelope"></i> Emails
                        </a>
                    </li>
                </ul>
            </li>
            <li class="treeview active">
                <a href="#">
                    <i class="fa fa-folder"></i>
                    <span>Prises en charge</span>
                </a>
                <ul class="treeview-menu">
                    <li>
                        <a href="{{ url('admin_prise_en_charge_index') }}">
                            <i class="fa fa-align-justify"></i>
                            <span>Liste</span>
                        </a>
                    </li>
                    <li>
                        <a href="{{ url('admin_prise_en_charge_create') }}">
                            <i class="fa fa-plus"></i>
                            <span>Ajouter</span>
                        </a>
                    </li>
                </ul>
            </li>
            <li class="treeview active">
                <a href="#">
                    <i class="fa fa-folder"></i>
                    <span>Tags</span>
                </a>
                <ul class="treeview-menu">
                    <li>
                        <a href="{{ url('admin_tag_index') }}">
                            <i class="fa fa-align-justify"></i>
                            <span>Liste</span>
                        </a>
                    </li>
                    <li>
                        <a href="{{ url('admin_tag_create') }}">
                            <i class="fa fa-plus"></i>
                            <span>Ajouter</span>
                        </a>
                    </li>
                </ul>
            </li>
            {% endif %}
            {% endif %}
            {% if is_granted('ROLE_WEBMASTER') %}
            <li class="treeview active">
                <a href="#">
                    <i class="fa fa-user"></i>
                    <span>Utilisateurs</span>
                </a>
                <ul class="treeview-menu">
                    <li>
                        <a href="{{ url('admin_user_index') }}">
                            <i class="fa fa-align-justify"></i>
                            <span>Liste</span>
                        </a>
                    </li>
                    <li>
                        <a href="{{ url('admin_user_register') }}">
                            <i class="fa fa-plus"></i>
                            <span>Ajouter</span>
                        </a>
                    </li>
                </ul>
            </li>
            {% if not app.user.isWebmasterCompta %}
                <li class="treeview active">
                    <a href="#">
                        <i class="fa fa-circle-o"></i>
                        <span>Bilan</span>
                    </a>
                    <ul class="treeview-menu">
                        {% set maxYear = "now"|date('Y') + 1 %}
                        {% for year in 2017..maxYear %}
                            <li><a href="{{ url('admin_classeur_bilan', {'year': year, 'quarter': 1}) }}"><i class="fa fa-align-justify"></i> {{ year }}</a></li>
                        {% endfor %}
                    </ul>
                </li>
            {% endif %}
            {% endif %}
            {% if is_granted('ROLE_SUPERVISOR') %}
                <li class="treeview active">
                    <a href="#">
                        <i class="fa fa-user-md"></i>
                        <span>Superviseur</span>
                    </a>
                    <ul class="treeview-menu">
                        {% if app.user and (app.user.isSupervisor or app.user.isSupervisorFr) %}
                        <li>
                            <a href="{{ url('admin_suivi_cr', {'person': app.user.id, 'year': "now"|date('Y')}) }}">
                                <i class="fa fa-align-justify"></i>
                                <span>Mon suivi</span>
                            </a>
                        </li>
                        {% endif %}
                        <li>
                            <a href="{{ url('admin_user_index', { role: 'ROLE_COORDINATOR'}) }}">
                                <i class="fa fa-align-justify"></i>
                                <span>Coordinateurs</span>
                            </a>
                        </li>
                    </ul>
                </li>
                {% if not app.user.isSupervisor and not app.user.isSupervisorFr %}
                <li class="treeview active">
                    <a href="#">
                        <i class="fa fa-circle-o"></i>
                        <span>Grilles d'indemnisation</span>
                    </a>
                    <ul class="treeview-menu">
                        {% set maxYear = "now"|date('Y') + 1 %}
                        {% for year in 2021..maxYear %}
                            <li><a href="{{ url('admin_indemnisation_index', {'year': year}) }}"><i class="fa fa-align-justify"></i>{{ year }}</a></li>
                        {% endfor %}
                    </ul>
                </li>
                {% endif %}
            {% endif %}
            {% if app.user and (app.user.isCoordinator or is_granted("ROLE_SUPER_ADMIN")) %}
            <li class="treeview active">
                <a href="{{ url('admin_plaquette_search') }}">
                    <i class="fa fa-file"></i>
                    <span>Communication</span>
                </a>
            </li>
            {% endif %}
            {% if app.user and (app.user.isCoordinator or app.user.isSupervisorFr or app.user.isSupervisor) %}
                <li class="treeview active">
                    <li><a href="{{ url('admin_formation_index', {year: "now"|date('Y'), 'archived' : 'non'}) }}">
                        <i class="fa fa-align-justify"></i>
                        <span>Mes sessions</span>
                    </a>
                </li>
                {% if not app.user.isSupervisorFr  and not app.user.isSupervisor %}
                    <li class="treeview active">
                        <a href="{{ url('admin_programme_index', { year: "now"|date('Y') }) }}">
                            <i class="fa fa-align-justify"></i>
                            <span>Mes formations</span>
                        </a>
                    </li>
                {% endif %}
            {% endif %}
            {% if app.user and (app.user.isFormer or app.user.isCoordinator) %}
                <li class="treeview active">
                    <a href="{{ url('admin_user_monitoring', {'id': app.user.id}) }}">
                        <i class="fa fa-user-md"></i>
                        <span>Mon suivi</span>
                    </a>
                </li>
            {% endif %}
            {# On masque les dashboards le temps de rendre la dataviz compatible avec la refonte
            {% if app.user and hasDashboard(app.user) %}
                <li class="treeview active">
                    <a>
                        <i class="fa fa-tachometer"></i>
                        <span>Dashboards</span>
                    </a>
                    <ul class="treeview-menu">
                        {% for name, dashboard in dashboardList(app.user) %}
                            <li>
                                <a href="{{ url('admin_dashboard_show', { id : dashboard.name }) }}">
                                    <i class="fa fa-minus"></i>
                                    <span>{{ dashboard.label }}</span>
                                </a>
                            </li>
                        {% endfor %}
                    </ul>
                </li>
            {% endif %}
            #}
            {% if is_granted('ROLE_SUPERVISOR') and not app.user.isSupervisor %}
                <li class="treeview active">
                    <a href="{{ url('export-csv-type') }}">
                        <i class="fa fa-circle-o"></i>
                        <span>Exports CSV</span>
                    </a>
                </li>
            {% endif %}
            {% if canSwitchAccount() %}
                <li class="treeview active">
                    <a href="#">
                        <i class="fa fa-circle-o"></i>
                        <span>Changer de compte</span>
                    </a>
                    <ul class="treeview-menu">
                        {% for account in getSwitchableAccounts() %}
                            {% if app.user.id != account.id or account.role == "ROLE_PARTICIPANT" %}
                                <li>
                                    <a href="{{ url('admin_user_switch', { id: account.id, role: account.role }) }}">
                                        <i class="fa fa-align-justify"></i>
                                        <span>{{ ("user.role." ~ account.role)|trans }}</span>
                                    </a>
                                </li>
                            {% endif %}
                        {% endfor %}
                    </ul>
                </li>
            {% endif %}
        </ul>
    </section>
    <!-- /.sidebar -->
</aside>
