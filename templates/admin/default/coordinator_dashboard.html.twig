{% extends 'admin/base.html.twig' %}

{% block title %}Tableau de bord {{ year }}{% endblock %}

{% block body %}
    <p>
        <em>Consulter les tableaux de bords des années précédentes :</em>
    </p>
    <ul class="pagination">
        {% set maxYear = "now"|date('Y') + 1 %}
        {% for dateYear in first_year..maxYear %}
            <li {% if year == dateYear %}class="active"{% endif %}><a href="{{ url('admin_coordinator_dashboard', {id: person.id, year: dateYear}) }}">{{ dateYear }}</a></li>
        {% endfor %}
    </ul>
    <div class="box box-info">
        <div class="box-body">
            <div class="row">
                <div class="col-sm-12">
                    {% if not person.isCoordinatorLbi %}
                    <div class="col-sm-12">
                        <div class="callout callout-warning">
                            <p>* Le cumul des marges et les calculs des commissions se basent sur la totalité des formations de l'année.</p>
                        </div>
                        <h4>Détails des marges :</h4>
                        <table class="table table-bordered table-responsive table-hover table-totaux">
                            <tr>
                                <th>CA coordinateur total</th>
                                <td>{{ person.allCoordinatorCaSecteurByCoordinator(year)|number_format(2, '.', ' ') }} €</td>
                            </tr>
                            <tr>
                                <th>Cumul des marges*</th>
                                <td>{{ total.marges|number_format(2, '.', ' ') }} €</td>
                            </tr>
                            <tr>
                                <th>Commissions taux de base*</th>
                                <td>{{ total.commissionBase|number_format(2, '.', ' ') }} €</td>
                            </tr>
                            <tr>
                                <th>Commission actualisée*</th>
                                <td>{{ total.commissionActualisee|number_format(2, '.', ' ') }} €</td>
                            </tr>
                            <tr>
                                <th>Commission exceptionnelle*</th>
                                <td>{{ total.commissionExceptionnelle|number_format(2, '.', ' ') }} €</td>
                            </tr>
                            {% if is_granted('ROLE_WEBMASTER') %}
                                {% set totalHonorary = person.getAllCoordinatorsCommissionsComptabilisees(year) %}
                                <tr>
                                    <th>Commissions comptabilisées</th>
                                    <td>{{ totalHonorary|number_format(2, '.', ' ') }} €</td>
                                </tr>
                                <tr>
                                    <th>Écart commissions</th>
                                    <td>{{ (total.commissionBase - totalHonorary)|number_format(2, '.', ' ') }} €</td>
                                </tr>
                            {% endif %}
                            <tr>
                                <td colspan="2">
                                    <a class="btn btn-eduprat" target="_blank" href="{{ asset('uploads/Tableau taux d\'intéressement.pdf') }}"><i class="fa fa-download"></i>    Télécharger le tableau d’intéressement</a>
                                </td>
                            </tr>
                        </table>
                    </div>
                    {% endif %}

                    <div class="col-lg-12">
                        <h4>Synthèse des formations :</h4>
                    </div>
                    <div class="col-lg-6 col-sm-12">
                        <table class="table table-bordered table-responsive table-hover table-totaux3">
                            <tr>
                                <th>Nombre de formations</th>
                                <td>{{ allProgrammes|length }}</td>
                            </tr>
                            <tr>
                                <th>Nombre de participations totales</th>
                                <td>{{ counts.totals.participations }}</td>
                            </tr>
                            {% for category, count in counts.categories %}
                                <tr>
                                    <th>Participations {{ category }}</th>
                                    <td>{{ count.participations }}</td>
                                </tr>
                            {% endfor %}
                        </table>
                    </div>
                    <div class="col-lg-6 col-sm-12">
                        <table class="table table-bordered table-responsive table-hover table-totaux3">
                            <tr>
                                <th colspan="2">&nbsp;</th>
                            </tr>
                            <tr>
                                <th>Nombre de participants uniques totaux</th>
                                <td>{{ counts.totals.participants }}</td>
                            </tr>
                            {% for category, count in counts.categories %}
                                <tr>
                                    <th>Participants uniques {{ category }}</th>
                                    <td>{{ count.participants }}</td>
                                </tr>
                            {% endfor %}
                        </table>
                    </div>
                </div>
            </div>
        </div>
    </div>
{% endblock body %}
