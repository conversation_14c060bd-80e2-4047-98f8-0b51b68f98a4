<div id="box-participants" class="row box-eduprat">
    <h4>{{ "admin.formation.participants"|trans }}</h4>
    {% set attestationMandatory = formation.attestationMandatory() %}
    {% for financeSousMode in formation.financeSousModes %}
        <div class="formation-finance-sous-mode-header {% if financeSousMode.isHorsDPC() %} finance-sous-mode-header-hors-dpc {% endif %}">{{ financeSousMode.identifiant ~ ' - ' ~ financeSousMode.name }}<i> ({{ financeSousMode.priseEnCharge }})</i></div>
        <table class="table table-bordered table-hover">
            <thead>
            <tr>
                {% if formation.hasLinkedForm %}
                    <th width="2%">Cpte</th>
                {% endif %}
                <th width="6%">{{ "admin.participant.lastname"|trans }}</th>
                <th width="6%">{{ "admin.participant.firstname"|trans }}</th>
                <th width="6%">{{ "admin.participant.category"|trans }}</th>
                {% if is_granted('ROLE_COORDINATOR_LBI') %}
                    <th width="6%">{{ "admin.participant.uga"|trans }}</th>
                {% endif %}
                {% if not app.user.isPharmacieFormer %}
                    {% if not app.user.isCoordinatorLbi %}
                        <th width="4.5%">{{ "admin.participant.priceDpc"|trans }}</th>
                    {% endif %}
                    {% if not newHonorary %}
                        <th width="4.5%">{{ "admin.participant.budgetCR"|trans }}</th>
                    {% endif %}
                {% endif %}

                {% if formation.hasLinkedForm %}
                    {% if formation.isAudit or formation.isPresentielle or formation.isTcs %}
                        <th class="text-center" width="6%">Parcours</th>
                    {% endif %}
                    {# {% if formation.isAudit %}
                    {% if formation.isPredefined %}
                        <th>{{ "admin.formation.predefined1"|trans }}</th>
                        <th>{{ "admin.formation.predefined2"|trans }}</th>
                    {% else %}
                        {% if formation.isVignette %}
                            <th>{{ "admin.formation.vignette1"|trans }}</th>
                            <th>{{ "admin.formation.vignette2"|trans }}</th>
                        {% else %}
                            <th>{{ "admin.formation.audit1"|trans }}</th>
                            <th>{{ "admin.formation.audit2"|trans }}</th>
                        {% endif %}
                    {% endif %}
                {% elseif formation.isPresentielle %}
                    <th>{{ "admin.formation.questionnaire1"|trans }}</th>
                    <th>{{ "admin.formation.questionnaire2"|trans }}</th>
                {% endif %} #}
                {% endif %}
                {% if attestationMandatory %}
                    <th width="6%">Attestations</th>
                {% endif %}
                {% if is_granted('ROLE_COORDINATOR_LBI') %}
                    <th width="8%">
                        {{ 'admin.global.actions'|trans }}
                    </th>
                {% endif %}
                {% if is_granted('ROLE_COORDINATOR_LBI') and coordinators|length > 1 %}
                    <th width="8%">{{ "admin.participant.coordinator"|trans }}</th>
                {% endif %}
                {% if is_granted('ROLE_COORDINATOR_LBI') and formation.programme.exercisesMode|length > 1 %}
                    <th width="8%">{{ "admin.participant.exerciseMode"|trans }}</th>
                {% endif %}
                <th width="8%">{{ "admin.participant.partenariat"|trans }}</th>
            </tr>
            </thead>
            <tbody class="table-finance-sous-mode" id="table-finance-sous-mode-{{ loop.index }}" data-id="{{ financeSousMode.id }}">
            {% for participation in participations %}
                {%  if participation.financeSousMode == financeSousMode %}
                    {% if formation.hasLinkedForm %}
                        {% set formCompleted1 = participation|auditCompleted(1) %}
                        {% set formCompleted2 = participation|auditCompleted(2) %}
                    {% endif %}
                    <tr class="row-participations" id="row-participation-{{ participation.id }}" data-id="{{ participation.id }}">
                        {% if formation.hasLinkedForm %}
                            <td style="text-align:center">
                                {% if participation.participant.user and participation.participant.user.hasCreatedPassword %}
                                    <img src="{{ asset('img/yes.png') }}" width="20" height="20" alt="Actif" />
                                {% else %}
                                    <img src="{{ asset('img/no.png') }}" width="20" height="20" alt="Inactif" />
                                {% endif %}
                            </td>
                        {% endif %}
                        <td>{% if is_granted('ROLE_WEBMASTER') %}<div style="display:flex"><span class='handle handle-icon'></span>{% endif %} <div> {{ participation.participant.lastname }}</div>{% if is_granted('ROLE_WEBMASTER') %}</div>{% endif %}</td>
                        <td>{{ participation.participant.firstname }}</td>
                        <td>{{ participation.participant.category }}</td>
                        {% if is_granted('ROLE_COORDINATOR_LBI') and not formation.featureDisabledBecauseAccounted() %}
                            <td>
                                <select style="width:100%" class="edit_uga" data-id="{{ participation.id }}">
                                    <option>{{ 'admin.global.select'|trans }}</option>
                                    {% for key, value in crm_uga %}
                                        <option {% if participation.uga == key %} selected="selected" {% endif %} data-field="uga" value="{{value.id}}">{{ value.id }}</option>
                                    {% endfor %}
                                </select>
                            </td>
                        {% elseif is_granted('ROLE_COORDINATOR_LBI') %}
                            <td>
                                {{ participation.uga }}
                            </td>
                        {% endif %}
                        {% if not app.user.isPharmacieFormer %}
                            {% if not app.user.isCoordinatorLbi %}
                                <td class="cout">
                                    {% if is_granted('ROLE_WEBMASTER') and not formation.locked %}
                                        <div class="inputs"></div>
                                    {% endif %}
                                    {% if is_granted('ROLE_WEBMASTER') and not formation.locked %}
                                        <a href="#" class="label label-primary edit" data-id="{{ participation.id }}" data-price="{{ participation.price }}" data-priceN1="{{ participation.priceYearN1 }}" data-editing="false" data-field="price" data-yearstart="{{ participation.formation.openingDate|date('Y') }}" data-yearend="{{ participation.formation.closingDate|date('Y') }}">
                                            <i class="fa fa-pencil"></i><span class="edit-text"></span>
                                        </a>
                                    {% endif %}
                                    <span class="edit-val">
                                    {% if participation.price is not null %}
                                        {% if participation.formation.isPluriAnnuelle() %}
                                            <strong>{{ participation.priceYearN1 + participation.price }}</strong><br>
                                            {{ participation.formation.openingDate|date('Y') }} : {{ participation.price }}<br>
                                            {{ participation.formation.closingDate|date('Y') }} : {{ participation.priceYearN1 }}
                                        {% else %}
                                            {{ participation.price }}
                                        {% endif %}
                                    {% else %}
                                        NC
                                    {% endif %}
                                </span>
                                </td>
                            {% endif %}
                            {% if not newHonorary %}
                                <td>
                                    {% if is_granted('ROLE_WEBMASTER') and not formation.locked %}
                                        <a href="#" class="label label-primary edit" data-id="{{ participation.id }}" data-editing="false" data-field="budgetCR">
                                            <i class="fa fa-pencil"></i><span class="edit-text"></span>
                                        </a>
                                    {% endif %}
                                    <span class="edit-val">{{ participation.budgetCR }}</span>
                                </td>
                            {% endif %}
                        {% endif %}
                        {% if formation.hasLinkedForm %}
                            <td>
                                {% set course = courseManager.getCourseDetail(participation) %}
                                <div style="display:flex; justify-content:center; align-items: center;">
                                {% if formation.hasCourse() and not formation.hasFormPost() %}
                                    {% if participation.courseEnded %}
                                        <img src="{{ asset('img/yes.png') }}" width="20" height="20" alt="OUI" />
                                    {% else %}
                                        <img src="{{ asset('img/no.png') }}" width="20" height="20" alt="OUI" />
                                    {% endif %}
                                {% else %}
                                    <div class="pre-session-group" style="margin-left:10px">
                                        Pré
                                        <div style="margin-left:6px">
                                            {% if formation.isElearning and isBeforeElearningUpdate %}
                                                {% if formCompleted1 %}
                                                    <img src="{{ asset('img/yes.png') }}" width="20" height="20" alt="OUI" />
                                                {% else %}
                                                    <img src="{{ asset('img/no.png') }}" width="20" height="20" alt="OUI" />
                                                {% endif %}
                                            {% else %}
                                                {% if formation.isElearning %}
                                                    {% if participation.isStepCompleted("reunion") %}
                                                        <img src="{{ asset('img/yes.png') }}" width="20" height="20" alt="OUI" />
                                                    {% else %}
                                                        <img src="{{ asset('img/no.png') }}" width="20" height="20" alt="OUI" />
                                                    {% endif %}
                                                {% else %}
                                                    {% if course["step_1"].completed %}
                                                        <img src="{{ asset('img/yes.png') }}" width="20" height="20" alt="OUI" />
                                                    {% else %}
                                                        <img src="{{ asset('img/no.png') }}" width="20" height="20" alt="OUI" />
                                                    {% endif %}
                                                {% endif %}
                                            {% endif %}
                                        </div>
                                    </div>

                                    <div class="separator"></div>

                                    <div class="post-session-group">
                                        Post
                                        <div style="margin-left:6px">
                                            {% if participation.nextModule is not null %}
                                                <img src="{{ asset('img/no.png') }}" width="20" height="20" alt="OUI" />
                                            {% else %}
                                                <img src="{{ asset('img/yes.png') }}" width="20" height="20" alt="OUI" />
                                            {% endif %}
                                        </div>
                                    </div>
                                {% endif %}
                                <a href="#" data-participationid="{{ participation.id }}" style="margin-left:10px; background-color: #109199 !important;"class="parcoursListLink btn btn-eduprat btn-icon"><i class="fa fa-pencil"></i><span class="edit-text"></span></a>
                                </div>
                            </td>
                        {% endif %}
                        {% if attestationMandatory %}
                            <td>
                                <div class="flex" style="align-items: center;">
                                    <div>
                                        {% if participation.formation.isPluriAnnuelle() and participation.formation.containOffline(participation.formation.closingDate|date('Y')) %}
                                            {{ participation.formation.openingDate|date('Y') }}
                                            {% if participation.attestationHonneur %}
                                                <img src="{{ asset('img/yes.png') }}" width="15" height="15" alt="OUI" /><br>
                                            {% else %}
                                                <img src="{{ asset('img/no.png') }}" width="15" height="15" alt="NON" /><br>
                                            {% endif %}
                                            {{ participation.formation.closingDate|date('Y') }}
                                            {% if participation.attestationHonneurN1 %}
                                                <img src="{{ asset('img/yes.png') }}" width="15" height="15" alt="OUI" /><br>
                                            {% else %}
                                                <img src="{{ asset('img/no.png') }}" width="15" height="15" alt="NON" /><br>
                                            {% endif %}
                                        {% else %}
                                            {% if participation.attestationHonneur %}
                                                <img src="{{ asset('img/yes.png') }}" width="20" height="20" alt="OUI" />
                                            {% else %}
                                                <img src="{{ asset('img/no.png') }}" width="20" height="20" alt="NON" />
                                            {% endif %}
                                        {% endif %}
                                    </div>
                                    <div>
                                        {% if is_granted('ROLE_SUPERVISOR') %}
                                            <a href="#" data-participationid="{{ participation.id }}" style="margin-left:10px; background-color: #109199 !important;"class="attestationListLink btn btn-eduprat btn-icon"><i class="fa fa-pencil"></i><span class="edit-text"></span></a>
                                        {% elseif app.user.isCoordinator %}
                                            {% if participation.participant.coordinator == app.user %}
                                                <a href="#" data-participationid="{{ participation.id }}" style="margin-left:10px; background-color: #109199 !important;"class="attestationListLink btn btn-eduprat btn-icon"><i class="fa fa-pencil"></i><span class="edit-text"></span></a>
                                            {% endif %}
                                        {% endif %}
                                    </div>
                                </div>
                            </td>
                        {% endif %}
                        {% if is_granted('ROLE_COORDINATOR_LBI') %}
                            <td>
                                <a href="{{ url('admin_participant_show', {'id': participation.participant.id }) }}" class="mbn btn btn-eduprat btn-icon" title="{{ 'admin.global.more'|trans }}"><i class="fa fa-eye"></i></a>
                                {% if is_granted('ROLE_COORDINATOR') %}
                                    {% if is_granted('ROLE_WEBMASTER') and not formation.featureDisabledBecauseAccounted() %}
                                        <a href="{{ url('admin_participation_delete', {'id': participation.id }) }}" class="btn btn-eduprat btn-icon" title="{{ 'admin.global.delete'|trans }}"><i class="fa fa-trash-o"></i></a>
                                    {% endif %}
                                    {% if formation.hasLinkedForm %}
                                        <a href="#" class="btn btn-eduprat btn-icon btn-email" title="{{ 'admin.formation.mail.btn'|trans }}"><i class="fa fa-envelope"></i></a>
                                    {% endif %}
                                {% endif %}
                            </td>
                        {% endif %}

                        {% if coordinators|length > 1 %}
                            {% if is_granted('ROLE_COORDINATOR') and not formation.featureDisabledBecauseAccounted() %}
                                <td>
                                    <select class="edit_coordinator" data-id="{{ participation.id }}">
                                        <option>{{ 'admin.participation.coordinator.select'|trans }}</option>
                                        {% for coordinator in coordinators %}
                                            <option {% if participation.coordinator and participation.coordinator.id == coordinator.id %} selected="selected" {% endif %} data-field="coordinator" value="{{coordinator.id}}">{{ coordinator.person.firstname }} {{ coordinator.person.lastname }}</option>
                                        {% endfor %}
                                    </select>
                                </td>
                            {% elseif is_granted('ROLE_COORDINATOR_LBI') %}
                                <td>
                                    {% if participation.coordinator %}
                                        {{ participation.coordinator.person.firstname }} {{ participation.coordinator.person.lastname }}
                                    {% endif %}
                                </td>
                            {% endif %}
                        {% endif %}

                        {% if formation.programme.exercisesMode|length > 1 %}
                            {% if is_granted('ROLE_COORDINATOR') and not formation.featureDisabledBecauseAccounted() %}
                                <td>
                                    <select class="edit_exercise_mode" data-id="{{ participation.id }}">
                                        <option>{{ 'admin.participation.exerciseMode.select'|trans }}</option>
                                        {% for exerciseMode in formation.programme.exercisesMode %}
                                            <option {% if participation.exerciseMode and participation.exerciseMode == exerciseMode %} selected="selected" {% endif %} data-field="exerciseMode" value="{{exerciseMode}}">{{ exerciseMode }}</option>
                                        {% endfor %}
                                    </select>
                                </td>
                            {% elseif is_granted('ROLE_COORDINATOR_LBI') %}
                                <td>
                                    {% if participation.exerciseMode %}
                                        {{ participation.exerciseMode }}
                                    {% endif %}
                                </td>
                            {% endif %}
                        {% endif %}
                        <td>
                            <select class="edit_partenariat" data-id="{{ participation.id }}" style="width: 80%;">
                                <option data-field="partenariat" value="" {% if participation.partenariat == null %} selected="selected" {% endif %}>Aucun</option>
                                <option data-field="partenariat" value="GPM" {% if participation.partenariat == "GPM" %} selected="selected" {% endif %}>GPM</option>
                                <option data-field="partenariat" value="M Soigner" {% if participation.partenariat == "M Soigner" %} selected="selected" {% endif %}>M Soigner</option>
                                <option data-field="partenariat" value="MFM" {% if participation.partenariat == "MFM" %} selected="selected" {% endif %}>MFM</option>
                                <option data-field="partenariat" value="Union des podologues" {% if participation.partenariat == "Union des podologues" %} selected="selected" {% endif %}>UP</option>
                                <option data-field="partenariat" value="Site internet" {% if participation.partenariat == "Site internet" %} selected="selected" {% endif %}>Site internet</option>
                                <option data-field="partenariat" value="LBI" {% if participation.partenariat == "LBI" %} selected="selected" {% endif %}>LBI</option>
                                <option data-field="partenariat" value="Pharmazon" {% if participation.partenariat == "Pharmazon" %} selected="selected" {% endif %}>Pharmazon</option>
                            </select>
                        </td>
                    </tr>
                    {% set displayN1Attestation = formation.isPluriannuelle()  and formation.containOffline(formation.closingDate|date('Y')) %}
                    {% if not formation.programme.isFormatPresentiel %}
                        <tr class="sessionsWrap" id="attestation-{{ participation.id }}">
                            <td colspan="10">
                                <div class="attestationList" style="display: none">
                                    <div class="collaps-list collaps-list-attestation">
                                        <i class="fa fa-circle btn-attestation btn-attestation-circle"></i><a style="margin-left: 2px !important;" target="_blank" class="download-file" href="{{ url('pdf_attestation_honneur_pdf', { id: participation.formation.id, token: participation.formation.token, participant: participation.participant.id, person : "null" }) }}">{{ 'admin.participation.attestation_honneur.generate'|trans }} {% if displayN1Attestation %} de {{ formation.openingDate|date('Y')}}{% endif %}</a>
                                        {% if displayN1Attestation %}
                                            <br>
                                            <i class="fa fa-circle btn-attestation btn-attestation-circle"></i><a style="margin-left: 2px !important;" target="_blank" class="download-file" href="{{ url('pdf_attestation_honneur_pdf', { id: participation.formation.id, token: participation.formation.token, participant: participation.participant.id, person : "null", n1 : "true" }) }}">{{ 'admin.participation.attestation_honneur.generate'|trans }} de {{ (formation.closingDate|date('Y')) }}</a>
                                        {% endif %}
                                        <br>
                                        <div class="btn-download-file-attestation" data-attestation="{% if participation.attestationHonneur %}true{%else%}false{%endif%}" id="attestationHonneurFile_{{ participation.id }}">
                                            <i class="fa fa-download  btn-attestation" ></i>
                                            <div class="import-attestation">{{ 'admin.participation.attestation_honneur.import'|trans }}{% if displayN1Attestation %} de {{ participation.formation.openingDate|date('Y')}}{% endif %}</div>
                                        </div>
                                        {% if displayN1Attestation %}
                                            <div class="btn-download-file-attestation" data-attestation="{% if participation.attestationHonneurN1 %}true{%else%}false{%endif%}" id="attestationHonneurFileN1_{{ participation.id }}">
                                                <i class="fa fa-download  btn-attestation" ></i>
                                                <div class="import-attestation">{{ 'admin.participation.attestation_honneur.import'|trans }} de {{ participation.formation.closingDate|date('Y')}}</div>
                                            </div>
                                        {% endif %}
                                        {% if participation.attestationHonneur %}
                                            <i class="fa fa-eye btn-attestation"></i><a target="_blank" style="margin-left: 4px !important;" class="downloadFile" href="{{ url('admin_attestation_file', {'id' : participation.id }) }}">{{ 'admin.participation.attestation_honneur.view'|trans }}{% if displayN1Attestation %} de {{ participation.formation.openingDate|date('Y')}}{% endif %}</a><br>
                                            <i class="fa fa-trash-o btn-attestation"></i><a style="margin-left: 4px !important;" class="downloadFile delete-attestation" href="{{ url('admin_attestation_delete_file', {'id' : participation.id, redirect: app.request.uri ~ "#row-participation-" ~ participation.id }) }}">{{ 'admin.participation.attestation_honneur.delete'|trans }}{% if displayN1Attestation %} de {{ participation.formation.openingDate|date('Y')}}{% endif %}</a><br>
                                        {% endif %}
                                        {% if displayN1Attestation and participation.attestationHonneurN1%}
                                            <i class="fa fa-eye btn-attestation"></i><a target="_blank" style="margin-left: 4px !important;" class="downloadFile" href="{{ url('admin_attestation_file', {'id' : participation.id, n1: true}) }}">{{ 'admin.participation.attestation_honneur.view'|trans }} de {{ participation.formation.closingDate|date('Y')}}</a><br>
                                            <i class="fa fa-trash-o btn-attestation"></i><a style="margin-left: 4px !important;" class="downloadFile delete-attestation" href="{{ url('admin_attestation_delete_file', {'id' : participation.id, n1: true, redirect: app.request.uri ~ "#row-participation-" ~ participation.id }) }}">{{ 'admin.participation.attestation_honneur.delete'|trans }} de {{ participation.formation.closingDate|date('Y')}}</a><br>
                                        {% endif %}
                                        <div id="attestationHonneurFile" class="downloadFile">
                                            {{ form_start(forms_attestation_honneur[participation.id],{'attr': {'class': 'formDownloadFile'}}) }}
                                            {{ form_widget(forms_attestation_honneur[participation.id].attestationHonneurFile) }}
                                            {{ form_end(forms_attestation_honneur[participation.id]) }}
                                            {% if displayN1Attestation %}
                                                {{ form_start(forms_attestation_honneurN1[participation.id],{'attr': {'class': 'formDownloadFile'}}) }}
                                                {{ form_widget(forms_attestation_honneurN1[participation.id].attestationHonneurFileN1) }}
                                                {{ form_end(forms_attestation_honneurN1[participation.id]) }}
                                            {% endif %}
                                        </div>
                                    </div>
                                </div>
                            </td>
                        </tr>
                    {% endif %}
                    {% if formation.hasLinkedForm %}
                        {% set formPostAvailable = ("now"|date("Y-m-d")) <= formation.formClosingDate|date("Y-m-d") %}
                        {% include "admin/formation/emails.html.twig" %}
                        <tr class="sessionsWrap" id="parcours-{{ participation.id }}">
                            {# {% set isOldCourse = courseManager.isOldCourse(participation) %}
                        {# {% set course = courseManager.getCourseDetail(participation) %} #}
                            {% set courseType = courseManager.getCourseType(participation) %}
                            {% set participantActionSheet = participantActionSheetList[participation.id] is defined ? participantActionSheetList[participation.id] : false %}
                            <td colspan="10">
                                <div class="parcoursList" style="display: none">
                                    <div class="collaps-list collaps-list-attestation">
                                        <div class="row">
                                            {% include "admin/formation/modules_list.html.twig" %}
                                        </div>
                                    </div>
                                </div>
                            </td>
                        </tr>
                    {% endif %}
                {% endif %}
            {% endfor %}
            </tbody>
        </table>
    {% endfor %}
    <div class="row">
        <div class="col-sm-12">
            {% if pagination.npages > 1 %}
                {% include 'admin/common/pagination.html.twig' with pagination %}
            {% endif %}
        </div>
    </div>
</div>
