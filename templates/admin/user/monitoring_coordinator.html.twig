{% extends 'admin/base.html.twig' %}

{% block title %}{{ 'admin.user.detail.coordinator'|trans }}{% endblock %}

{% block body %}
    <div class="row">
        <div class="col-sm-12">
            <div class="box box-info">
                <div class="box-header">
                    <h2 class="box-title">{{ 'admin.user.detail.info'|trans }}</h2>
                </div>
                <div class="box-body">
                    <table class="table table-bordered table-hover">
                        <tr>
                            <th>{{ 'login.lastname'|trans }}</th>
                            <td>{{ user.lastname }}</td>
                        </tr>
                        <tr>
                            <th>{{ 'login.firstname'|trans }}</th>
                            <td>{{ user.firstname }}</td>
                        </tr>
                        <tr>
                            <th>{{ 'admin.user.address'|trans }}</th>
                            <td>{{ user.address }}</td>
                        </tr>
                        <tr>
                            <th>{{ 'admin.user.zipCode'|trans }}</th>
                            <td>{{ user.zipCode }}</td>
                        </tr>
                        <tr>
                            <th>{{ 'admin.user.city'|trans }}</th>
                            <td>{{ user.city }}</td>
                        </tr>
                        <tr>
                            <th>{{ 'admin.user.phone'|trans }}</th>
                            <td>{{ user.phone }}</td>
                        </tr>
                        <tr>
                            <th>{{ 'login.email'|trans }}</th>
                            <td>{{ user.email }}</td>
                        </tr>
                        <tr>
                            <th>{{ 'admin.user.siret'|trans }}</th>
                            <td>{{ user.siret }}</td>
                        </tr>
                        <tr>
                            <th>{{ 'admin.user.manualEmailReminder'|trans }}</th>
                            <td>{{ user.manualEmailReminder ? "Oui" : "Non" }}</td>
                        </tr>
                        {% if is_granted('ROLE_WEBMASTER') %}
                            <tr>
                                <th>{{ "admin.user.detail.report" | trans }}</th>
                                <td>
                                    {% set maxYear = "now"|date('Y') %}
                                    {% for year in 2018..maxYear %}
                                        <a class="btn btn-eduprat" target="_blank" href="{{ url('evaluation_global_coordinator_year', {'person': user.id, 'year': year}) }}">{{ year }}</a>
                                    {% endfor %}
                                </td>
                            </tr>
                        {% endif %}
                        {% if app.user.id == user.id %}
                            <tr>
                                <td colspan="2"><a class="btn btn-eduprat" href="{{ url('admin_user_edit', {id: user.id}) }}">{{ "admin.user.detail.edit" | trans }}</a></td>
                            </tr>
                        {% endif %}
                    </table>
                </div>
            </div>
        </div>
        <div class="col-sm-12">
            <div class="box box-info">
                <div class="box-header">
                    <h2 class="box-title">{{ 'admin.user.detail.sessions'|trans }}</h2>
                </div>
                <div class="box-body">
                    <table class="table table-bordered table-hover">
                        <thead>
                        <tr>
                            <th>{{ 'admin.formation.reference'|trans }}</th>
                            <th>{{ 'admin.formation.sessionNumber'|trans }}</th>
                            <th>{{ 'admin.formation.participantCount'|trans }}</th>
                            <th>{{ 'admin.formation.title'|trans }}</th>
                            <th>{{ 'admin.formation.startDate'|trans }}</th>
                            <th>{{ 'admin.user.detail.downloads'|trans }}</th>
                            <th class="text-center">
                                {{ "admin.global.actions" | trans }}
                            </th>
                        </tr>
                        </thead>
                        <tbody>
                        {% for formation in formations %}
                            <tr>
                                <td>
                                    <a href="{{ url('admin_formation_show', { id: formation.id }) }}">{{ formation.programme.reference }}</a>
                                </td>
                                <td>{{ formation.sessionNumber }}</td>
                                <td>{{ formation.participations|length }}</td>
                                <td>{{ formation.programme.title }}</td>
                                <td>
                                    {% if formation.searchStartDate|date('d/m/Y') == formation.searchEndDate|date('d/m/Y') %}
                                        {{ formation.searchStartDate | date('d/m/Y') }}
                                    {% else %}
                                        Du {{ formation.searchStartDate | date('d/m/Y') }}<br> au {{ formation.searchEndDate | date('d/m/Y') }}
                                    {% endif %}
                                </td>
                                <td>
                                    {% if formation.topoFiles %}
                                        {% for file in formation.topoFiles %}
                                            {% if file.topoFile is not null %}
                                                <div>
                                                    {% set name = "admin.formation.topo"|trans %}
                                                    {% if file.topoOriginalName %}
                                                        {% set name = file.topoOriginalName %}
                                                    {% endif %}
                                                    <a target="_blank" title="{{ file.topoOriginalName }}" href="{{ url('admin_topo_file', {'id' : file.id, 'fileField' : 'topoFile'}) }}">{{ name }}</a>
                                                </div>
                                            {% endif %}
                                        {% endfor %}
                                    {% endif %}
                                </td>
                                <td class="text-center">
                                    <a href="#" class="mbn sessionsListLink btn btn-eduprat"><i class="fa fa-eye"></i> Liste des participants <i class="fa fa-angle-right"></i></a>
                                </td>
                            </tr>
                            <tr class="sessionsWrap">
                                <td colspan="7">
                                    {% if formation.participations|length %}
                                        <div class="sessionsList">
                                            <table class="table table-bordered table-responsive table-hover">
                                                <tr>
                                                    <th>{{ "admin.participant.firstname"|trans }}</th>
                                                    <th>{{ "admin.participant.lastname"|trans }}</th>
                                                    <th>{{ "admin.participant.created"|trans }}</th>
                                                    <th class="text-center" width="25%">Parcours</th>
                                                    {% if not formation.programme.isFormatPresentiel and formation.attestationMandatory %}
                                                        <th width="8%">Attestations</th>
                                                    {% endif %}
                                                    {# <th class="text-center">{{ ('front.label.' ~ formation.displayType ~ '.plural') | trans | capitalize }}</th> #}
                                                    <th>{{ "admin.global.actions"|trans }}</th>
                                                </tr>
                                                {% set displayAll = formation.coordinators|length == 1 %}
                                                {% for participation in  formation.participations %}
                                                    {% if (displayAll or (participation.coordinator and participation.coordinator.person == user)) %}
                                                    {% set course = courseManager.getCourseDetail(participation) %}
                                                    {% set isBeforeElearningUpdate = courseManager.isBeforeElearningUpdate(participation) %}
                                                    {% set formCompleted1 = participation|auditCompleted(1) %}
                                                    {% set formCompleted2 = participation|auditCompleted(2) %}
                                                    <tr>
                                                        <td>{{ participation.participant.firstname }}</td>
                                                        <td>{{ participation.participant.lastname }}</td>
                                                        <td class="text-center">
                                                            {% if participation.participant.user and participation.participant.user.hasCreatedPassword %}
                                                                <img src="{{ asset('img/yes.png') }}" width="20" height="20" alt="OUI" />
                                                            {% else %}
                                                                <img src="{{ asset('img/no.png') }}" width="20" height="20" alt="NON" />
                                                            {% endif %}
                                                        </td>
                                                        <td class="text-center">
                                                            {% if formation.hasLinkedForm and course %}
                                                                <div style="display:flex; justify-content:center; align-items: center;">
                                                                {% if formation.hasCourse() and not formation.hasFormPost() %}
                                                                    {% if participation.courseEnded %}
                                                                        <img src="{{ asset('img/yes.png') }}" width="20" height="20" alt="OUI" />
                                                                    {% else %}
                                                                        <img src="{{ asset('img/no.png') }}" width="20" height="20" alt="OUI" />
                                                                    {% endif %}
                                                                {% else %}
                                                                    <div class="pre-session-group" style="margin-left:10px">
                                                                        Pré
                                                                        <div style="margin-left:6px">
                                                                            {% if formation.isElearning and isBeforeElearningUpdate %}
                                                                                {% if formCompleted1 %}
                                                                                    <img src="{{ asset('img/yes.png') }}" width="20" height="20" alt="OUI" />
                                                                                {% else %}
                                                                                    <img src="{{ asset('img/no.png') }}" width="20" height="20" alt="OUI" />
                                                                                {% endif %}
                                                                            {% else %}
                                                                                {% if formation.isElearning %}
                                                                                    {% if participation.isStepCompleted("reunion") %}
                                                                                        <img src="{{ asset('img/yes.png') }}" width="20" height="20" alt="OUI" />
                                                                                    {% else %}
                                                                                        <img src="{{ asset('img/no.png') }}" width="20" height="20" alt="OUI" />
                                                                                    {% endif %}
                                                                                {% else %}
                                                                                    {% if course["step_1"].completed %}
                                                                                        <img src="{{ asset('img/yes.png') }}" width="20" height="20" alt="OUI" />
                                                                                    {% else %}
                                                                                        <img src="{{ asset('img/no.png') }}" width="20" height="20" alt="OUI" />
                                                                                    {% endif %}
                                                                                {% endif %}
                                                                            {% endif %}
                                                                        </div>
                                                                    </div>

                                                                    <div class="separator"></div>

                                                                    <div class="post-session-group">
                                                                        Post
                                                                        <div style="margin-left:6px">
                                                                            {% if courseManager.hasModule(course, "synthese") %}
                                                                                {% if participation.isStepCompleted("synthese") %}
                                                                                    <img src="{{ asset('img/yes.png') }}" width="20" height="20" alt="OUI" />
                                                                                {% else %}
                                                                                    <img src="{{ asset('img/no.png') }}" width="20" height="20" alt="OUI" />
                                                                                {% endif %}
                                                                            {% else %}
                                                                                {% if participation.formation.openingDate|date("Y-m-d") >= parcours_migration_date ? participation.isStepCompleted("end") : participation.completedForms %}
                                                                                    <img src="{{ asset('img/yes.png') }}" width="20" height="20" alt="OUI" />
                                                                                {% else %}
                                                                                    <img src="{{ asset('img/no.png') }}" width="20" height="20" alt="OUI" />
                                                                                {% endif %}
                                                                            {% endif %}
                                                                        </div>
                                                                    </div>
                                                                {% endif %}
                                                                <a href="#" data-participationid="{{ participation.id }}" style="margin-left:10px; background-color: #109199 !important;"class="parcoursListLink btn btn-eduprat btn-icon"><i class="fa fa-pencil"></i><span class="edit-text"></span></a>
                                                            </div>
                                                            {% endif %}
                                                        </td>
                                                            {% if not formation.programme.isFormatPresentiel and formation.attestationMandatory %}
                                                                <td>
                                                                    <div class="flex" style="align-items: center;">
                                                                        <div>
                                                                            {% if participation.formation.isPluriAnnuelle() and participation.formation.containOffline(participation.formation.closingDate|date('Y')) %}
                                                                                {{ participation.formation.openingDate|date('Y') }}
                                                                                {% if participation.attestationHonneur %}
                                                                                    <img src="{{ asset('img/yes.png') }}" width="15" height="15" alt="OUI" /><br>
                                                                                {% else %}
                                                                                    <img src="{{ asset('img/no.png') }}" width="15" height="15" alt="NON" /><br>
                                                                                {% endif %}
                                                                                {{ participation.formation.closingDate|date('Y') }}
                                                                                {% if participation.attestationHonneurN1 %}
                                                                                    <img src="{{ asset('img/yes.png') }}" width="15" height="15" alt="OUI" /><br>
                                                                                {% else %}
                                                                                    <img src="{{ asset('img/no.png') }}" width="15" height="15" alt="NON" /><br>
                                                                                {% endif %}
                                                                            {% else %}
                                                                                {% if participation.attestationHonneur %}
                                                                                    <img src="{{ asset('img/yes.png') }}" width="20" height="20" alt="OUI" />
                                                                                {% else %}
                                                                                    <img src="{{ asset('img/no.png') }}" width="20" height="20" alt="NON" />
                                                                                {% endif %}
                                                                            {% endif %}
                                                                        </div>
                                                                        <div>
                                                                            {% if is_granted('ROLE_SUPERVISOR') %}
                                                                                <a href="#" data-participationid="{{ participation.id }}" style="margin-left:10px; background-color: #109199 !important;"class="attestationListLink btn btn-eduprat btn-icon"><i class="fa fa-pencil"></i><span class="edit-text"></span></a>
                                                                            {% elseif app.user.isCoordinator %}
                                                                                {% if participation.participant.coordinator == app.user %}
                                                                                    <a href="#" data-participationid="{{ participation.id }}" style="margin-left:10px; background-color: #109199 !important;"class="attestationListLink btn btn-eduprat btn-icon"><i class="fa fa-pencil"></i><span class="edit-text"></span></a>
                                                                                {% endif %}
                                                                            {% endif %}
                                                                        </div>
                                                                    </div>
                                                                </td>
                                                            {% endif %}
                                                            <td class="text-center">
                                                                {% if formation.hasLinkedForm %}
                                                                    <a href="#" class="btn btn-eduprat btn-icon btn-email" title="{{ 'admin.formation.mail.btn'|trans }}"><i class="fa fa-envelope"></i></a>
                                                                {% endif %}
                                                                <a href="{{ url('admin_participant_show', {'id': participation.participant.id }) }}" class="mbn btn btn-eduprat" title="{{ 'admin.global.more'|trans }}"><i class="fa fa-eye"></i>{{ 'admin.global.more'|trans }}</a>
                                                            </td>
                                                        </tr>
                                                        {% set displayN1Attestation = formation.isPluriannuelle()  and formation.containOffline(formation.closingDate|date('Y')) %}
                                                        {% if not formation.programme.isFormatPresentiel and formation.attestationMandatory %}
                                                            <tr class="sessionsWrap" id="attestation-{{ participation.id }}">
                                                                <td colspan="10">
                                                                    <div class="attestationList" style="display: none">
                                                                        <div class="collaps-list collaps-list-attestation">
                                                                            <i class="fa fa-circle btn-attestation btn-attestation-circle"></i><a style="margin-left: 2px !important;" target="_blank" class="download-file" href="{{ url('pdf_attestation_honneur_pdf', { id: participation.formation.id, token: participation.formation.token, participant: participation.participant.id, person : "null" }) }}">{{ 'admin.participation.attestation_honneur.generate'|trans }} {% if displayN1Attestation %} de {{ formation.openingDate|date('Y')}}{% endif %}</a>
                                                                            {% if displayN1Attestation %}
                                                                                <br>
                                                                                <i class="fa fa-circle btn-attestation btn-attestation-circle"></i><a style="margin-left: 2px !important;" target="_blank" class="download-file" href="{{ url('pdf_attestation_honneur_pdf', { id: participation.formation.id, token: participation.formation.token, participant: participation.participant.id, person : "null", n1 : "true" }) }}">{{ 'admin.participation.attestation_honneur.generate'|trans }} de {{ (formation.closingDate|date('Y')) }}</a>
                                                                            {% endif %}
                                                                            <br>
                                                                            <div class="btn-download-file-attestation" data-attestation="{% if participation.attestationHonneur %}true{%else%}false{%endif%}" id="attestationHonneurFile_{{ participation.id }}">
                                                                                <i class="fa fa-download  btn-attestation" ></i>
                                                                                <div class="import-attestation">{{ 'admin.participation.attestation_honneur.import'|trans }}{% if displayN1Attestation %} de {{ participation.formation.openingDate|date('Y')}}{% endif %}</div>
                                                                            </div>
                                                                            {% if displayN1Attestation %}
                                                                                <div class="btn-download-file-attestation" data-attestation="{% if participation.attestationHonneurN1 %}true{%else%}false{%endif%}" id="attestationHonneurFileN1_{{ participation.id }}">
                                                                                    <i class="fa fa-download  btn-attestation" ></i>
                                                                                    <div class="import-attestation">{{ 'admin.participation.attestation_honneur.import'|trans }} de {{ participation.formation.closingDate|date('Y')}}</div>
                                                                                </div>
                                                                            {% endif %}
                                                                            {% if participation.attestationHonneur %}
                                                                                <i class="fa fa-eye btn-attestation"></i><a target="_blank" style="margin-left: 4px !important;" class="downloadFile" href="{{ url('admin_attestation_file', {'id' : participation.id }) }}">{{ 'admin.participation.attestation_honneur.view'|trans }}{% if displayN1Attestation %} de {{ participation.formation.openingDate|date('Y')}}{% endif %}</a><br>
                                                                                <i class="fa fa-trash-o btn-attestation"></i><a style="margin-left: 4px !important;" class="downloadFile delete-attestation" href="{{ url('admin_attestation_delete_file', {'id' : participation.id, redirect: app.request.uri ~ "#row-participation-" ~ participation.id }) }}">{{ 'admin.participation.attestation_honneur.delete'|trans }}{% if displayN1Attestation %} de {{ participation.formation.openingDate|date('Y')}}{% endif %}</a><br>
                                                                            {% endif %}
                                                                            {% if displayN1Attestation and participation.attestationHonneurN1%}
                                                                                <i class="fa fa-eye btn-attestation"></i><a target="_blank" style="margin-left: 4px !important;" class="downloadFile" href="{{ url('admin_attestation_file', {'id' : participation.id, n1: true}) }}">{{ 'admin.participation.attestation_honneur.view'|trans }} de {{ participation.formation.closingDate|date('Y')}}</a><br>
                                                                                <i class="fa fa-trash-o btn-attestation"></i><a style="margin-left: 4px !important;" class="downloadFile delete-attestation" href="{{ url('admin_attestation_delete_file', {'id' : participation.id, n1: true, redirect: app.request.uri ~ "#row-participation-" ~ participation.id }) }}">{{ 'admin.participation.attestation_honneur.delete'|trans }} de {{ participation.formation.closingDate|date('Y')}}</a><br>
                                                                            {% endif %}
                                                                            <div id="attestationHonneurFile" class="downloadFile">
                                                                                {{ form_start(forms_attestation_honneur[participation.id],{'attr': {'class': 'formDownloadFile'}}) }}
                                                                                {{ form_widget(forms_attestation_honneur[participation.id].attestationHonneurFile) }}
                                                                                {{ form_end(forms_attestation_honneur[participation.id]) }}
                                                                                {% if displayN1Attestation %}
                                                                                    {{ form_start(forms_attestation_honneurN1[participation.id],{'attr': {'class': 'formDownloadFile'}}) }}
                                                                                    {{ form_widget(forms_attestation_honneurN1[participation.id].attestationHonneurFileN1) }}
                                                                                    {{ form_end(forms_attestation_honneurN1[participation.id]) }}
                                                                                {% endif %}
                                                                            </div>
                                                                        </div>
                                                                    </div>
                                                                </td>
                                                            </tr>
                                                        {% endif %}
                                                        <tr class="sessionsWrap" id="parcours-{{ participation.id }}">
                                                            {% set oldCourse = courseManager.oldCourse(participation) %}
                                                            {# {% set course = courseManager.getCourseDetail(participation) %} #}
                                                            {% if formation.hasLinkedForm %}
                                                                {% set formCompleted1 = participation|auditCompleted(1) %}
                                                                {% set formCompleted2 = participation|auditCompleted(2) %}
                                                            {% endif %}
                                                            {% set courseType = courseManager.getCourseType(participation) %}
                                                            {% set participantActionSheet = participantActionSheetList[participation.id] is defined ? participantActionSheetList[participation.id] : false %}
                                                            <td colspan="10">
                                                                <div class="parcoursList" style="display: none">
                                                                    <div class="collaps-list collaps-list-attestation">
                                                                        <div class="row">
                                                                            {% include "admin/formation/modules_list.html.twig" %}
                                                                        </div>
                                                                    </div>
                                                                </div>
                                                            </td>
                                                        </tr>
                                                        {% set formPostAvailable = ("now"|date("Y-m-d")) <= formation.formClosingDate|date("Y-m-d") %}
                                                        {% include "admin/formation/emails.html.twig" %}
                                                    {% endif %}
                                                {% endfor %}
                                            </table>
                                        </div>
                                    {% endif %}
                                </td>
                            </tr>
                        {% endfor %}
                        </tbody>
                    </table>
                    <div class="row">
                        <div class="col-sm-12">
                            {% if npages > 1 %}
                                {% include '@AlienorApi/Includes/paginationBootstrap.html.twig' with {urlName : search_route, extraParams : {id: user.id} } %}
                            {% endif %}
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
{% endblock %}

{% block javascripts %}

    <script type="text/javascript">
        $(document).ready(function() {
            $('.btn-download-file').click(function () {
                $('form[name="'+$(this).attr('id')+'"]').find('input').click();
            });

            $('.btn-download-file-attestation').click(function () {
                if($(this).data("attestation")) {
                    if(confirm("En choisissant un fichier, vous écraserez l'attestation actuelle")) {
                        $('form[name="'+$(this).attr('id')+'"]').find('input').click();
                    }
                } else {
                    $('form[name="'+$(this).attr('id')+'"]').find('input').click();
                }
            });

            $('.delete-attestation').click(function () {
                if(confirm("Supprimer l'attestation ?")) {
                    return true;
                } else {
                    return false;
                }
            });

            $('form.formDownloadFile').on('change', 'input[type="file"]', function () {
                $(this).closest("form").submit();
            });

            $(document).on('click', '.btn-email', function (e) {
                e.preventDefault();
                $(this).closest('tr').nextAll('tr.row-email').first().toggleClass('hide');
            });

        });
    </script>

{% endblock %}
