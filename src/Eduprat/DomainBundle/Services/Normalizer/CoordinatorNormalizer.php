<?php

namespace Eduprat\DomainBundle\Services\Normalizer;

use Alienor\ElasticBundle\Services\Normalizer\ObjectNormalizer;
use Ed<PERSON>rat\AdminBundle\Entity\Person;
use Eduprat\DomainBundle\Entity\Coordinator;
use Symfony\Component\Asset\Packages;
use Symfony\Component\PropertyAccess\PropertyAccessorInterface;
use Symfony\Component\Serializer\Mapping\Factory\ClassMetadataFactoryInterface;
use Symfony\Component\Serializer\NameConverter\NameConverterInterface;
use Vich\UploaderBundle\Templating\Helper\UploaderHelper;

class CoordinatorNormalizer extends ObjectNormalizer
{
    /**
     * @var \DateTime
     */
    protected $migrationDate;

    /**
     * @var UploaderHelper
     */
    private $uploaderHelper;

    /**
     * @var Packages
     */
    private $packages;

    public function __construct(ClassMetadataFactoryInterface $classMetadataFactory = null, NameConverterInterface $nameConverter = null, PropertyAccessorInterface $propertyAccessor = null, $migrationDate, Packages $packages, UploaderHelper $uploaderHelper)
    {
        parent::__construct($classMetadataFactory, $nameConverter, $propertyAccessor);
        $this->migrationDate = new \DateTime($migrationDate);
        $this->uploaderHelper = $uploaderHelper;
        $this->packages = $packages;
    }


    public function supportsDenormalization($data, $type, $format = null, array $context = []): bool
    {
        return $type == Coordinator::class;
    }

    public function supportsNormalization($data, $format = null, array $context = []): bool
    {
        return $data instanceof Coordinator;
    }

    /**
     * @param Coordinator $object
     */
    public function normalize($object, ?string $format = null, array $context = []): array
    {
        $data = parent::normalize($object, $format, $context);
        if (!in_array("apiV2", $context["groups"]) && $object->getFormation()) {
            if ($this->migrationDate < $object->getFormation()->getStartDate()) {
                $data["honorary"] = $object->getCalculatedHonorary();
            } else {
                $data["avancesCost"] = $object->getRestaurationHonorary();
                $data["formationCost"] = $object->getFormation()->getFormerHonorary();
                $data["marges"] = null;
            }
        }
        if (in_array("apiV2", $context["groups"])) {
            if ($object->getPerson()->getAvatar()) {
                $image = $this->uploaderHelper->asset($object->getPerson(), 'avatarFile', Person::class);
            } else {
                $image = "img/default-avatar.png";
            }
            $data["avatar"] = $this->packages->getUrl($image);
        }
        return $data;
    }


    /**
     * @param Coordinator $object
     * @param mixed $data
     * @param string|null $format
     * @param array $context
     * @return Coordinator
     */
    protected function postDenormalize($object, $data, $format = null, array $context = array())
    {
        return $object;
    }

    public function getClass()
    {
        return Coordinator::class;
    }

    public function getSupportedTypes(?string $format): array
    {
        return array(Coordinator::class => false);
    }

}