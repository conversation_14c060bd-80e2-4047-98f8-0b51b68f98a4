<?php

namespace Eduprat\DomainBundle\Services;

class SpecialitesService {

    const CATEGORIES = array(
        "Audioprothésiste" => "1",
        "Biologiste" => "2",
        "Chirurgien – dentiste" => "3",
        "Diététicien" => "4",
        "Ergothérapeute" => "5",
        "Infirmier" => "6",
        "Manipulateur d’électroradiologie médicale (ERM)" => "7",
        "Masseur-kinésithérapeute" => "8",
        "Médecin" => "9",
        "Orthophoniste" => "10",
        "Orthoprothésiste" => "11",
        "Orthoptiste" => "12",
        "Orthopédiste-orthésiste" => "13",
        "Orthésiste" => "14",
        "Pharmacien" => "15",
        "Podo-orthésiste" => "16",
        "Prothésiste" => "17",
        "Préparateur en pharmacie" => "18",
        "Psychomotricien" => "19",
        "Pédicure-podologue" => "20",
        "Sages-Femmes" => "21",
        "Technicien de laboratoire médical" => "22",
        "Opticien-lunetier" => "23",
        "Cadre de santé" => "24",
        "Psychothérapeute" => "25",
        "Psychologue" => "26",
        "Secrétaire" => "27",
        "Etudiant" => "28",
        "Autre" => "29",
        "Educateur" => "30",
        "Accompagnant éducatif et social" => "31",
        "Agent de Services Hospitaliers" => "32",
        "Aide médico-psychologique" => "33",
        "Personnel soignant" => "34",
        "Auxiliaire de vie" => "35",
    );
    // Laboratoire, Chiropracteur, Ostéopathe, Dermatologie et vénérologie, Médecine d'urgence, Masseur-kinésithérapeute
    const SPECIALITES = array(
        "Chirurgie dentaire (specialiste Orthopédie Dento-Faciale)" => "1",
        "Aide-soignant" => "2",
        "Anatomie et cytologie pathologiques" => "3",
        "Anesthésie-réanimation" => "4",
        "Audioprothésiste" => "5",
        "Cardiologie et maladies vasculaires / Pathologies cardio-vasculaire" => "6",
        "Chirurgie de la face et du cou" => "7",
        "Chirurgie dentaire (omnipraticiens)" => "8",
        "Chirurgie dentiste spécialisé en chirurgie orale" => "9",
        "Chirurgie dentiste spécialisé en médecine bucco dentaire" => "10",
        "Chirurgie générale" => "11",
        "Chirurgie infantile" => "12",
        "Chirurgie maxillo-faciale" => "13",
        "Chirurgie maxillo-faciale et stomatologie" => "14",
        "Chirurgie orale" => "15",
        "Chirurgie orthopédique et traumatologique" => "16",
        "Chirurgie plastique reconstructrice et esthétique" => "17",
        "Chirurgie pédiatrique" => "18",
        "Chirurgie thoracique et cardio-vasculaire" => "19",
        "Chirurgie urologique" => "20",
        "Chirurgie vasculaire" => "21",
        "Chirurgie viscérale et digestive" => "22",
        "Dermatologie et vénérologie" => "23",
        "Diététicien" => "24",
        "Endocrinologie diabétologie et maladies métaboliques" => "25",
        "Endocrinologie diabétologie et nutrition" => "26",
        "Endocrinologie et métabolismes" => "27",
        "Ergothérapeute" => "28",
        "Gastro-entérologie et hépatologie" => "29",
        "Gynécologie médicale" => "30",
        "Gynécologie médicale et obstétrique" => "31",
        "Gynécologie obstétrique" => "32",
        "Génétique médicale" => "33",
        "Gériatrie / Gérontologie" => "34",
        "Hépato-gastro-entérologie" => "35",
        "Infirmier Anesthésiste Diplômé d’Etat (IADE)" => "36",
        "Infirmier de Bloc Opératoire Diplômé d’Etat (IBODE)" => "37",
        "Infirmier Diplômé d’Etat (IDE)" => "38",
        "Infirmier Puéricultrice Diplômée d’Etat" => "39",
        "Maladies infectieuses et tropicales" => "40",
        "Manipulateur d’électroradiologie médicale (ERM)" => "41",
        "Masseur-kinésithérapeute" => "42",
        "Médecin" => "43",
        "Allergologie" => "44",
        "Médecine cardiovasculaire" => "45",
        "Médecine et santé au travail" => "46",
        "Médecine d’urgence" => "47",
        "Médecine générale" => "48",
        "Médecine intensive-réanimation" => "49",
        "Médecine légale et expertises médicale" => "50",
        "Médecine nucléaire" => "51",
        "Médecine physique et réadaptation" => "52",
        "Médecine vasculaire" => "53",
        "Neurochirurgie" => "54",
        "Neurologie" => "55",
        "Neuropsychiatrie" => "56",
        "Néphrologie" => "57",
        "Oncologie" => "58",
        "Oncologie radiothérapique" => "59",
        "Ophtalmologie" => "60",
        "Orthophoniste" => "61",
        "Orthoprothésiste" => "62",
        "Orthoptiste" => "63",
        "Orthopédiste-orthésiste" => "64",
        "Orthésiste" => "65",
        "Oto-rhino-laryngologie - chirurgie cervico-faciale" => "66",
        "Pharmacien" => "67",
        "Pharmacien adjoint d’officine" => "68",
        "Pharmacien hospitalier" => "69",
        "Pharmacien titulaire d’officine" => "70",
        "Pneumologie" => "71",
        "Podo-orthésiste" => "72",
        "Prothésiste" => "73",
        "Préparateur en pharmacie" => "74",
        "Psychiatrie de l’enfant et de l’adolescent" => "75",
        "Psychiatrie" => "76",
        "Psychomotricien" => "77",
        "Pédiatrie" => "78",
        "Pédicure-podologue" => "79",
        "Radiodiagnostic et imagerie médicale" => "80",
        "Radiologie et imagerie médicale" => "81",
        "Radiothérapie" => "82",
        "Rhumatologie" => "83",
        "Réanimation médicale" => "84",
        "Sages-Femmes" => "85",
        "Santé publique" => "86",
        "Santé publique et médecine sociale" => "87",
        "Stomatologie" => "88",
        "Technicien de laboratoire médical" => "89",
        "Urologie" => "90",
        "Médecin PMI" => "91",
        "Médecine interne et immunologie clinique" => "92",
        "Auxiliaire de puériculture" => "93",
        "Opticien-lunetier" => "94",
        "Cadre de santé" => "95",
        "Psychothérapeute" => "96",
        "Psychologue" => "97",
        "Secrétaire" => "98",
        "Interne" => "99",
        "Externe" => "100",
        "Autre" => "101",
        "Hématologie" => "102",
        "Biologie médicale" => "103",
        "Educateur" => "104",
        "Accompagnant éducatif et social" => "105",
        "Agent de Services Hospitaliers" => "106",
        "Aide médico-psychologique" => "107",
        "Personnel soignant" => "108",
        "Auxiliaire de vie" => "109",
        "Infirmier en pratique avancée (IPA)" => "110",
    );

    public function __construct() {

    }

    public function getSpecialiteSib($specialite) {
        return isset(self::SPECIALITES[$specialite]) ? self::SPECIALITES[$specialite] : null;
    }

    public function getCategorieSib($categorie) {
        return isset(self::CATEGORIES[$categorie]) ? self::CATEGORIES[$categorie] : null;
    }
}
