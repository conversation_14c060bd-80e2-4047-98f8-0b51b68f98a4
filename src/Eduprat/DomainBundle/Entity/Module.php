<?php

namespace Eduprat\DomainBundle\Entity;

use Doctrine\DBAL\Types\Types;
use Doctrine\ORM\Mapping as ORM;

/**
 * module
 */
#[ORM\Entity]
#[ORM\HasLifecycleCallbacks]
#[ORM\Table(name: 'module')]
class Module
{

    /**
     * @var int
     */
    #[ORM\Column(name: 'id', type: Types::INTEGER)]
    #[ORM\Id]
    #[ORM\GeneratedValue(strategy: 'AUTO')]
    private ?int $id = null;

    /**
     * @var Step
     */
    #[ORM\ManyToOne(targetEntity: 'Eduprat\DomainBundle\Entity\Step', inversedBy: 'modules', cascade: ['persist'], fetch: 'EAGER')]
    #[ORM\JoinColumn(name: 'step', nullable: true)]
    private ?Step $step = null;

    /**
     * @var string
     */
    #[ORM\Column(name: 'label', type: Types::STRING, length: 255)]
    private ?string $label = null;

    /**
     * @var string
     */
    #[ORM\Column(name: 'icon', type: Types::STRING, length: 255, nullable: true)]
    private ?string $icon = null;

    /**
     * @var string
     */
    #[ORM\Column(name: 'route', type: Types::STRING, length: 255, nullable: true)]
    private ?string $route = null;

    /**
     * @var string
     */
    #[ORM\Column(name: 'routeParams', type: Types::JSON, nullable: true)]
    private $routeParams;
    
    /**
     * @var boolean
     */
    #[ORM\Column(name: 'isEndModule', type: Types::BOOLEAN)]
    private ?bool $isEndModule = null;
    
    /**
     * @var boolean
     */
    #[ORM\Column(name: 'isMandatory', type: Types::BOOLEAN)]
    private ?bool $isMandatory = null;
    
    /**
     * @var boolean
     */
    #[ORM\Column(name: 'accessibleIfCompleted', type: Types::BOOLEAN, nullable: true)]
    private ?bool $accessibleIfCompleted = null;

    /**
     * @var integer
     */
    #[ORM\Column(name: 'position', type: Types::INTEGER, nullable: true)]
    private ?int $position = null;

    /**
     * @var boolean
     */
    #[ORM\Column(name: 'valueProgression', type: Types::BOOLEAN, nullable: true)]
    private ?bool $valueProgression = null;

    public function __construct(array $arrayModule, bool $isMandatory, bool $isEndModule, int $position)
    {
        $this->label = isset($arrayModule["id"]) ? $arrayModule["id"] : null;
        $this->icon = isset($arrayModule["icon"]) ? $arrayModule["icon"] : null;
        $this->route = isset($arrayModule["route"]) ? $arrayModule["route"] : null;
        $this->routeParams = isset($arrayModule["route_params"]) ? $arrayModule["route_params"] : null;
        $this->accessibleIfCompleted = isset($arrayModule["accessible_if_completed"]) ? $arrayModule["accessible_if_completed"] : null;
        $this->isMandatory = $isMandatory;
        $this->isEndModule = $isEndModule;
        $this->position = $position;
        $this->valueProgression = isset($arrayModule["valueProgression"]) ? $arrayModule["valueProgression"] : null;
    }
    
    public function getId()
    {
        return $this->id;
    }

    public function getStep(): Step
    {
        return $this->step;
    }

    public function setStep(Step $step = null): self
    {
        $this->step = $step;
        return $this;
    }

    public function setLabel($label): self
    {
        $this->label = $label;
        return $this;
    }

    public function getLabel(): string
    {
        return $this->label;
    }

    public function setIcon($icon): self
    {
        $this->icon = $icon;
        return $this;
    }

    public function getIcon(): string
    {
        return $this->icon;
    }

    public function setRoute($route): self
    {
        $this->route = $route;
        return $this;
    }

    public function getRoute(): string
    {
        return $this->route;
    }

    public function setRouteParams(array $routeParams): self
    {
        $this->routeParams = $routeParams;
        return $this;
    }

    public function getRouteParams(): ?array
    {
        return $this->routeParams;
    }

    public function setIsAccessibleIfCompleted($value): self
    {
        $this->accessibleIfCompleted = $value;
        return $this;
    }

    public function isAccessibleIfCompleted(): ?bool
    {
        return $this->accessibleIfCompleted;
    }

    public function setPosition($position): self
    {
        $this->position = $position;
        return $this;
    }
    
    public function getPosition(): int
    {
        return $this->position;
    }

    public function setIsMandatory($value): self
    {
        $this->isMandatory = $value;
        return $this;
    }

    public function isMandatory(): bool
    {
        return $this->isMandatory;
    }

    public function setIsEndModule($value): self
    {
        $this->isEndModule = $value;
        return $this;
    }

    public function isEndModule(): bool
    {
        return $this->isEndModule;
    }

    public function toArray(): array
    {
        return [
            "id" => $this->label,
            "icon" => $this->icon,
            "route" => $this->route,
            "route_params" => $this->getRouteParams(),
            "accessible_if_completed" => $this->isAccessibleIfCompleted(),
            "isMandatory" => $this->isMandatory,
            "valueProgression" => $this->valueProgression
        ];
    }
}