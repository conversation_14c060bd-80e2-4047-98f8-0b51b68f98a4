<?php

namespace Eduprat\DomainBundle\Entity;

use Doctrine\DBAL\Types\Types;
use Doctrine\ORM\Mapping as ORM;
use Eduprat\AdminBundle\Entity\Person;

/**
 * LeadHistory
 */
#[ORM\Entity(repositoryClass: 'Eduprat\DomainBundle\Repository\LeadHistoryRepository')]
#[ORM\Table(name: 'lead_history')]
#[ORM\HasLifecycleCallbacks()]
class LeadHistory
{
    /**
     * @var int
     */
    #[ORM\Column(name: 'id', type: Types::INTEGER)]
    #[ORM\Id]
    #[ORM\GeneratedValue(strategy: 'AUTO')]
    private ?int $id = null;

    /**
     * @var Participant
     */
    #[ORM\ManyToOne(targetEntity: 'Eduprat\DomainBundle\Entity\Participant', inversedBy: 'participations', cascade: ['persist'])]
    #[ORM\JoinColumn(name: 'participant', nullable: false)]
    private ?Participant $participant = null;

    /**
     * @var Person
     */
    #[ORM\ManyToOne(targetEntity: 'Eduprat\AdminBundle\Entity\Person', cascade: ['persist'])]
    #[ORM\JoinColumn(name: 'leadReferent', nullable: true)]
    private ?Person $leadReferent = null;

    /**
     * @var Person
     */
    #[ORM\ManyToOne(targetEntity: 'Eduprat\AdminBundle\Entity\Person', cascade: ['persist'])]
    #[ORM\JoinColumn(name: 'advisor', nullable: true)]
    private ?Person $advisor = null;

    /**
     * @var string
     */
    #[ORM\Column(name: 'leadType', type: Types::STRING, nullable: true)]
    private ?string $leadType = null;

    /**
     * @var string
     */
    #[ORM\Column(name: 'partenariat', type: Types::STRING, nullable: true)]
    private ?string $partenariat = null;

    /**
     * @var string
     */
    #[ORM\Column(name: 'leadStatus', type: Types::STRING, nullable: true)]
    private ?string $leadStatus = null;

    /**
     * @var \DateTimeInterface
     */
    #[ORM\Column(name: 'leadCreationDate', type: Types::DATE_MUTABLE, nullable: true)]
    private ?\DateTimeInterface $leadCreationDate = null;

    /**
     * @var string
     */
    #[ORM\Column(name: 'leadContactDate', type: Types::STRING, nullable: true)]
    private ?string $leadContactDate = null;

    /**
     * @var string
     */
    #[ORM\Column(name: 'leadComment', type: Types::TEXT, nullable: true)]
    private ?string $leadComment = null;

    /**
     * @var string
     */
    #[ORM\Column(name: 'gpmMemberNumber', type: Types::STRING, nullable: true)]
    private ?string $gpmMemberNumber = null;

    /**
     * @var string
     */
    #[ORM\Column(name: 'leadState', type: Types::STRING, nullable: true)]
    private ?string $leadState = null;

    /**
     * @var string
     */
    #[ORM\Column(name: 'leadCommentEduprat', type: Types::STRING, nullable: true)]
    private ?string $leadCommentEduprat = null;

    /**
     * Constructor
     */
    public function __construct(Participant $participant)
    {
        $this->participant = $participant;
        $this->leadReferent = $participant->getLeadReferent();
        $this->advisor = $participant->getAdvisor();
        $this->leadType = $participant->getLeadType();
        $this->partenariat = $participant->getPartenariat();
        $this->leadStatus = $participant->getLeadStatus();
        $this->leadCreationDate = $participant->getLeadCreationDate();
        $this->leadContactDate = $participant->getLeadContactDate();
        $this->leadComment = $participant->getLeadComment();
        $this->gpmMemberNumber = $participant->getGpmMemberNumber();
        $this->leadState = $participant->getLeadState();
        $this->leadCommentEduprat = $participant->getLeadCommentEduprat();
        $this->participant->addLeadHistory($this);
    }

    public function getId()
    {
        return $this->id;
    }

    public function setId($id)
    {
        $this->id = $id;
        return $this;
    }

    public function getParticipant()
    {
        return $this->participant;
    }

    public function setParticipant(Participant $participant)
    {
        $this->participant = $participant;

        return $this;
    }

    /**
     * @return Person
     */
    public function getLeadReferent()
    {
        return $this->leadReferent;
    }

    /**
     * @param Person $leadReferent
     * @return LeadHistory
     */
    public function setLeadReferent($leadReferent)
    {
        $this->leadReferent = $leadReferent;
        return $this;
    }

    /**
     * @return Person
     */
    public function getAdvisor()
    {
        return $this->advisor;
    }

    /**
     * @param Person $advisor
     * @return LeadHistory
     */
    public function setAdvisor($advisor)
    {
        $this->advisor = $advisor;
        return $this;
    }

    /**
     * @param string $leadType
     */
    public function getLeadType() {
        return $this->leadType;
    }

    /**
     * @param string $leadType
     * @return LeadHistory
     */
    public function setLeadType($leadType) {
        $this->leadType = $leadType;
        return $this;
    }

    /**
     * @param string $partenariat
     */
    public function getPartenariat() {
        return $this->partenariat;
    }

    /**
     * @param string $partenariat
     * @return LeadHistory
     */
    public function setPartenariat($partenariat) {
        $this->partenariat = $partenariat;
        return $this;
    }

    /**
     * @param string $leadStatus
     */
    public function getLeadStatus() {
        return $this->leadStatus;
    }

    /**
     * @param string $leadStatus
     * @return LeadHistory
     */
    public function setLeadStatus($leadStatus) {
        $this->leadStatus = $leadStatus;
        return $this;
    }

    /**
     * Get leadCreationDate
     *
     * @return \DateTime
     */
    public function getLeadCreationDate()
    {
        return $this->leadCreationDate;
    }

    /**
     * Set leadCreationDate
     *
     * @param \DateTime $leadCreationDate
     * @return LeadHistory
     */
    public function setLeadCreationDate(\DateTime $leadCreationDate = null)
    {
        $this->leadCreationDate = $leadCreationDate;
        return $this;
    }

    /**
     * Get leadContactDate
     *
     * @return string
     */
    public function getLeadContactDate()
    {
        return $this->leadContactDate;
    }

    /**
     * Set leadContactDate
     *
     * @param string|null $leadContactDate
     * @return LeadHistory
     */
    public function setLeadContactDate(string $leadContactDate = null)
    {
        $this->leadContactDate = $leadContactDate;
        return $this;
    }

    /**
     * @param string $leadComment
     */
    public function getLeadComment() {
        return $this->leadComment;
    }

    /**
     * @param string $leadComment
     * @return LeadHistory
     */
    public function setLeadComment($leadComment) {
        $this->leadComment = $leadComment;
        return $this;
    }

    /**
     * @param string $gpmMemberNumber
     */
    public function getGpmMemberNumber() {
        return $this->gpmMemberNumber;
    }

    /**
     * @param string $gpmMemberNumber
     * @return LeadHistory
     */
    public function setGpmMemberNumber($gpmMemberNumber) {
        $this->gpmMemberNumber = $gpmMemberNumber;
        return $this;
    }

    /**
     * @param string $leadState
     */
    public function getLeadState() {
        return $this->leadState;
    }

    /**
     * @param string $leadState
     * @return LeadHistory
     */
    public function setLeadState($leadState) {
        $this->leadState = $leadState;
        return $this;
    }

     /**
     * @param string $leadCommentEduprat
     */
    public function getLeadCommentEduprat() {
        return $this->leadCommentEduprat;
    }

    /**
     * @param string $leadCommentEduprat
     * @return LeadHistory
     */
    public function setLeadCommentEduprat($leadCommentEduprat) {
        $this->leadCommentEduprat = $leadCommentEduprat;
        return $this;
    }

    public function __toArrayLead()
    {
        $previousDate = $this->getParticipant()->getLeadCreationDate();        
        foreach($this->getParticipant()->getLeadHistories() as $history) {
            if ($history->getLeadCreationDate() === $this->getLeadCreationDate()) {
                $startDate = $history->getLeadCreationDate();
                $endDate = $previousDate;
            } else {
                $previousDate = $history->getLeadCreationDate();
            }
        }
        $person = array();
        $person['region'] = $this->participant->getRegion();
        $person['lastname'] = $this->participant->getLastname();
        $person['firstname'] = $this->participant->getFirstname();
        $person['leadCreationDate'] = $this->getLeadCreationDate() ? $this->getLeadCreationDate()->format('d/m/Y') : null;
        $person['leadType'] = $this->getLeadType();
        $person['status'] = $this->getLeadStatus();
        $person['advisor'] = $this->getAdvisor() ? $this->getAdvisor()->getInvertedFullname() : null;
        $person['leadReferent'] = $this->getLeadReferent() ? $this->getLeadReferent()->getInvertedFullname() : null;
        $person['category'] = $this->participant->getCategory();
        $person['speciality'] = $this->participant->getSpeciality();
        $person['leadContactDate'] = $this->getLeadContactDate();
        $person['leadComment'] = $this->getLeadComment();
        $person['leadState'] = $this->getLeadState() ? $this->getLeadState() : "A traiter";
        $person['leadCommentEduprat'] = $this->getLeadCommentEduprat();
        $person['adressePost'] = $this->participant->getAddress();
        $person['zipCode'] = $this->participant->getZipCode();
        $person['city'] = $this->participant->getCity();
        $person['email'] = $this->participant->getEmail();
        $person['phone'] = $this->participant->getPhone();
        $person['type'] = $this->participant->isProspect() ? "Prospect" : "Participant";
        $person['exerciseMode'] = $this->participant->getExerciceMode();
        $person['inscriptions'] = count($this->participant->getLeadParticipations($startDate, $endDate));
        $person['endParcours'] = $this->participant->getParticipationsCompletedCount();
        $person['rpps'] = $this->participant->getRpps();


        return $person;
    }
}