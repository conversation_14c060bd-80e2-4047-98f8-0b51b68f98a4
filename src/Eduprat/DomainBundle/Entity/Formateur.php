<?php

namespace Eduprat\DomainBundle\Entity;

use Doctrine\DBAL\Types\Types;
use Alienor\ElasticBundle\Model\ElasticableInterface;
use Alienor\UserBundle\Entity\Person;
use Doctrine\Common\Collections\ArrayCollection;
use Doctrine\Common\Collections\Collection;
use Doctrine\ORM\Mapping as ORM;

/**
 * Formateur
 */
#[ORM\Entity(repositoryClass: 'Eduprat\DomainBundle\Repository\FormateurRepository')]
#[ORM\Table(name: 'formateur')]
class Formateur implements ElasticableInterface
{
    CONST ELASTIC_INDEX = 'former';

    /**
     * @var int
     */
    #[ORM\Column(name: 'id', type: Types::INTEGER)]
    #[ORM\Id]
    #[ORM\GeneratedValue(strategy: 'AUTO')]
    private ?int $id = null;

    /**
     * @var Programme
     */
    #[ORM\ManyToOne(targetEntity: 'Eduprat\DomainBundle\Entity\Programme', inversedBy: 'formateurs', cascade: ['persist'], fetch: 'EAGER')]
    #[ORM\JoinColumn(name: 'programme', nullable: true)]
    private ?Programme $programme = null;

    /**
     *
     * @var Formation
     */
    #[ORM\ManyToOne(targetEntity: 'Eduprat\DomainBundle\Entity\Formation', inversedBy: 'formateurs', cascade: ['persist'])]
    #[ORM\JoinColumn(name: 'formation', nullable: true, onDelete: 'CASCADE')]
    private ?Formation $formation = null;

    /**
     * @var Person
     */
    #[ORM\ManyToOne(targetEntity: 'Eduprat\AdminBundle\Entity\Person', inversedBy: 'formers', cascade: ['persist'], fetch: 'EAGER')]
    #[ORM\JoinColumn(name: 'person', nullable: false)]
    private ?\Eduprat\AdminBundle\Entity\Person $person = null;

    /**
     * @var Collection<int, FormateurFiles>
     */
    #[ORM\OneToMany(targetEntity: 'Eduprat\DomainBundle\Entity\FormateurFiles', mappedBy: 'formateur', cascade: ['remove'], fetch: 'EAGER')]
    protected Collection $formateurFiles;

    /**
     * @var float
     */
    #[ORM\Column(name: 'honorary', type: Types::FLOAT, nullable: false)]
    protected float $honorary;

    /**
     * @var integer
     */
    #[ORM\Column(name: 'evaluationParticipantAnswersCount', type: Types::INTEGER, nullable: true)]
    private ?int $evaluationParticipantAnswersCount = null;

    /**
     * @var integer
     */
    #[ORM\Column(name: 'evaluationParticipantAnswersSum', type: Types::INTEGER, nullable: true)]
    private ?int $evaluationParticipantAnswersSum = null;

    /**
     * @var integer
     */
    #[ORM\Column(name: 'evaluationCoordinatorAnswersCount', type: Types::INTEGER, nullable: true)]
    private ?int $evaluationCoordinatorAnswersCount = null;

    /**
     * @var integer
     */
    #[ORM\Column(name: 'evaluationCoordinatorAnswersSum', type: Types::INTEGER, nullable: true)]
    private ?int $evaluationCoordinatorAnswersSum = null;

    /**
     * @var boolean
     */
    #[ORM\Column(name: 'is_Paid', type: Types::BOOLEAN, nullable: true)]
    protected ?bool $isPaid = false;
    
    /**
     * @var \DateTimeInterface
     */
    #[ORM\Column(name: 'paidDate', type: Types::DATETIME_MUTABLE, nullable: true)]
    private ?\DateTimeInterface $paidDate = null;

        /**
     * @var \DateTimeInterface
     */
    #[ORM\Column(name: 'startedAtZoom', type: Types::DATETIME_MUTABLE, nullable: true)]
    private ?\DateTimeInterface $startedAtZoom = null;

    /**
     * @var \DateTimeInterface
     */
    #[ORM\Column(name: 'finishedAtZoom', type: Types::DATETIME_MUTABLE, nullable: true)]
    private ?\DateTimeInterface $finishedAtZoom = null;

    /**
     * @var int
     */
    #[ORM\Column(name: 'totalTimeZoom', type: Types::INTEGER, nullable: true)]
    private ?int $totalTimeZoom = null;

    public function __construct()
    {
        $this->formateurFiles = new ArrayCollection();
        $this->honorary = 0;
    }

    public function __clone() {
        if ($this->id) {
            $this->id = null;
            $this->formateurFiles = new ArrayCollection();
            $this->isPaid = false;
            $this->paidDate = null;
            $this->startedAtZoom = null;
            $this->finishedAtZoom = null;
        }
    }

    /**
     * Get id
     *
     * @return integer 
     */
    public function getId()
    {
        return $this->id;
    }

    /**
     * Set programme
     *
     * @param Programme $programme
     * @return Formateur
     */
    public function setProgramme(Programme $programme = null)
    {
        $this->programme = $programme;

        return $this;
    }

    /**
     * Get programme
     *
     * @return Programme
     */
    public function getProgramme()
    {
        return $this->programme;
    }

    /**
     * @return Formation
     */
    public function getFormation()
    {
        return $this->formation;
    }

    /**
     * @param Formation $formation
     * @return Formateur
     */
    public function setFormation(Formation $formation)
    {
        $this->formation = $formation;
        return $this;
    }

    /**
     * Set person
     *
     * @param \Eduprat\AdminBundle\Entity\Person $person
     * @return Formateur
     */
    public function setPerson(\Eduprat\AdminBundle\Entity\Person $person)
    {
        $this->person = $person;

        return $this;
    }

    /**
     * Get person
     *
     * @return \Eduprat\AdminBundle\Entity\Person
     */
    public function getPerson()
    {
        return $this->person;
    }


    /**
     * Add formateurFiles
     *
     * @param FormateurFiles $formateurFiles
     * @return Formateur
     */
    public function addFormateurFile(FormateurFiles $formateurFiles)
    {
        $this->formateurFiles[] = $formateurFiles;

        return $this;
    }

    /**
     * Remove formateurFiles
     *
     * @param FormateurFiles $formateurFiles
     */
    public function removeFormateurFile(FormateurFiles $formateurFiles)
    {
        $this->formateurFiles->removeElement($formateurFiles);
    }

    /**
     * Get formateurFiles
     *
     * @return Collection
     */
    public function getFormateurFiles()
    {
        return $this->formateurFiles;
    }

    /**
     * @return float
     */
    public function getHonorary()
    {
        return $this->honorary;
    }

    /**
     * @param float $honorary
     * @return Formateur
     */
    public function setHonorary($honorary)
    {
        $this->honorary = $honorary;

        return $this;
    }

    /**
     * @return int
     */
    public function getEvaluationParticipantAnswersCount()
    {
        return $this->evaluationParticipantAnswersCount;
    }

    /**
     * @param int $evaluationParticipantAnswersCount
     * @return Formateur
     */
    public function setEvaluationParticipantAnswersCount($evaluationParticipantAnswersCount)
    {
        $this->evaluationParticipantAnswersCount = $evaluationParticipantAnswersCount;
        return $this;
    }

    /**
     * @return int
     */
    public function getEvaluationParticipantAnswersSum()
    {
        return $this->evaluationParticipantAnswersSum;
    }

    /**
     * @param int $evaluationParticipantAnswersSum
     * @return Formateur
     */
    public function setEvaluationParticipantAnswersSum($evaluationParticipantAnswersSum)
    {
        $this->evaluationParticipantAnswersSum = $evaluationParticipantAnswersSum;
        return $this;
    }

    /**
     * @return int
     */
    public function getEvaluationFormerAnswersCount()
    {
        return $this->evaluationCoordinatorAnswersCount;
    }

    /**
     * @param int $evaluationCoordinatorAnswersCount
     * @return Formateur
     */
    public function setEvaluationCoordinatorAnswersCount($evaluationCoordinatorAnswersCount)
    {
        $this->evaluationCoordinatorAnswersCount = $evaluationCoordinatorAnswersCount;
        return $this;
    }

    /**
     * @return int
     */
    public function getEvaluationCoordinatorAnswersSum()
    {
        return $this->evaluationCoordinatorAnswersSum;
    }

    /**
     * @param int $evaluationCoordinatorAnswersSum
     * @return Formateur
     */
    public function setEvaluationCoordinatorAnswersSum($evaluationCoordinatorAnswersSum)
    {
        $this->evaluationCoordinatorAnswersSum = $evaluationCoordinatorAnswersSum;
        return $this;
    }

    public function setIsPaid($value) {
        $this->isPaid = $value;
        return $this;
    }

    public function isPaid() {
        return $this->isPaid;
    }

    /**
     * Set paidDate
     *
     * @param \DateTime $paidDate
     */
    public function setPaidDate($paidDate)
    {
        $this->paidDate = $paidDate;

        return $this;
    }

    /**
     * Get paidDate
     *
     * @return \DateTime 
     */
    public function getPaidDate()
    {
        return $this->paidDate;
    }

    /**
     * @return string
     */
    public function getToken() {
        return md5(sprintf('%s%s%s',
            $this->getFormation()->getId(), $this->getId(), $this->getPerson()->getId()
        ));
    }

    public function getElasticIndex(): string
    {
        return self::ELASTIC_INDEX;
    }

    /**
     * Set startedAtZoom
     *
     * @param \DateTime|null $startedAtZoom
     * @return Formateur
     */
    public function setStartedAtZoom(?\DateTime $startedAtZoom)
    {
        $this->startedAtZoom = $startedAtZoom;

        return $this;
    }

    /**
     * Get startedAtZoom
     *
     * @return \DateTime
     */
    public function getStartedAtZoom()
    {
        return $this->startedAtZoom;
    }

    /**
     * Set finishedAtZoom
     *
     * @param \DateTime|null $finishedAtZoom
     * @return Formateur
     */
    public function setFinishedAtZoom(?\DateTime $finishedAtZoom)
    {
        $this->finishedAtZoom = $finishedAtZoom;

        return $this;
    }

    /**
     * Get finishedAtZoom
     *
     * @return \DateTime
     */
    public function getFinishedAtZoom()
    {
        return $this->finishedAtZoom;
    }

    /**
     * Set totalTimeZoom
     *
     * @param int $totalTimeZoom
     * @return Formateur
     */
    public function setTotalTimeZoom($totalTimeZoom)
    {
        $this->totalTimeZoom = $totalTimeZoom;

        return $this;
    }

    /**
     * Get totalTimeZoom
     *
     * @return int
     */
    public function getTotalTimeZoom()
    {
        return $this->totalTimeZoom;
    }

    public function getTotalTimeZoomMin()
    {
        return $this->totalTimeZoom ? $this->totalTimeZoom / 60 : $this->totalTimeZoom;
    }
}
