<?php

namespace Eduprat\DomainBundle\Entity;

use Doctrine\DBAL\Types\Types;
use Doctrine\ORM\Mapping as ORM;

/**
 * Invoice
 */
#[ORM\HasLifecycleCallbacks]
#[ORM\Entity(repositoryClass: 'Eduprat\DomainBundle\Repository\InvoiceProformaRepository')]
#[ORM\Table(name: 'invoice_proforma')]
class InvoiceProforma
{
    /**
     * @var int
     */
    #[ORM\Column(name: 'id', type: Types::INTEGER)]
    #[ORM\Id]
    #[ORM\GeneratedValue(strategy: 'AUTO')]
    private ?int $id = null;

    /**
     * @var Formation
     */
    #[ORM\ManyToOne(targetEntity: 'Eduprat\DomainBundle\Entity\Formation', inversedBy: 'invoices_proforma', cascade: ['persist'])]
    #[ORM\JoinColumn(name: 'formation', nullable: false, onDelete: 'CASCADE')]
    private ?Formation $formation = null;

    /**
     * @var FinanceSousMode
     */
    #[ORM\ManyToOne(targetEntity: 'Eduprat\DomainBundle\Entity\FinanceSousMode', cascade: ['persist'])]
    #[ORM\JoinColumn(name: 'finance_sous_mode', nullable: true, onDelete: 'CASCADE')]
    private ?FinanceSousMode $financeSousMode = null;

    /**
     * @var string
     */
    #[ORM\Column(name: 'number', type: Types::STRING, length: 255, unique: true)]
    private ?string $number = null;

    /**
     * @var integer
     */
    #[ORM\Column(name: 'year', type: Types::INTEGER, nullable: true)]
    private ?int $year = null;

    /**
     * @var \DateTimeInterface
     */
    #[ORM\Column(name: 'createdAt', type: Types::DATETIME_MUTABLE)]
    private ?\DateTimeInterface $createdAt = null;

    /**
     * @var \DateTimeInterface
     */
    #[ORM\Column(name: 'updatedAt', type: Types::DATETIME_MUTABLE, nullable: true)]
    private ?\DateTimeInterface $updatedAt = null;

    /**
     * Invoice constructor.
     * @param Formation $formation
     * @param FinanceSousMode $financeSousMode
     * @param string $number
     */
    public function __construct(Formation $formation, FinanceSousMode $financeSousMode, $number, $year) {
        $this->formation = $formation;
        $this->financeSousMode = $financeSousMode;
        $this->number = $number;
        $this->year = $year;
        $this->createdAt = new \DateTime();
    }

    /**
     * #ORM\PreUpdate
     */
    public function setPreUpdate()
    {
        $this->updatedAt = new \DateTime();
    }

    /**
     * Get id
     *
     * @return int
     */
    public function getId()
    {
        return $this->id;
    }

    /**
     * @return Formation
     */
    public function getFormation()
    {
        return $this->formation;
    }

    /**
     * @param Formation $formation
     */
    public function setFormation($formation)
    {
        $this->formation = $formation;
    }

    /**
     * @return FinanceSousMode
     */
    public function getFinanceSousMode()
    {
        return $this->financeSousMode;
    }

    /**
     * @param FinanceSousMode $financeSousMode
     * @return Invoice
     */
    public function setFinanceSousMode($financeSousMode)
    {
        $this->financeSousMode = $financeSousMode;
        return $this;
    }

    /**
     * Set number
     *
     * @param string $number
     *
     * @return Invoice
     */
    public function setNumber($number)
    {
        $this->number = $number;

        return $this;
    }

    /**
     * Get number
     *
     * @return string
     */
    public function getNumber()
    {
        return $this->number;
    }

     /**
     * @return int
     */
    public function getYear()
    {
        return $this->year;
    }

    /**
     * @param int $year
     * @return Invoice
     */
    public function setYear($year)
    {
        $this->year = $year;
        return $this;
    }

    /**
     * Set createdAt
     *
     * @param \DateTime $createdAt
     *
     * @return Invoice
     */
    public function setCreatedAt($createdAt)
    {
        $this->createdAt = $createdAt;

        return $this;
    }

    /**
     * Get createdAt
     *
     * @return \DateTime
     */
    public function getCreatedAt()
    {
        return $this->createdAt;
    }

    /**
     * Set updatedAt
     *
     * @param \DateTime $updatedAt
     *
     * @return Invoice
     */
    public function setUpdatedAt($updatedAt)
    {
        $this->updatedAt = $updatedAt;

        return $this;
    }

    /**
     * Get updatedAt
     *
     * @return \DateTime
     */
    public function getUpdatedAt()
    {
        return $this->updatedAt;
    }
}

