<?php

namespace Eduprat\DomainBundle\Entity;

use Doctrine\DBAL\Types\Types;
use Alienor\ElasticBundle\Model\ElasticableInterface;
use Carbon\Carbon;
use DateTime;
use Doctrine\Common\Collections\ArrayCollection;
use Doctrine\Common\Collections\Collection;
use Doctrine\Common\Collections\Criteria;
use Doctrine\ORM\Mapping as ORM;
use Doctrine\ORM\PersistentCollection;
use Eduprat\AuditBundle\Services\CourseManager;
use Eduprat\DomainBundle\Form\FormationAuditType;
use Eduprat\DomainBundle\Form\FormationPowerpointType;
use Eduprat\DomainBundle\Form\FormationPresentielleType;
use Eduprat\DomainBundle\Form\FormationElearningType;
use Eduprat\DomainBundle\Form\FormationActaliansType;
use Eduprat\DomainBundle\Form\FormationType;
use Eduprat\DomainBundle\Form\FormationVignetteType;
use Eduprat\DomainBundle\Form\FormationVfcType;
use Eduprat\DomainBundle\Form\FormationTcsType;
use Symfony\Component\HttpFoundation\File\File;
use Eduprat\AdminBundle\Entity\Person;
use Eduprat\DomainBundle\Form\FormationVignetteAuditType;
use Eduprat\DomainBundle\Services\AdressesService;
use Symfony\Component\HttpFoundation\File\UploadedFile;
use Symfony\Component\Validator\Context\ExecutionContextInterface;
use Symfony\Component\Validator\Constraints as Assert;

#[ORM\Entity(repositoryClass: 'Eduprat\DomainBundle\Repository\FormationRepository')]
#[ORM\InheritanceType('JOINED')]
#[ORM\HasLifecycleCallbacks]
#[ORM\DiscriminatorColumn(name: 'programme_discr', type: 'string')]
#[ORM\DiscriminatorMap(['formation' => 'Formation', 'formation_audit' => 'FormationAudit', 'formation_presentielle' => 'FormationPresentielle', 'formation_elearning' => 'FormationElearning', 'formation_sedd' => 'FormationSedd', 'formation_congres' => 'FormationCongres', 'formation_actalians' => 'FormationActalians', 'formation_powerpoint' => 'FormationPowerpoint', 'formation_vignette' => 'FormationVignette', 'formation_vignette_audit' => 'FormationVignetteAudit', 'formation_vfc' => 'FormationVfc', 'formation_tcs' => 'FormationTcs'])]
class Formation implements ElasticableInterface, ObjectWithTokenInterface
{

    const TYPE_FORMATION    = 'formation_audit';
    const TYPE_AUDIT    = 'formation_audit';
    const TYPE_AUDIT_PREDEFINED    = 'formation_predefined';
    const TYPE_VIGNETTE_AUDIT    = 'formation_vignette_audit';
    const TYPE_PRESENTIELLE = 'formation_presentielle';
    const TYPE_ELEARNING = 'formation_elearning';
    const TYPE_SEDD = 'formation_sedd';
    const TYPE_CONGRES = 'formation_congres';
    const TYPE_ACTALIANS = 'formation_actalians';
    const TYPE_POWERPOINT = 'formation_powerpoint';
    const TYPE_VIGNETTE = 'formation_vignette';
    const TYPE_LBI = 'formation_lbi';
    const TYPE_VFC = 'formation_vfc';
    const TYPE_TCS = 'formation_tcs';

    const FORM_TYPE_AUDIT = "audit";
    const FORM_TYPE_PREDEFINED = "predefined";
    const FORM_TYPE_SURVEY = "survey";
    const FORM_TYPE_VIGNETTE = "vignette";
    const FORM_TYPE_VIGNETTE_AUDIT = "vignette_audit";
    const FORM_TYPE_AUDIT_VIGNETTE = "audit_vignette";
    const FORM_TYPE_TCS = "tcs";

    const GROUP_EDUPRAT = 'Réseau Eduprat';
    const GROUP_CMS = 'CMS';
    const GROUP_LBI = 'Coordinateur LBI';
    const GROUP_SET = 'SET';
    const GROUP_SEDD = 'SEDD';
    const GROUP_CONGRES = 'Congrés';
    const GROUP_ELEARNING = 'Elearning';
    const GROUP_ACTALIANS = 'Actalians';

    const VARIETY_ELEARNING = 'elearning';
    const VARIETY_MIXED = 'mixed';
    const VARIETY_PRESENTIEL = 'presentiel';
    const VARIETIES = [self::VARIETY_ELEARNING, self::VARIETY_MIXED, self::VARIETY_PRESENTIEL];

    const STATUS_CLOSED = 'closed';
    const STATUS_OPENED = 'opened';
    const STATUS_FUTURE = 'future';
    const STATUS_ACCESSIBLE = 'accessible';
    const STATUS_CLOSED_ANDPC = 'closed_andpc';
    const STATUS_OPENED_ANDPC = 'opened_andpc';
    const STATUS_FUTURE_ANDPC = 'future_andpc';

    CONST ELASTIC_INDEX = 'formation';

    const TYPES = array(
        self::TYPE_AUDIT => FormationAudit::class,
        self::TYPE_PRESENTIELLE => FormationPresentielle::class,
        self::TYPE_AUDIT_PREDEFINED => FormationAudit::class,
        self::TYPE_ELEARNING => FormationElearning::class,
        self::TYPE_SEDD => FormationSedd::class,
        self::TYPE_CONGRES => FormationCongres::class,
        self::TYPE_ACTALIANS => FormationActalians::class,
        self::TYPE_POWERPOINT => FormationPowerpoint::class,
        self::TYPE_VIGNETTE => FormationVignette::class,
        self::TYPE_VIGNETTE_AUDIT => FormationVignetteAudit::class,
        self::TYPE_VFC => FormationVfc::class,
        self::TYPE_TCS => FormationTcs::class,
    );

    const FORM_TYPES = array(
        self::TYPE_AUDIT => FormationAuditType::class,
        self::TYPE_AUDIT_PREDEFINED => FormationAuditType::class,
        self::TYPE_PRESENTIELLE => FormationPresentielleType::class,
        self::TYPE_ELEARNING => FormationElearningType::class,
        self::TYPE_SEDD => FormationType::class,
        self::TYPE_CONGRES => FormationType::class,
        self::TYPE_ACTALIANS => FormationActaliansType::class,
        self::TYPE_POWERPOINT => FormationPowerpointType::class,
        self::TYPE_VIGNETTE => FormationVignetteType::class,
        self::TYPE_VIGNETTE_AUDIT => FormationVignetteAuditType::class,
        self::TYPE_VFC => FormationVfcType::class,
        self::TYPE_TCS => FormationTcsType::class,
    );

    CONST DEFAULT_MAX_PLACES_SITE = 48;
    CONST DEFAULT_MAX_PLACES_VIRTUELLE = 30;
    CONST DEFAULT_MAX_PLACES_ELEARNING = 100;

    const FORMAT_PERIOD_CONGRES = 'Congrès';
    const FORMAT_PERIOD_JOURNEE = 'Journée';

    /**
     * @var int
     */
    #[ORM\Column(name: 'id', type: Types::INTEGER)]
    #[ORM\Id]
    #[ORM\GeneratedValue(strategy: 'AUTO')]
    private ?int $id = null;

    /**
     * @var float
     */
    #[ORM\Column(name: 'price', type: Types::FLOAT)]
    private ?float $price = 0;

    /**
     * Date de début de la première unité
     * @var \DateTimeInterface
     */
    #[ORM\Column(name: 'openingDate', type: Types::DATETIME_MUTABLE, nullable: true)]
    private ?\DateTimeInterface $openingDate = null;

    /**
     * Date de fin de la dernière unité
     * @var \DateTimeInterface
     */
    #[ORM\Column(name: 'closingDate', type: Types::DATETIME_MUTABLE, nullable: true)]
    private ?\DateTimeInterface $closingDate = null;

    /**
     * @var \DateTimeInterface
     */
    #[ORM\Column(name: 'lastImportDate', type: Types::DATETIME_MUTABLE, nullable: true)]
    private ?\DateTimeInterface $lastImportDate = null;

    /**
     * @var bool
     */
    #[ORM\Column(name: 'closed', type: Types::BOOLEAN)]
    private ?bool $closed = false;

    /**
     * @var bool
     */
    #[ORM\Column(name: 'closedAndpc', type: Types::BOOLEAN)]
    private ?bool $closedAndpc = false;

    /**
     * @var bool
     */
    #[ORM\Column(name: 'billed', type: Types::BOOLEAN)]
    private ?bool $billed = false;

    /**
     * @var bool
     */
    #[ORM\Column(name: 'accounted', type: Types::BOOLEAN)]
    private ?bool $accounted = false;

    /**
     * @var bool
     */
    #[ORM\Column(name: 'accountedMidCourse', type: Types::BOOLEAN)]
    private ?bool $accountedMidCourse = false;

    /**
     * @var \DateTimeInterface
     */
    #[ORM\Column(name: 'createdAt', type: Types::DATETIME_MUTABLE)]
    private ?\DateTimeInterface $createdAt = null;

    /**
     * @var \DateTimeInterface
     */
    #[ORM\Column(name: 'updatedAt', type: Types::DATETIME_MUTABLE, nullable: true)]
    private ?\DateTimeInterface $updatedAt = null;

    /**
     * @var Programme
     */
    #[ORM\ManyToOne(targetEntity: 'Eduprat\DomainBundle\Entity\Programme', inversedBy: 'formations', cascade: ['persist'])]
    #[ORM\JoinColumn(name: 'programme', nullable: false)]
    private ?Programme $programme = null;

    /**
     * @var FinanceMode
     */
    #[ORM\ManyToOne(targetEntity: 'Eduprat\DomainBundle\Entity\FinanceMode', cascade: ['persist'])]
    #[ORM\JoinColumn(name: 'financeMode', nullable: true)]
    private ?FinanceMode $financeMode = null;

    /**
     * @var FinanceSousMode
     */
    #[ORM\ManyToOne(targetEntity: 'Eduprat\DomainBundle\Entity\FinanceSousMode', cascade: ['persist'])]
    #[ORM\JoinColumn(name: 'financeSousMode', nullable: true)]
    private ?FinanceSousMode $financeSousMode = null;

    /**
     * @var Collection<int, FinanceMode>
     */
    #[ORM\ManyToMany(targetEntity: 'Eduprat\DomainBundle\Entity\FinanceMode', cascade: ['persist'])]
    #[ORM\JoinTable(name: 'formation_finance_mode', joinColumns: [new ORM\JoinColumn(name: 'formation', referencedColumnName: 'id', onDelete: 'CASCADE')], inverseJoinColumns: [new ORM\JoinColumn(name: 'finance_mode', referencedColumnName: 'id')])]
    private Collection $financeModes;

    /**
     * @var Collection<int, FinanceSousMode>
     */
    #[ORM\ManyToMany(targetEntity: 'Eduprat\DomainBundle\Entity\FinanceSousMode', cascade: ['persist'])]
    #[ORM\JoinTable(name: 'formation_finance_sous_mode', joinColumns: [new ORM\JoinColumn(name: 'formation', referencedColumnName: 'id', onDelete: 'CASCADE')], inverseJoinColumns: [new ORM\JoinColumn(name: 'finance_sous_mode', referencedColumnName: 'id')])]
    #[ORM\OrderBy(['id' => 'ASC'])]
    private Collection $financeSousModes;

    /**
     * @var array
     */
    #[ORM\Column(name: 'financeSousModeFactures', type: Types::JSON, nullable: true)]
    private $financeSousModeFactures;

    /**
       * @var bool
       */
      #[ORM\Column(name: 'oneFinanceSousModeFactured', type: Types::BOOLEAN, nullable: true)]
      protected ?bool $oneFinanceSousModeFactured = false;

    /**
     * @var Collection<int, Participation>
     */
    #[ORM\OneToMany(targetEntity: 'Eduprat\DomainBundle\Entity\Participation', mappedBy: 'formation', cascade: ['persist'])]
    protected Collection $participations;

    /**
     * @var Invoice
     */
    #[ORM\OneToMany(targetEntity: 'Invoice', mappedBy: 'formation', cascade: ['persist'])]
    private Collection $invoices;

    /**
     * @var InvoiceProforma
     */
    #[ORM\OneToMany(targetEntity: 'InvoiceProforma', mappedBy: 'formation', cascade: ['persist'])]
    private Collection $invoices_proforma;

    /**
     * @var string
     */
    #[ORM\Column(name: 'emargement', type: Types::STRING, length: 255, nullable: true)]
    private ?string $emargement = null;

    /**
     * @var File
     */
    private $emargementFile;

    /**
     * @var string
     */
    #[ORM\Column(name: 'factureFormer', type: Types::STRING, length: 255, nullable: true)]
    private ?string $factureFormer = null;

    /**
     * @var File
     */
    private $factureFormerFile;

    /**
     * @var string
     */
    #[ORM\Column(name: 'factureRestauration', type: Types::STRING, length: 255, nullable: true)]
    private ?string $factureRestauration = null;

    /**
     * @var File
     */
    private $factureRestaurationFile;

    /**
     * @var string
     */
    #[ORM\Column(name: 'justificatifSuivi', type: Types::STRING, length: 255, nullable: true)]
    private ?string $justificatifSuivi = null;

    /**
     * @var File
     */
    private $justificatifSuiviFile;

    /**
     * @var string
     */
    #[ORM\Column(name: 'contratFormateur', type: Types::STRING, length: 255, nullable: true)]
    private ?string $contratFormateur = null;

    /**
     * @var string
     */
    #[ORM\Column(name: 'contratFormateur2', type: Types::STRING, length: 255, nullable: true)]
    private ?string $contratFormateur2 = null;

    /**
     * @var string
     */
    #[ORM\Column(name: 'contratFormateur3', type: Types::STRING, length: 255, nullable: true)]
    private ?string $contratFormateur3 = null;

    /**
     * @var string
     */
    #[ORM\Column(name: 'contratFormateur4', type: Types::STRING, length: 255, nullable: true)]
    private ?string $contratFormateur4 = null;

    /**
     * @var string
     */
    #[ORM\Column(name: 'contratFormateur5', type: Types::STRING, length: 255, nullable: true)]
    private ?string $contratFormateur5 = null;

    /**
     * @var File
     */
    private $contratFormateurFile;

    /**
     * @var File
     */
    private $contratFormateur2File;

    /**
     * @var File
     */
    private $contratFormateur3File;

    /**
     * @var File
     */
    private $contratFormateur4File;

    /**
     * @var File
     */
    private $contratFormateur5File;

    /**
     * @var string
     */
    #[ORM\Column(name: 'topoFormateur', type: Types::STRING, length: 255, nullable: true)]
    private ?string $topoFormateur = null;

    /**
     * @var File
     */
    private $topoFormateurFile;

    /**
     * @var int
     */
    #[ORM\Column(name: 'sessionNumber', type: Types::STRING, nullable: true)]
    private ?string $sessionNumber = null;

    /**
     * @var bool
     */
    #[ORM\Column(name: 'noMailing', type: Types::BOOLEAN)]
    private ?bool $noMailing = false;

    /**
     * @var bool
     */
    #[ORM\Column(name: 'expert', type: Types::BOOLEAN)]
    private ?bool $expert = false;

    /**
     * Date de mise à disposition du formulaire (pré)
     * @var \DateTimeInterface
     */
    #[ORM\Column(name: 'formOpeningDate', type: Types::DATETIME_MUTABLE, nullable: true)]
    protected ?\DateTimeInterface $formOpeningDate = null;

    /**
     * Date de fin d'acceptabilité des formulaires
     * @var \DateTimeInterface
     */
    #[ORM\Column(name: 'formClosingDate', type: Types::DATETIME_MUTABLE, nullable: true)]
    protected ?\DateTimeInterface $formClosingDate = null;

    /**
     * @var Collection<int, FormateurFiles>
     */
    #[ORM\OneToMany(targetEntity: 'Eduprat\DomainBundle\Entity\FormateurFiles', mappedBy: 'formation', cascade: ['persist'])]
    protected Collection $formateurFiles;

    /**
     * @var Collection<int, CoordinatorFiles>
     */
    #[ORM\OneToMany(targetEntity: 'Eduprat\DomainBundle\Entity\CoordinatorFiles', mappedBy: 'formation', cascade: ['persist'])]
    protected Collection $coordinatorFiles;

    /**
     * @var Collection<int, TopoFiles>
     */
    #[ORM\OneToMany(targetEntity: 'Eduprat\DomainBundle\Entity\TopoFiles', mappedBy: 'formation', cascade: ['persist'])]
    protected Collection $topoFiles;

    /**
     * @var Collection|EmargementFile[]
     */
    #[ORM\OneToMany(targetEntity: 'EmargementFile', mappedBy: 'formation', cascade: ['persist'])]
    protected Collection $emargementFiles;

    /**
     * @var Collection|ConventionFile[]
     */
    #[ORM\OneToMany(targetEntity: 'ConventionFile', mappedBy: 'formation', cascade: ['persist'])]
    protected Collection $conventionFiles;

    /**
     * @var boolean
     */
    #[ORM\Column(name: 'actaliansPdf', type: Types::BOOLEAN, nullable: true)]
    private ?bool $actaliansPdf = null;

    /**
     * @var boolean
     */
    private $typeModif = null;

    /**
     * @var integer
     */
    #[ORM\Column(name: 'manualParticipantCount', type: Types::INTEGER, nullable: true)]
    private ?int $manualParticipantCount = null;

    /**
     * @var float
     */
    #[ORM\Column(name: 'cost', type: Types::FLOAT, nullable: true)]
    private ?float $cost = null;

    /**
     * @var Collection<int, Formateur>
     */
    #[ORM\OneToMany(targetEntity: 'Eduprat\DomainBundle\Entity\Formateur', mappedBy: 'formation', cascade: ['persist', 'remove'], orphanRemoval: true)]
    private Collection $formateurs;

    /**
     * @var string
     */
    #[ORM\Column(name: 'address', type: Types::STRING, length: 255, nullable: true)]
    private ?string $address = null;

    /**
     * @var string
     */
    #[ORM\Column(name: 'address2', type: Types::STRING, length: 255, nullable: true)]
    private ?string $address2 = null;

    /**
     * @var string
     */
    #[ORM\Column(name: 'city', type: Types::STRING, length: 255, nullable: true)]
    private ?string $city = null;

    /**
     * @var string
     */
    #[ORM\Column(name: 'zipCode', type: Types::STRING, length: 255, nullable: true)]
    private ?string $zipCode = null;

    /**
     * Date de début de la réunion principale
     * @var \DateTimeInterface
     */
    #[ORM\Column(name: 'startDate', type: Types::DATETIME_MUTABLE, nullable: true)]
    private ?\DateTimeInterface $startDate = null;

    /**
     * Date de fin de la réunion principale
     * @var \DateTimeInterface
     */
    #[ORM\Column(name: 'endDate', type: Types::DATETIME_MUTABLE, nullable: true)]
    private ?\DateTimeInterface $endDate = null;

    /**
     * @var string
     */
    #[ORM\Column(name: 'messageRappel', type: Types::TEXT, nullable: true)]
    private ?string $messageRappel = null;

    /**
     * @var Collection<int, Coordinator>
     */
    #[ORM\OneToMany(targetEntity: 'Eduprat\DomainBundle\Entity\Coordinator', mappedBy: 'formation', cascade: ['persist', 'remove'], orphanRemoval: true)]
    private Collection $coordinators;

    /**
     * @var float
     */
    #[ORM\Column(name: 'costKilometres', type: Types::FLOAT, nullable: true)]
    protected ?float $costKilometres = null;

    /**
     * @var float
     */
    #[ORM\Column(name: 'costBadges', type: Types::FLOAT, nullable: true)]
    protected ?float $costBadges = null;

    /**
     * @var float
     */
    #[ORM\Column(name: 'costRetrocessions', type: Types::FLOAT, nullable: true)]
    protected ?float $costRetrocessions = null;

    /**
     * @var float
     */
    #[ORM\Column(name: 'costMateriel', type: Types::FLOAT, nullable: true)]
    protected ?float $costMateriel = null;

    /**
     * @var float
     */
    #[ORM\Column(name: 'costDivers', type: Types::FLOAT, nullable: true)]
    protected ?float $costDivers = null;

    /**
     * @var string
     */
    #[ORM\Column(name: 'region', type: Types::STRING, nullable: true)]
    protected ?string $region = null;

    /**
     * @var string
     */
    #[ORM\Column(name: 'departement', type: Types::STRING, nullable: true)]
    protected ?string $departement = null;


    #[ORM\Column(name: 'latitude', type: Types::STRING, length: 255, nullable: true)]
    private ?string $latitude = null;

    /**
     * @var string
     */
    #[ORM\Column(name: 'longitude', type: Types::STRING, length: 255, nullable: true)]
    private ?string $longitude = null;

    /**
     * @var Collection<int, EvaluationGlobalAnswer>
     */
    #[ORM\OneToMany(targetEntity: 'Eduprat\DomainBundle\Entity\EvaluationGlobalAnswer', mappedBy: 'formation', cascade: ['persist'])]
    protected Collection $evaluationAnswers;


    #[ORM\Column(name: 'zoomId', type: Types::STRING, length: 255, nullable: true)]
    private ?string $zoomId = null;

    /**
     * @var string
     */
    #[ORM\Column(name: 'zoomLink', type: Types::STRING, length: 255, nullable: true)]
    private ?string $zoomLink = null;

    /**
     * @var boolean
     */
    #[ORM\Column(name: 'fileCompleted', type: Types::BOOLEAN)]
    private ?bool $fileCompleted = null;

    /**
     * @var boolean
     */
    #[ORM\Column(name: 'national', type: Types::BOOLEAN, nullable: true)]
    private ?bool $national = null;

    /**
       * @var bool
       */
      #[ORM\Column(name: 'quizsCompletedsEmailSended', type: Types::BOOLEAN, nullable: true)]
      protected ?bool $quizsCompletedsEmailSended = null;

    /**
     * @var string
     */
    #[ORM\Column(name: 'accessibilite', type: Types::STRING, length: 255)]
    private ?string $accessibilite = "Si vous avez des questions sur l’accessibilité à la formation en cas de handicap, n’hésitez pas à contacter Doriane PAULEN à l'<NAME_EMAIL>.";

    /**
     * @var string
     */
    #[ORM\Column(name: 'commentaire', type: Types::STRING, length: 255, nullable: true)]
    private ?string $commentaire = "Restauration prise en charge.";

    /**
     * @var string
     */
    #[ORM\Column(name: 'commentaireNotif', type: Types::STRING, nullable: true)]
    protected ?string $commentaireNotif = null;

    /**
     * @var string
     */
    #[ORM\Column(name: 'outil', type: Types::STRING, length: 255, nullable: true)]
    private ?string $outil = null;

    /**
     * @var bool
     */
    #[ORM\Column(name: 'private', type: Types::BOOLEAN)]
    private ?bool $private = false;

    /**
     * @var AuditCategory
     */
    private $theme;

    /**
     * @var string
     */
    #[ORM\Column(name: 'partenariat', type: Types::STRING, length: 255, nullable: true)]
    private ?string $partenariat = null;

        /**
     * @var string
     */
    #[ORM\Column(name: 'ots', type: Types::STRING, length: 255, nullable: true)]
    private ?string $ots = null;

     /**
     * @var boolean
     */
    #[ORM\Column(name: 'archived', type: Types::BOOLEAN, length: 255, options: ['default' => false])]
    private ?bool $archived = null;

    /**
     * @var Collection<int, FormationHistory>
     */
    #[ORM\OneToMany(targetEntity: 'Eduprat\DomainBundle\Entity\FormationHistory', mappedBy: 'formation', cascade: ['persist', 'remove'])]
    private Collection $formationHistories;

    #[ORM\Column(name: 'topoProgrammeDeleted', type: Types::JSON, nullable: false)]
    private ?array $topoProgrammeDeleted = array();

    /**
     * @var \DateTimeInterface
     */
    #[ORM\Column(name: 'zoomStartTime', type: Types::DATETIME_MUTABLE, nullable: true)]
    private ?\DateTimeInterface $zoomStartTime = null;

    /**
     * @var \DateTimeInterface
     */
    #[ORM\Column(name: 'zoomEndTime', type: Types::DATETIME_MUTABLE, nullable: true)]
    private ?\DateTimeInterface $zoomEndTime = null;

    /**
     * @var Collection<int, UnitySession>
     */
    #[ORM\OneToMany(targetEntity: 'Eduprat\DomainBundle\Entity\UnitySession', mappedBy: 'formation', orphanRemoval: true, cascade: ['persist', 'remove'])]
    private Collection $unities;

    /**
     * @var Collection<int, ModuleTimes>
     */
    #[ORM\OneToMany(targetEntity: 'Eduprat\DomainBundle\Entity\ModuleTimes', mappedBy: 'formation', orphanRemoval: true, cascade: ['persist', 'remove'])]
    private Collection $moduleTimes;

    /**
     * @var Collection<int, ModuleMinTimes>
     */
    #[ORM\OneToMany(targetEntity: 'Eduprat\DomainBundle\Entity\ModuleMinTimes', mappedBy: 'formation', orphanRemoval: true, cascade: ['persist', 'remove'])]
    private Collection $moduleMinTimes;

    #[ORM\OneToOne(targetEntity: 'Eduprat\DomainBundle\Entity\FactureState', mappedBy: 'formation', cascade: ['persist', 'remove'], orphanRemoval: true)]
    private ?FactureState $factureState = null;

    /**
     * @var bool
     */
    private $applyTimesToAll;

    /**
     * @var integer
     */
    #[ORM\Column(name: 'nombrePlacesInitiales', type: Types::INTEGER, nullable: true)]
    private ?int $nombrePlacesInitiales;

    /**
     * Date de fin de début affichée dans la recherche de sessions
     * Pour les elearning : elle reprend openingDate de la formation
     * Pour les autres formations, elle reprend startDate
     */
    #[ORM\Column(name: 'searchStartDate', type: Types::DATETIME_MUTABLE, nullable: true)]
    protected ?\DateTimeInterface $searchStartDate = null;

    /**
     * Date de fin de fin affichée dans la recherche de sessions
     * Pour les elearning : elle reprend closingDate de la formation
     * Pour les autres formations, elle reprend endDate
     * @var \DateTimeInterface
     */
    #[ORM\Column(name: 'searchEndDate', type: Types::DATETIME_MUTABLE, nullable: true)]
    protected ?\DateTimeInterface $searchEndDate = null;

    /**
     * Date de fin de début utilisée dans la recherche de sessions
     * Pour les elearning : elle reprend openingDate de la formation
     * Pour les autres formations, elle reprend startDate
     */
    #[ORM\Column(name: 'querySearchStartDate', type: Types::DATETIME_MUTABLE, nullable: true)]
    protected ?\DateTime $querySearchStartDate = null;

    /**
     * Date de fin utilisée dans la recherche de sessions
     * Pour les elearning : elle reprend openingDate de la formation
     * Pour les autres formations, elle reprend endDate
     * @var \DateTimeInterface
     */
    #[ORM\Column(name: 'querySearchEndDate', type: Types::DATETIME_MUTABLE, nullable: true)]
    protected ?\DateTime $querySearchEndDate = null;

    /**
     * Date à partir une attestation est considérée comme manquante
     */
    #[ORM\Column(name: 'attestationMissingDate', type: Types::DATETIME_MUTABLE, nullable: true)]
    protected ?\DateTimeInterface $attestationMissingDate = null;

    #[ORM\Column(name: 'attestationN1MissingDate', type: Types::DATETIME_MUTABLE, nullable: true)]
    protected ?\DateTimeInterface $attestationMissingDateN1 = null;

    /**
     * @var Course
     */
    #[ORM\OneToOne(targetEntity: 'Eduprat\DomainBundle\Entity\Course', inversedBy: 'formation', cascade: ['persist'])]
    #[ORM\JoinColumn(name: 'course_id', nullable: true)]
    private ?Course $course = null;

    /**
     * @var string
     */
    #[ORM\Column(name: 'formatPeriod', type: Types::STRING, length: 255, nullable: true)]
    private ?string $formatPeriod = null;

    /**
     * Formation constructor.
     */
    public function __construct()
    {
        $this->conventionFiles = new ArrayCollection();
        $this->evaluationAnswers = new ArrayCollection();
        $this->formationHistories = new ArrayCollection();
        $this->createdAt = new \DateTime();
        $this->formateurFiles = new ArrayCollection();
        $this->coordinatorFiles = new ArrayCollection();
        $this->topoFiles = new ArrayCollection();
        $this->financeSousModes = new ArrayCollection();
        $this->formateurs = new ArrayCollection();
        $this->participations = new ArrayCollection();
        $this->coordinators = new ArrayCollection();
        $this->financeModes = new ArrayCollection();
        $this->invoices = new ArrayCollection();
        $this->invoices_proforma = new ArrayCollection();
        $this->emargementFiles = new ArrayCollection();
        $this->fileCompleted = false;
        $this->archived = false;
        $this->topoProgrammeDeleted = array();
        $this->unities = new ArrayCollection();
        $this->moduleTimes = new ArrayCollection();
        $this->moduleMinTimes = new ArrayCollection();
        $this->nombrePlacesInitiales = 50;
    }

    public function createFormationFrom() {
        $coordinators = $this->coordinators;
        $formateurs = $this->formateurs;
        $financeSousModes = $this->financeSousModes;
        $financeModes = $this->financeModes;
        $newObject = clone $this;
        $newObject->__construct();
        $newObject->id = null;
        $newObject->closed = false;
        $newObject->closedAndpc = false;
        $newObject->billed = false;
        $newObject->accounted = false;
        $newObject->fileCompleted = false;
        $newObject->financeSousModeFactures = null;
        $newObject->oneFinanceSousModeFactured = false;

        foreach ($coordinators as $coordinator) {
            $newObject->addCoordinator(clone $coordinator);
        }
        foreach ($formateurs as $formateur) {
            $newObject->addFormateur(clone $formateur);
        }
        foreach ($financeSousModes as $financeSousMode) {
            $newObject->addFinanceSousMode($financeSousMode);
        }
        foreach ($financeModes as $financeMode) {
            $newObject->addFinanceMode($financeMode);
        }
        return $newObject;
    }

    #[ORM\PreUpdate]
    public function setPreUpdate()
    {
        $this->updatedAt = new \DateTime();
    }

    /**
     * Get id
     *
     * @return integer
     */
    public function getId()
    {
        return $this->id;
    }

    /**
     * Set price
     *
     * @param float $price
     * @return Formation
     */
    public function setPrice($price)
    {
        $this->price = $price;

        return $this;
    }

    /**
     * Get price
     *
     * @return float
     */
    public function getPrice()
    {
        return $this->price;
    }

    /**
     * Set typeModif
     *
     * @param string $typeModif
     * @return Formation
     */
    public function setTypeModif($typeModif)
    {
        $this->typeModif = $typeModif;

        return $this;
    }

    /**
     * Set typeModif
     *
     * @param string $typeModif
     * @return Formation
     */
    public function setTypeModifFromChange($dateChanged, $addressChanged, $formateursChanged)
    {

        $txt = null;

        if ($dateChanged) {
            $txt = "Changement de date";
            if ($addressChanged && $formateursChanged) {
                $txt = $txt . ",d'adresse et de formateur";
            } else {
                if ($addressChanged) {
                    $txt = $txt . " et d'adresse";
                } elseif ($formateursChanged) {
                    $txt = $txt . " et de formateur";
                }
            }
        } elseif(!$dateChanged && $addressChanged) {
            $txt = "Changement d'adresse";
            if ($formateursChanged) {
                $txt = $txt . " et de formateur";
            }
        } elseif(!$dateChanged && !$addressChanged && $formateursChanged) {
            $txt = "Changement de formateur";
        }

        $this->setTypeModif($txt);

        return $this;
    }

    /**
     * Get price
     *
     * @return float
     */
    public function getTypeModif()
    {
        return $this->typeModif;
    }

    /**
     * Set openingDate
     *
     * @param \DateTime $openingDate
     * @return Formation
     */
    public function setOpeningDate($openingDate)
    {
        $this->openingDate = $openingDate;

        return $this;
    }

    /**
     * Get openingDate
     *
     * @return \DateTime
     */
    public function getOpeningDate()
    {
        return $this->openingDate;
    }

    /**
     * Set closingDate
     *
     * @param \DateTime $closingDate
     * @return Formation
     */
    public function setClosingDate($closingDate)
    {
        $this->closingDate = $closingDate;

        return $this;
    }

    /**
     * Get closingDate
     *
     * @return \DateTime
     */
    public function getClosingDate()
    {
        return $this->closingDate;
    }

    /**
     * Set lastImportDate
     *
     * @param \DateTime $lastImportDate
     * @return Formation
     */
    public function setLastImportDate($lastImportDate)
    {
        $this->lastImportDate = $lastImportDate;

        return $this;
    }

    /**
     * Get lastImportDate
     *
     * @return \DateTime
     */
    public function getLastImportDate()
    {
        return $this->lastImportDate;
    }

    /**
     * Set closed
     *
     * @param boolean $closed
     * @return Formation
     */
    public function setClosed($closed)
    {
        $this->closed = $closed;

        return $this;
    }

    /**
     * Get closed
     *
     * @return boolean
     */
    public function getClosed()
    {
        return $this->closed;
    }

    /**
     * Set closedAndpc
     *
     * @param boolean $closedAndpc
     * @return Formation
     */
    public function setClosedAndpc($closedAndpc)
    {
        $this->closedAndpc = $closedAndpc;

        return $this;
    }

    /**
     * Get closedAndpc
     *
     * @return boolean
     */
    public function getClosedAndpc()
    {
        return $this->closedAndpc;
    }

    /**
     * Set accounted
     *
     * @param boolean $accounted
     * @return Formation
     */
    public function setAccounted($accounted)
    {
        $this->accounted = $accounted;

        return $this;
    }

    /**
     * Get accounted
     *
     * @return boolean
     */
    public function getAccounted()
    {
        return $this->accounted;
    }

    /**
     * Set accountedMidCourse
     *
     * @param boolean $accountedMidCourse
     * @return Formation
     */
    public function setAccountedMidCourse($accountedMidCourse)
    {
        $this->accountedMidCourse = $accountedMidCourse;

        return $this;
    }

    /**
     * Get accountedMidCourse
     *
     * @return boolean
     */
    public function getAccountedMidCourse()
    {
        return $this->accountedMidCourse;
    }

    public function featureDisabledBecauseAccounted() {
        return $this->getAccounted() || ($this->getAccountedMidCourse() && $this->isPluriannuelle());
    }

    /**
     * Set billed
     *
     * @param boolean $billed
     * @return Formation
     */
    public function setBilled($billed)
    {
        $this->billed = $billed;

        return $this;
    }

    /**
     * Get billed
     *
     * @return boolean
     */
    public function getBilled()
    {
        return $this->billed;
    }

    /**
     * Set fileCompleted
     *
     * @param boolean $fileCompleted
     * @return Formation
     */
    public function setFileCompleted($fileCompleted)
    {
        $this->fileCompleted = $fileCompleted;

        return $this;
    }

    /**
     * Get fileCompleted
     *
     * @return boolean
     */
    public function getFileCompleted()
    {
        return $this->fileCompleted;
    }

    /**
     * Set billingProgression
     *
     * @param array $billingProgression
     * @return Formation
     */
    public function setBillingProgression($billingProgression)
    {
        $this->billingProgression = $billingProgression;

        return $this;
    }

    /**
     * Get billingProgression
     *
     * @return array
     */
    public function getBillingProgression()
    {
        return $this->billingProgression;
    }

    /**
     * Set createdAt
     *
     * @param \DateTime $createdAt
     * @return Formation
     */
    public function setCreatedAt($createdAt)
    {
        $this->createdAt = $createdAt;

        return $this;
    }

    /**
     * Get createdAt
     *
     * @return \DateTime
     */
    public function getCreatedAt()
    {
        return $this->createdAt;
    }

    /**
     * Set updatedAt
     *
     * @param \DateTime $updatedAt
     * @return Formation
     */
    public function setUpdatedAt($updatedAt)
    {
        $this->updatedAt = $updatedAt;

        return $this;
    }

    /**
     * Get updatedAt
     *
     * @return \DateTime
     */
    public function getUpdatedAt()
    {
        return $this->updatedAt;
    }

    /**
     * Add participations
     *
     * @param Participation $participations
     * @return Formation
     */
    public function addParticipation(Participation $participations)
    {
        $this->participations[] = $participations;
        return $this;
    }

    /**
     * Remove participations
     *
     * @param Participation $participations
     */
    public function removeParticipation(Participation $participations)
    {
        $this->participations->removeElement($participations);
    }

    public function setParticipations(ArrayCollection|Collection|array $participations): self
    {
        $this->participations = $participations;

        return $this;
    }

    /**
     * Get participations
     *
     * @return Participation[]|Collection
     */
    public function getParticipations($archived = false)
    {
        if (!$this->participations) {
            return $this->participations;
        }
        return $this->participations->matching(Criteria::create()->where(Criteria::expr()->eq('archived', $archived)));
    }

    public function getParticipationsComplete()
    {
        if (!$this->participations) {
            return $this->participations;
        }
        return $this->getParticipations()->matching(Criteria::create()->where(Criteria::expr()->isNull('nextModule')));
    }

    /**
     * Get participations
     *
     * @return Participation[]|Collection
     */
    public function getElasticParticipations($archived = false)
    {
        if (!$this->participations) {
            return $this->participations;
        }
        return $this->participations->matching(Criteria::create()->where(Criteria::expr()->eq('archived', $archived)));
    }

    public function getParticipationsPerMode(FinanceSousMode $financeSousMode = null) {

        $criterias = Criteria::create()
            ->where(Criteria::expr()->eq('archived', false))
            ->andWhere(Criteria::expr()->eq('financeSousMode', $financeSousMode));

        return $this->participations->matching($criterias);
    }

    /**
     * @return ArrayCollection|Collection
     */
    public function getParticipantsPerModes($financesSousMode = []) {
        $criterias = Criteria::create()
            ->where(Criteria::expr()->eq('archived', false))
        ;
        return $this->participations->matching($criterias)->filter(function(Participation $p) use ($financesSousMode) {
            if (empty($financesSousMode)) {
                return true;
            }
            return in_array($p->getFinanceSousMode()->getPriseEnCharge(), $financesSousMode);
        })->map(function(Participation $p) { return $p->getParticipant();});
    }

    public function getParticipationsPerModeAndCoordinator(FinanceSousMode $financeSousMode = null, Coordinator $coordinator = null) {

        $criterias = Criteria::create()
            ->where(Criteria::expr()->eq('archived', false))
            ->andWhere(Criteria::expr()->eq('financeSousMode', $financeSousMode));

        if ($this->getCoordinators()->count() > 1) {
            $criterias->andWhere(Criteria::expr()->eq('coordinator', $coordinator));
        }

        return $this->participations->matching($criterias);
    }

    /**
     * @return Participation[]|Collection
     */
    public function getAllParticipations()
    {
        return $this->participations;
    }

    /**
     * Get participations
     *
     * @return Participation[]|Collection
     */
    public function getParticipationsByCoordinator(Person $coordinator)
    {
        if ($this->getCoordinators()->count() === 1) {
            return $this->getParticipations();
        }
        $iterator = $this->getParticipations()->getIterator();
        $participations = array();
        foreach ($iterator as $participation) {
            if($participation->getCoordinator()) {
                if ($participation->getCoordinator()->getPerson() == $coordinator) {
                    $participations[] = $participation;
                }
            }
        }
        return new ArrayCollection($participations);
    }

    /**
     * Get participations ordered by last name
     *
     * @return Participation[]|Collection
     */
    public function getOrderedParticipations()
    {
        $iterator = $this->getParticipations()->getIterator();
        $iterator->uasort(function (Participation $a, Participation $b) {
            return (ucfirst(strtolower($a->getParticipant()->getLastname())) < ucfirst(strtolower($b->getParticipant()->getLastname()))) ? -1 : 1;
        });
        return new ArrayCollection(iterator_to_array($iterator));
    }

    /**
     * @return Programme
     */
    public function getProgramme()
    {
        return $this->programme;
    }

    /**
     * @param Programme $programme
     * @return $this
     */
    public function setProgramme($programme)
    {
        $this->programme = $programme;

        return $this;
    }

    /**
     * Get financeMode
     *
     * @return FinanceMode
     */
    public function getFinanceMode()
    {
        return $this->financeMode;
    }

    /**
     * Set financeMode
     *
     * @param FinanceMode $financeMode
     * @return Formation
     */
    public function setFinanceMode($financeMode)
    {
        $this->financeMode = $financeMode;
    }

    /**
     * Get financeSousMode
     *
     * @return FinanceSousMode
     */
    public function getFinanceSousMode()
    {
        return $this->financeSousMode;
    }

    /**
     * Set financeSousMode
     *
     * @param FinanceSousMode $financeSousMode
     * @return Formation
     */
    public function setFinanceSousMode($financeSousMode)
    {
        $this->financeSousMode = $financeSousMode;
    }

    /**
     * @return Collection|FinanceSousMode[]
     */
    public function getFinanceSousModes()
    {
        return $this->financeSousModes;
    }

    public function addFinanceSousMode($financeSousMode)
    {
        if (!$this->financeSousModes->contains($financeSousMode)) {
            $this->financeSousModes[] = $financeSousMode;
            $this->refreshAddFinanceSousModeFactures();
        }

        return $this;
    }

    public function removeFinanceSousMode($financeSousMode)
    {
        if ($this->financeSousModes->contains($financeSousMode)) {
            $this->financeSousModes->removeElement($financeSousMode);
            $this->refreshRemoveFinanceSousModeFactures();
        }

        return $this;
    }

    public function refreshFinanceSousModeFactures() {
        $this->refreshAddFinanceSousModeFactures();
        $this->refreshRemoveFinanceSousModeFactures();
    }

    public function refreshAddFinanceSousModeFactures() {
        $financeSousModeFactures = $this->financeSousModeFactures;
        $financeSousModeFactures = is_null($financeSousModeFactures) ? [] : $financeSousModeFactures;
        foreach($this->getFinanceSousModes() as $financeSousMode) {

            if(!isset($financeSousModeFactures[$financeSousMode->getId()])) {
                $financeSousModeFactures[$financeSousMode->getId()] = ["factured" => false, "date" => null];
            }
            if($this->isPluriAnnuelle() && !isset($financeSousModeFactures[$financeSousMode->getId() . "-n1"])) {
                $financeSousModeFactures[$financeSousMode->getId() . "-n1"] = ["factured" => false, "date" => null];
            }
        }
        $this->financeSousModeFactures = $financeSousModeFactures;
    }

    public function refreshRemoveFinanceSousModeFactures() {
        $financeSousModeFactures = $this->financeSousModeFactures;
        if(!is_null($financeSousModeFactures)) {
            foreach($financeSousModeFactures as  $id => $financeSousModeFacture) {
                $haveToRemove = true;
                foreach($this->getFinanceSousModes() as $financeSousMode) {
                    if($financeSousMode->getId() == $id || ($financeSousMode->getId() . "-n1" == $id && $this->isPluriAnnuelle())) {
                        $haveToRemove = false;
                    }
                }
                if($haveToRemove) {
                    unset($financeSousModeFactures[$id]);
                    if($this->isPluriAnnuelle() && isset($financeSousModeFactures[$id . "-n1"])) {
                        unset($financeSousModeFactures[$id . "-n1"]);
                    }
                }

                if(!$this->isPluriAnnuelle() && isset($financeSousModeFactures[$id . "-n1"])) { // Suppression si la session n'est plus pluriannuelle
                    unset($financeSousModeFactures[$id . "-n1"]);
                }
            }
        }
        $this->financeSousModeFactures = $financeSousModeFactures;
    }

    public function isFacturedFinanceSousMode($id, $n1 = false) {
        $id = $n1 ? $id . "-n1" : $id;
        $financeSousModeFactures = $this->financeSousModeFactures;
        return isset($financeSousModeFactures[$id]) ? $financeSousModeFactures[$id]["factured"] : false;
    }

    public function getFacturedFinanceSousModeDate($id, $n1 = false) {
        $id = $n1 ? $id . "-n1" : $id;
        $financeSousModeFactures = $this->financeSousModeFactures;
        return $financeSousModeFactures[$id]["date"];
    }

    public function factureFinanceSousMode(FinanceSousMode $financeSousMode, $n1 = false) {
        $fnsmId = $n1 ? $financeSousMode->getId() . "-n1" : $financeSousMode->getId();
        $financeSousModeFactures = $this->financeSousModeFactures;
        $financeSousModeFactures[$fnsmId]["factured"] = !$financeSousModeFactures[$fnsmId]["factured"];
        $date = new DateTime('now');
        $financeSousModeFactures[$fnsmId]["date"] = $date->format('d/m/Y');
        $this->financeSousModeFactures = $financeSousModeFactures;
        return $financeSousModeFactures[$fnsmId]["factured"];
    }

    public function allIsFactured() {
        $isPluriannuelle = $this->isPluriAnnuelle();
        foreach ($this->getFinanceSousModes() as $financeSousMode) {
            if (
                !$this->isFacturedFinanceSousMode($financeSousMode->getId()) ||
                ($isPluriannuelle && !$this->isFacturedFinanceSousMode($financeSousMode->getId(), true))
            ) {
                return false;
            }
        }
        return true;
    }

    public function thereIsFinanceSousModeFactured() {
        $financeSousModeFactures = $this->financeSousModeFactures;
        foreach($financeSousModeFactures as $financeSousModeFacture) {
            if ($financeSousModeFacture["factured"]) {
                return true;
            }
        }
        return false;
    }

    /**
     * Set oneFinanceSousModeFactured
     *
     * @param boolean $oneFinanceSousModeFactured
     * @return Formation
     */
    public function setOneFinanceSousModeFactured($oneFinanceSousModeFactured)
    {
        $this->oneFinanceSousModeFactured = $oneFinanceSousModeFactured;

        return $this;
    }

    /**
     * Get oneFinanceSousModeFactured
     *
     * @return boolean
     */
    public function getOneFinanceSousModeFactured()
    {
        return $this->oneFinanceSousModeFactured;
    }



    /**
     * @return Collection|FinanceMode[]
     */
    public function getFinanceModes()
    {
        return $this->financeModes;
    }

    public function addFinanceMode($financeMode)
    {
        if (!$this->financeModes->contains($financeMode)) {
            $this->financeModes[] = $financeMode;
        }

        return $this;
    }

    public function removeFinanceMode($financeMode)
    {
        if ($this->financeModes->contains($financeMode)) {
            $this->financeModes->removeElement($financeMode);
        }

        return $this;
    }

    /**
     * Add invoices
     *
     * @param Invoice $invoice
     * @return Formation
     */
    public function addInvoice(Invoice $invoice)
    {
        $this->invoices[] = $invoice;

        return $this;
    }

    /**
     * Remove invoices
     *
     * @param Invoice $invoice
     */
    public function removeInvoice(Invoice $invoice)
    {
        $this->invoices->removeElement($invoice);
    }

    /**
     * Get invoices
     *
     * @return Invoice[]|Collection
     */
    public function getInvoices()
    {
        return $this->invoices;
    }

    /**
     * @param FinanceSousMode $financeSousMode
     */
    public function getInvoicePerFinanceSousMode(FinanceSousMode $financeSousMode, $year) {
        $criterias = Criteria::create()
        ->where(Criteria::expr()->eq('financeSousMode', $financeSousMode))
        ->andWhere(Criteria::expr()->eq('year', $year))
        ;
        $invoice = $this->invoices->matching($criterias)->first();
        return $invoice ? $invoice : null;
    }

    /**
     * Add invoices
     *
     * @param InvoiceProforma $invoice
     * @return Formation
     */
    public function addInvoiceProforma(InvoiceProforma $invoice)
    {
        $this->invoices_proforma[] = $invoice;

        return $this;
    }

    /**
     * Remove invoices
     *
     * @param InvoiceProforma $invoice
     */
    public function removeInvoiceProforma(InvoiceProforma $invoice)
    {
        $this->invoices_proforma->removeElement($invoice);
    }

    /**
     * Get invoices
     *
     * @return InvoiceProforma[]|Collection
     */
    public function getInvoicesProforma()
    {
        return $this->invoices_proforma;
    }

    /**
     * @param FinanceSousMode $financeSousMode
     */
    public function getInvoiceProformaPerFinanceSousMode(FinanceSousMode $financeSousMode, $year) {
        $criterias = Criteria::create()
        ->where(Criteria::expr()->eq('financeSousMode', $financeSousMode))
        ->andWhere(Criteria::expr()->eq('year', $year))
        ;
        $invoice = $this->invoices_proforma->matching($criterias)->first();
        return $invoice ? $invoice : null;
    }

    /**
     * @return string
     */
    public function getDiscr()
    {
        return self::TYPE_FORMATION;
    }

    /**
     * @return string
     */
    public function getDisplayType()
    {
        if ($this->isFormPredefined()) {
            return self::TYPE_AUDIT_PREDEFINED;
        } elseif ($this->isFormVignette()) {
            if ($this->isVfc()) {
                return self::TYPE_VFC;
            }
            return self::TYPE_VIGNETTE;
        } elseif ($this->isFormAudit()) {
            return self::TYPE_AUDIT;
        }
        return $this->getDiscr();
    }

    /**
     * @return string
     */
    public function getDisplayTypeSuffix()
    {
        //return $this->isActalians() ? "actalians" : null;
        return null;
    }


    /**
     * @return string
     */
    public function getSubType()
    {
        if ($this->isAudit()) {
            if ($this->isPredefined()) {
                return FormationAudit::TYPE_PREDEFINED;
            } else {
                return FormationAudit::TYPE_CLASSIC;
            }
        }
        return null;
    }

    /**
     * @return string
     */
    public function getApiType()
    {
        return str_replace('formation_', '', $this->getDiscr());
    }

    /**
     * @return string
     */
    public function getApiVariety()
    {
        if ($this->getProgramme()->getDurationPresentielle() > 0) {
            if ($this->getProgramme()->getDurationNotPresentielle() > 0) {
                return self::VARIETY_MIXED;
            } else {
                return self::VARIETY_PRESENTIEL;
            }
        } else if ($this->getProgramme()->getDurationNotPresentielle() > 0) {
            return self::VARIETY_ELEARNING;
        }
        return self::VARIETY_ELEARNING;
    }

    /**
     * @return string
     */
    public function getVariety()
    {
        return $this->getProgramme()->getFormatByDurations();
    }

    public function isFormFormation() {
        return $this->isAudit() || $this->isVignette() || $this->isPresentielle();
    }

    /**
     * @return bool
     */
    public function isAudit()
    {
        return $this instanceof FormationAudit || $this instanceof FormationVfc;
    }

     /**
     * @return bool
     */
    public function isVfc()
    {
        return $this instanceof FormationVfc;
    }

     /**
     * @return bool
     */
    public function isTcs()
    {
        return $this instanceof FormationTcs;
    }

    /**
     * @return bool
     */
    public function isPredefined()
    {
        return ($this instanceof FormationAudit) && $this->getAudit() && $this->getAudit()->isPredefinedType();
    }

    /**
     * @return bool
     */
    public function isDefaultType()
    {
        return ($this instanceof FormationAudit) && $this->getAudit() && $this->getAudit()->isDefaultType() && !$this->isFormVignetteAudit();
    }

    /**
     * @return bool
     */
    public function isPresentielle()
    {
        return $this instanceof FormationPresentielle;
    }

    /**
     * @return bool
     */
    public function isElearning()
    {
        return $this instanceof FormationElearning;
    }

    /**
     * @return bool
     */
    public function isElearningDiscr()
    {
        return $this->getDiscr() === self::TYPE_ELEARNING;
    }

    /**
     * @return bool
     */
    public function isSedd()
    {
        return $this instanceof FormationSedd;
    }

    /**
     * @return bool
     */
    public function isCongres()
    {
        return $this instanceof FormationCongres;
    }

    /**
     * @return bool
     */
    public function isFormationActalians()
    {
        return $this instanceof FormationActalians;
    }

    /**
     * @return bool
     */
    public function isPowerpoint()
    {
        return $this instanceof FormationPowerpoint;
    }

    /**
     * @return bool
     */
    public function isVignette()
    {
        return $this instanceof FormationVignette || $this instanceof FormationVfc;
    }

    public function isVignetteAudit(): bool
    {
        return $this instanceof FormationVignetteAudit;
    }

    /**
     * @return bool
     */
    public function isFormVignette()
    {
        return  in_array($this->getFormType(), [self::FORM_TYPE_VIGNETTE, self::FORM_TYPE_VIGNETTE_AUDIT, self::FORM_TYPE_AUDIT_VIGNETTE]);
    }

        /**
     * @return bool
     */
    public function isFormVignetteAudit()
    {
        return  in_array($this->getFormType(), [self::FORM_TYPE_VIGNETTE_AUDIT, self::FORM_TYPE_AUDIT_VIGNETTE]);
    }

    /**
     * @return bool
     */
    public function isFormPredefined()
    {
        return $this->getFormType() == self::FORM_TYPE_PREDEFINED;
    }

    /**
     * @return bool
     */
    public function isFormPresentielle()
    {
        return $this->getFormType() == self::FORM_TYPE_SURVEY;
    }

    /**
     * @return bool
     */
    public function isFormAudit()
    {
        return $this->getFormType() == self::FORM_TYPE_AUDIT;
    }

    public function isVignetteFirst() {
        return $this->getFormType() == self::FORM_TYPE_VIGNETTE_AUDIT;
    }

    public function isAuditFirst() {
        return $this->getFormType() == self::FORM_TYPE_AUDIT_VIGNETTE;
    }

    /**
     * @return bool
     */
    public function isClasseVirtuelleNationale() {
        return $this->getProgramme()->isClasseVirtuelle() && empty($this->getCity());
    }

    /**
     * @return bool
     */
    public function isCms()
    {
        if ($this->getCoordinators()) {
            foreach($this->getCoordinators() as $coordinator) {
                if(!$coordinator->getPerson()->isCms()) {
                    return false;
                }
            }
        }
        return true;
    }

    // /**
    //  * @return bool
    //  */
    // public function isCoordinatorLbi()
    // {
    //     return $this->getProgramme()->isCoordinatorLbi();
    // }

    /**
     * @return bool
     */
    public function acceptForm()
    {
        return $this instanceof FormationWithFormInterface;
    }

    /**
     * Set emargement
     *
     * @param string $emargement
     * @return Formation
     */
    public function setEmargement($emargement)
    {
        $this->emargement = $emargement;

        return $this;
    }

    /**
     * Get emargement
     *
     * @return string
     */
    public function getEmargement()
    {
        return $this->emargement;
    }

    /**
     * @return File
     */
    public function getEmargementFile()
    {
        return $this->emargementFile;
    }

    /**
     * @param File $emargementFile
     */
    public function setEmargementFile(File $emargementFile = null)
    {
        $this->emargementFile = $emargementFile;
        if ($this->emargementFile instanceof UploadedFile) {
            $this->setPreUpdate();
        }
        return $this;
    }

    /**
     * Add emargementFiles
     *
     * @param EmargementFile $emargementFile
     * @return Formation
     */
    public function addEmargementFile(EmargementFile $emargementFile)
    {
        $this->emargementFiles[] = $emargementFile;

        return $this;
    }

    /**
     * Remove emargementFiles
     *
     * @param EmargementFile $emargementFile
     */
    public function removeEmargementFile(EmargementFile $emargementFile)
    {
        $this->emargementFiles->removeElement($emargementFile);
    }

    /**
     * Get emargementFiles
     *
     * @return EmargementFile[]|Collection
     */
    public function getEmargementFiles()
    {
        return $this->emargementFiles;
    }

    /**
     * Set factureFormer
     *
     * @param string $factureFormer
     * @return Formation
     */
    public function setFactureFormer($factureFormer)
    {
        $this->factureFormer = $factureFormer;

        return $this;
    }

    /**
     * Get factureFormer
     *
     * @return string
     */
    public function getFactureFormer()
    {
        return $this->factureFormer;
    }

    /**
     * @return File
     */
    public function getFactureFormerFile()
    {
        return $this->factureFormerFile;
    }

    /**
     * @param File $factureFormerFile
     */
    public function setFactureFormerFile(File $factureFormerFile = null)
    {
        $this->factureFormerFile = $factureFormerFile;
        if ($this->factureFormerFile instanceof UploadedFile) {
            $this->setPreUpdate();
        }
        return $this;
    }

    /**
     * Set factureRestauration
     *
     * @param string $factureRestauration
     * @return Formation
     */
    public function setFactureRestauration($factureRestauration)
    {
        $this->factureRestauration = $factureRestauration;

        return $this;
    }

    /**
     * Get factureRestauration
     *
     * @return string
     */
    public function getFactureRestauration()
    {
        return $this->factureRestauration;
    }

    /**
     * @return File
     */
    public function getFactureRestaurationFile()
    {
        return $this->factureRestaurationFile;
    }

    /**
     * @param File $factureRestaurationFile
     */
    public function setFactureRestaurationFile(File $factureRestaurationFile = null)
    {
        $this->factureRestaurationFile = $factureRestaurationFile;
        if ($this->factureRestaurationFile instanceof UploadedFile) {
            $this->setPreUpdate();
        }
        return $this;
    }

    /**
     * Set justificatifSuivi
     *
     * @param string $justificatifSuivi
     * @return Formation
     */
    public function setJustificatifSuivi($justificatifSuivi)
    {
        $this->justificatifSuivi = $justificatifSuivi;

        return $this;
    }

    /**
     * Get justificatifSuivi
     *
     * @return string
     */
    public function getJustificatifSuivi()
    {
        return $this->justificatifSuivi;
    }

    /**
     * @return File
     */
    public function getJustificatifSuiviFile()
    {
        return $this->justificatifSuiviFile;
    }

    /**
     * @param File $justificatifSuiviFile
     */
    public function setJustificatifSuiviFile(File $justificatifSuiviFile = null)
    {
        $this->justificatifSuiviFile = $justificatifSuiviFile;
        if ($this->justificatifSuiviFile instanceof UploadedFile) {
            $this->setPreUpdate();
        }
        return $this;
    }

    /**
     * Set contratFormateur
     *
     * @param string $contratFormateur
     * @return Formation
     */
    public function setContratFormateur($contratFormateur)
    {
        $this->contratFormateur = $contratFormateur;

        return $this;
    }

    /**
     * Get contratFormateur
     *
     * @return string
     */
    public function getContratFormateur()
    {
        return $this->contratFormateur;
    }

    /**
     * @return File
     */
    public function getContratFormateurFile()
    {
        return $this->contratFormateurFile;
    }

    /**
     * @param File $contratFormateurFile
     */
    public function setContratFormateurFile(File $contratFormateurFile = null)
    {
        $this->contratFormateurFile = $contratFormateurFile;
        if ($this->contratFormateurFile instanceof UploadedFile) {
            $this->setPreUpdate();
        }
        return $this;
    }

    /**
     * Set contratFormateur2
     *
     * @param string $contratFormateur2
     * @return Formation
     */
    public function setContratFormateur2($contratFormateur2)
    {
        $this->contratFormateur2 = $contratFormateur2;

        return $this;
    }

    /**
     * Get contratFormateur2
     *
     * @return string
     */
    public function getContratFormateur2()
    {
        return $this->contratFormateur2;
    }

    /**
     * @return File
     */
    public function getContratFormateur2File()
    {
        return $this->contratFormateur2File;
    }

    /**
     * @param File $contratFormateur2File
     */
    public function setContratFormateur2File(File $contratFormateur2File = null)
    {
        $this->contratFormateur2File = $contratFormateur2File;
        if ($this->contratFormateur2File instanceof UploadedFile) {
            $this->setPreUpdate();
        }
        return $this;
    }

    /**
     * Set contratFormateur3
     *
     * @param string $contratFormateur3
     * @return Formation
     */
    public function setContratFormateur3($contratFormateur3)
    {
        $this->contratFormateur3 = $contratFormateur3;

        return $this;
    }

    /**
     * Get contratFormateur3
     *
     * @return string
     */
    public function getContratFormateur3()
    {
        return $this->contratFormateur3;
    }

    /**
     * @return File
     */
    public function getContratFormateur3File()
    {
        return $this->contratFormateur3File;
    }

    /**
     * @param File $contratFormateur3File
     */
    public function setContratFormateur3File(File $contratFormateur3File = null)
    {
        $this->contratFormateur3File = $contratFormateur3File;
        if ($this->contratFormateur3File instanceof UploadedFile) {
            $this->setPreUpdate();
        }
        return $this;
    }

    /**
     * Set contratFormateur4
     *
     * @param string $contratFormateur4
     * @return Formation
     */
    public function setContratFormateur4($contratFormateur4)
    {
        $this->contratFormateur4 = $contratFormateur4;

        return $this;
    }

    /**
     * Get contratFormateur4
     *
     * @return string
     */
    public function getContratFormateur4()
    {
        return $this->contratFormateur4;
    }

    /**
     * @return File
     */
    public function getContratFormateur4File()
    {
        return $this->contratFormateur4File;
    }

    /**
     * @param File $contratFormateur4File
     */
    public function setContratFormateur4File(File $contratFormateur4File = null)
    {
        $this->contratFormateur4File = $contratFormateur4File;
        if ($this->contratFormateur4File instanceof UploadedFile) {
            $this->setPreUpdate();
        }
        return $this;
    }

    /**
     * Set contratFormateur5
     *
     * @param string $contratFormateur5
     * @return Formation
     */
    public function setContratFormateur5($contratFormateur5)
    {
        $this->contratFormateur5 = $contratFormateur5;

        return $this;
    }

    /**
     * Get contratFormateur5
     *
     * @return string
     */
    public function getContratFormateur5()
    {
        return $this->contratFormateur5;
    }

    /**
     * @return File
     */
    public function getContratFormateur5File()
    {
        return $this->contratFormateur5File;
    }

    /**
     * @param File $contratFormateur5File
     */
    public function setContratFormateur5File(File $contratFormateur5File = null)
    {
        $this->contratFormateur5File = $contratFormateur5File;
        if ($this->contratFormateur5File instanceof UploadedFile) {
            $this->setPreUpdate();
        }
        return $this;
    }

    /**
     * Set topoFormateur
     *
     * @param string $topoFormateur
     * @return Formation
     */
    public function setTopoFormateur($topoFormateur)
    {
        $this->topoFormateur = $topoFormateur;

        return $this;
    }

    /**
     * Get topoFormateur
     *
     * @return string
     */
    public function getTopoFormateur()
    {
        return $this->topoFormateur;
    }

    /**
     * @return File
     */
    public function getTopoFormateurFile()
    {
        return $this->topoFormateurFile;
    }

    /**
     * @param File $topoFormateurFile
     */
    public function setTopoFormateurFile(File $topoFormateurFile = null)
    {
        $this->topoFormateurFile = $topoFormateurFile;
        if ($this->topoFormateurFile instanceof UploadedFile) {
            $this->setPreUpdate();
        }
        return $this;
    }

    /**
     * @return int
     */
    public function getSessionNumber()
    {
        return $this->sessionNumber;
    }

    /**
     * @param int $sessionNumber
     * @return Formation
     */
    public function setSessionNumber($sessionNumber)
    {
        $this->sessionNumber = $sessionNumber;

        return $this;
    }

    /**
     * @return boolean
     */
    public function isNoMailing()
    {
        return $this->noMailing;
    }

    /**
     * @param boolean $noMailing
     * @return Formation
     */
    public function setNoMailing($noMailing)
    {
        $this->noMailing = $noMailing;

        return $this;
    }

    /**
     * @return bool
     */
    public function isExpert()
    {
        return $this->expert;
    }

    /**
     * @param bool $expert
     * @return Formation
     */
    public function setExpert($expert)
    {
        $this->expert = $expert;
        return $this;
    }

    /**
     * @return \DateTime
     */
    public function getFormOpeningDate()
    {
        return $this->formOpeningDate;
    }

    /**
     * @return \DateTime
     */
    public function getFormOpeningDateApi()
    {
        return $this->formOpeningDate;
    }

    /**
     * @param \DateTime $formOpeningDate
     * @return Formation
     */
    public function setFormOpeningDate($formOpeningDate)
    {
        $this->formOpeningDate = $formOpeningDate;

        return $this;
    }

    /**
     * @return \DateTime
     */
    public function getFormClosingDate()
    {
        return $this->formClosingDate;
    }

    /**
     * @return \DateTime
     */
    public function getFormClosingDateApi()
    {
        return $this->formClosingDate;
    }

    /**
     * @return string
     */
    public function getCommentaireNotif()
    {
        return $this->commentaireNotif;
    }

    /**
     * @param string $commentaireNotif
     * @return Programme
     */
    public function setCommentaireNotif($commentaireNotif)
    {
        $this->commentaireNotif = $commentaireNotif;
        return $this;
    }
    /**
     * @param \DateTime $formClosingDate
     * @return Formation
     */
    public function setFormClosingDate($formClosingDate)
    {
        $this->formClosingDate = $formClosingDate;

        return $this;
    }

    /**
     * Récupère le CA Total de la formation
     * Passer $n1 à true pour récupérer le CA de l'année suivante
     * @param bool $n1
     * @return ?float
     */
    public function getCaTotal(bool $n1 = false): ?float
    {
        if (!$this->getParticipations()) {
            return 0;
        }
        $ca = array_reduce(
            $this->getParticipations()
                ->filter(function (Participation $p) {
                    return !$p->isArchived();
                })
                ->map(function(Participation $p) use($n1) { return $n1 ? $p->getPriceYearN1() : $p->getPrice();})->toArray(),
            function($carry, $item) {
                $carry += $item;
                return $carry;
            }
        );
        if ($this->isSedd()) {
            $ca = $ca * $this->getDaysCount();
        }
        return $ca;
    }

    /**
     * @param FinanceSousMode $financeSousMode
     * @return int
     */
    public function getCaTotalPerMode(FinanceSousMode $financeSousMode, $n1 = false)
    {
        if (!$this->getParticipationsPerMode($financeSousMode)) {
            return 0;
        }
        $ca = array_reduce(
            $this->getParticipationsPerMode($financeSousMode)->map(function(Participation $p) use ($n1) { return $n1 ? $p->getPriceYearN1() : $p->getPrice();})->toArray(),
            function($carry, $item) {
                $carry += $item;
                return $carry;
            }
        );
        if ($this->isSedd()) {
            $ca = $ca * $this->getDaysCount();
        }
        return $ca;
    }

    /**
     * @param Coordinator $person
     * @return int
     */
    public function getCaTotalByCoordinator(Person $person, $n1 = false)
    {
        if (!$this->getParticipations()) {
            return 0;
        }
        if(count($this->getCoordinators()) > 1) {
            $ca = array_reduce(
                $this->getParticipations()->filter(function(Participation $p) use ($person) {
                    if(!$p->getCoordinator()) {
                        return false;
                    }
                    if ($p->isArchived()) {
                        return false;
                    }
                    return $p->getCoordinator()->getPerson() === $person;
                })->map(function(Participation $p) use ($person, $n1) {
                        return $n1 ? $p->getPriceYearN1() : $p->getPrice();
                })->toArray(),
                function($carry, $item) {
                    $carry += $item;
                    return $carry;
                }
            );
        }
        else {
            $ca = array_reduce(
                $this->getParticipations()
                ->filter(function(Participation $p) use ($person) {
                    if (!$p->getCoordinator()) {
                        return false;
                    }
                    if ($p->isArchived()) {
                        return false;
                    }
                    return true;
                })
                ->map(function(Participation $p) use ($person, $n1) {
                    return $n1 ? $p->getPriceYearN1() : $p->getPrice();
                })->toArray(),
                function($carry, $item) {
                    $carry += $item;
                    return $carry;
                }
            );
        }
        if ($this->isSedd()) {
            $ca = $ca * $this->getDaysCount();
        }
        return $ca;
    }

    public function getToken(): string
    {
        return md5(sprintf('%s%s%s',
            $this->getCreatedAt()->format('U'), $this->getId(), $this->getDiscr()
        ));
    }

    public function getParticipantsPricesDistribution() {
        $repartitions = [];
        foreach ($this->getParticipations() as $participation) {
            $p = (string) $participation->getPrice();
            if (!isset($repartitions[$p])) {
                $repartitions[$p] = 0;
            }
            $repartitions[$p]++;
        }
        return $repartitions;
    }

    public function getParticipantsPricesDistributionPerMode(FinanceSousMode $financeSousMode) {
        $repartitions = [];
        foreach ($this->getParticipationsPerMode($financeSousMode) as $participation) {
            $p = (string) $participation->getPrice();
            if (!isset($repartitions[$p])) {
                $repartitions[$p] = 0;
            }
            $repartitions[$p]++;
        }
        return $repartitions;
    }

    public function getParticipantsPricesDistributionPerModeAndCoordinator(FinanceSousMode $financeSousMode, $n1 = false) {
        $repartitions = [];
        /** @var Coordinator $coordinator */
        foreach ($this->getCoordinators() as $coordinator) {
            $identifier = $coordinator->getPerson()->getCrIdentifier() ?? $coordinator->getPerson()->getId();
            foreach ($this->getParticipationsPerModeAndCoordinator($financeSousMode, $coordinator) as $participation) {
                $p = $n1 ? (string) $participation->getPriceYearN1() : (string) $participation->getPrice();
                if (!isset($repartitions[$identifier][$p])) {
                    $repartitions[$identifier][$p] = 0;
                }
                $repartitions[$identifier][$p]++;
            }
        }
        return $repartitions;
    }

    public function getUploadFileDate($key) {
        $getter = "get" . ucfirst($key);
        if (method_exists($this, $getter)){
            $file = call_user_func_array( array($this, $getter), array());
        }
        if ($file && file_exists($file)) {
            return date('d/m/Y', filectime($file));
        }
    }

    public function getDaysCount() {
        return $this->getStartDate()->diff($this->getEndDate())->format("%a") + 1;
    }

    // public function getTotalDuration() {
    //     return $this->durationNotPresentielle + $this->durationPresentielle;
    // }

    public function hasLinkedForm() {
        if ($this->isAudit() || $this->isTcs()) {
            return !is_null($this->getAudit());
        } else if ($this->isPresentielle()) {
            return !is_null($this->getQuestionnaire());
        }
        return false;
    }

    /**
     * @return Audit|Survey|null
     */
    public function getForm(): ?QuestionnaireInterface {
        if ($this->isAudit() || $this->isTcs()) {
            return $this->getAudit();
        } else if ($this->isPresentielle()) {
            return $this->getQuestionnaire();
        }
        return null;
    }

    public function getFormType() {
        return $this->getProgramme()->getFormType();
    }

    public function getFormTypePre() {
        if ($this->isFormVignetteAudit()) {
            if ($this->isVignetteFirst()) {
                return self::FORM_TYPE_VIGNETTE;
            }
            return self::FORM_TYPE_AUDIT;
        }
        if($this->getFormType() == "survey") {
            return "survey";
        }

        if($this->getFormType() == "tcs") {
            return "tcs";
        }
        return $this->getForm() ? $this->getForm()->getType() : $this->getFormType();
    }

    public function getFormTypePost() {
        if ($this->isFormVignetteAudit()) {
            if ($this->isVignetteFirst()) {
                return self::FORM_TYPE_AUDIT;
            }
            return self::FORM_TYPE_VIGNETTE;
        }
        if($this->getFormType() == "survey") {
            return "survey";
        }
        return $this->getForm() ? $this->getForm()->getType() : $this->getFormType();
    }

    public function isFormPreVignette() {
        return ($this->isVignette() && !$this->isFormVignetteAudit()) || ($this->isFormVignetteAudit() && $this->isVignetteFirst());
    }

    public function isFormPostVignette() {
        return ($this->isVignette() && !$this->isFormVignetteAudit()) || ($this->isFormVignetteAudit() && $this->isAuditFirst());
    }

    public function isFormPreDefault() {
        return $this->isDefaultType() || ($this->isFormVignetteAudit() && $this->isAuditFirst());
    }

    public function isFormPostDefault() {
        return $this->isDefaultType() || ($this->isFormVignetteAudit() && $this->isVignetteFirst());
    }

    public function canSendEmail() {
        return !$this->isNoMailing();
    }

    /**
     * @param $type
     * @return string
     */
    public function getGeneratedFileBasePath($type) {
        return __DIR__ . sprintf("/../../../../uploads/formations/%s/", $type);
    }

    /**
     * @param $type
     * @param FinanceSousMode|null $financeSousMode
     * @return string
     */
    public function getGeneratedFileTmpBasePath($type, FinanceSousMode $financeSousMode = null) {
        return __DIR__ . sprintf("/../../../../uploads/formations/%s/%s%s/", $type, $this->id, $financeSousMode ? ("-" . $financeSousMode->getId()) : "");
    }

    /**
     * @param $type
     * @param FinanceSousMode|null $financeSousMode
     * @return string
     */
    public function getGeneratedFilePath($type, FinanceSousMode $financeSousMode = null) {
        return sprintf($this->getGeneratedFileBasePath($type) . "%s%s.pdf", $this->id, $financeSousMode ? ("-" . $financeSousMode->getId()) : "");
    }

    /**
     * @param $type
     * @param FinanceSousMode|null $financeSousMode
     * @return string
     */
    public function getGeneratedFileTmpPath($type, FinanceSousMode $financeSousMode = null) {
        return sprintf($this->getGeneratedFileTmpBasePath($type, $financeSousMode) . "%s%s.pdf", $this->id, $financeSousMode ? ("-" . $financeSousMode->getId()) : "");
    }

    /**
     * @param $type
     * @param $nb
     * @param FinanceSousMode|null $financeSousMode
     * @return string
     */
    public function getGeneratedFileBatchTmpPath($type, $nb, FinanceSousMode $financeSousMode = null) {
        return sprintf($this->getGeneratedFileTmpBasePath($type, $financeSousMode) . "%s%s-%s.pdf", $this->id, $financeSousMode ? ("-" . $financeSousMode->getId()) : "", $nb);
    }

    /**
     * @param $type
     * @param FinanceSousMode|null $financeSousMode
     * @return string
     */
    public function getGeneratedFileErrorPath($type, FinanceSousMode $financeSousMode = null) {
        return sprintf($this->getGeneratedFileBasePath($type) . "%s%s.log", $this->id, $financeSousMode ? ("-" . $financeSousMode->getId()) : "");
    }

    /**
     * @param $type
     * @param FinanceSousMode|null $financeSousMode
     * @return bool
     */
    public function hasGeneratedFile($type, FinanceSousMode $financeSousMode = null) {
        return $this->getGeneratedFilePath($type, $financeSousMode) && file_exists($this->getGeneratedFilePath($type, $financeSousMode));
    }

    /**
     * @param $type
     * @param FinanceSousMode|null $financeSousMode
     * @return bool
     */
    public function hasGeneratedFileError($type, FinanceSousMode $financeSousMode = null) {
        return ($this->getGeneratedFileErrorPath($type, $financeSousMode) && file_exists($this->getGeneratedFileErrorPath($type, $financeSousMode)))
            || ($this->getGeneratedFilePath($type, $financeSousMode) && file_exists($this->getGeneratedFilePath($type, $financeSousMode)) && filesize($this->getGeneratedFilePath($type, $financeSousMode)) === 0)
            && !is_dir($this->getGeneratedFileTmpBasePath($type, $financeSousMode));
    }

    /**
     * @param $type
     * @param FinanceSousMode|null $financeSousMode
     * @return bool
     */
    public function generateFileIsFinished($type, FinanceSousMode $financeSousMode = null) {
        if ($this->hasGeneratedFile($type, $financeSousMode)) {
            return filesize($this->getGeneratedFilePath($type, $financeSousMode)) > 0;
        }
        return false;
    }

    static public function getClassByType($type) {
        return self::TYPES[$type];
    }

    static public function getFormClassByType($type) {
        return self::FORM_TYPES[$type];
    }

    /**
     * Add formateurFiles
     *
     * @param FormateurFiles $formateurFiles
     * @return Formation
     */
    public function addFormateurFiles(FormateurFiles $formateurFiles)
    {
        $this->formateurFiles[] = $formateurFiles;

        return $this;
    }

    /**
     * Remove formateurFiles
     *
     * @param FormateurFiles $formateurFiles
     */
    public function removeFormateurFile(FormateurFiles $formateurFiles)
    {
        $this->formateurFiles->removeElement($formateurFiles);
    }

    /**
     * Get formateurFiles
     *
     * @return FormateurFiles[]|Collection
     */
    public function getFormateurFiles()
    {
        return $this->formateurFiles;
    }

    /**
     * Add coordinatorFiles
     *
     * @param CoordinatorFiles $coordinatorFiles
     * @return Formation
     */
    public function addCoordinatorFiles(CoordinatorFiles $coordinatorFiles)
    {
        $this->coordinatorFiles[] = $coordinatorFiles;

        return $this;
    }

    /**
     * Remove coordinatorFiles
     *
     * @param CoordinatorFiles $coordinatorFiles
     */
    public function removeCoordinatorFile(CoordinatorFiles $coordinatorFiles)
    {
        $this->coordinatorFiles->removeElement($coordinatorFiles);
    }

    /**
     * Get coordinatorFiles
     *
     * @return CoordinatorFiles[]|Collection
     */
    public function getCoordinatorFiles()
    {
        return $this->coordinatorFiles;
    }

    /**
     * Add topoFiles
     *
     * @param TopoFiles $topoFiles
     * @return Formation
     */
    public function addTopoFiles(TopoFiles $topoFiles)
    {
        $this->topoFiles[] = $topoFiles;

        return $this;
    }

    /**
     * Remove topoFiles
     *
     * @param TopoFiles $topoFiles
     */
    public function removeTopoFile(TopoFiles $topoFiles)
    {
        $this->topoFiles->removeElement($topoFiles);
    }

    /**
     * Get topoFiles
     *
     * @return TopoFiles[]|Collection
     */
    public function getTopoFiles()
    {
        return $this->topoFiles;
    }

    /**
     * Get first topoFiles
     *
     * @return TopoFiles
     */
    public function getFirstTopoFile()
    {
        foreach($this->getTopoFiles() as $topoFile) {
            return $topoFile;
        }
        return false;
    }

    /**
     * @return int
     */
    public function getManualParticipantCount()
    {
        return $this->manualParticipantCount;
    }

    /**
     * @param int $manualParticipantCount
     * @return Formation
     */
    public function setManualParticipantCount($manualParticipantCount)
    {
        $this->manualParticipantCount = $manualParticipantCount;
        return $this;
    }

    /**
     * @return float
     */
    public function getCost()
    {
        return $this->cost;
    }

    /**
     * @param float $cost
     * @return Formation
     */
    public function setCost($cost)
    {
        $this->cost = $cost;
        return $this;
    }

    /**
     * Set address
     *
     * @param string $address
     * @return Programme
     */
    public function setAddress($address)
    {
        $this->address = $address;

        return $this;
    }

    /**
     * Get address
     *
     * @return string
     */
    public function getAddress()
    {
        return $this->address;
    }

    /**
     * @return string
     */
    public function getAddress2()
    {
        return $this->address2;
    }

    /**
     * @param string $address2
     * @return Programme
     */
    public function setAddress2($address2)
    {
        $this->address2 = $address2;

        return $this;
    }

    /**
     * Set city
     *
     * @param string $city
     * @return Programme
     */
    public function setCity($city)
    {
        $this->city = $city;

        return $this;
    }

    /**
     * Get city
     *
     * @return string
     */
    public function getCity()
    {
        return $this->city;
    }

    /**
     * Set zipCode
     *
     * @param string $zipCode
     * @return Programme
     */
    public function setZipCode($zipCode)
    {
        $this->zipCode = $zipCode;

        return $this;
    }

    /**
     * Get zipCode
     *
     * @return string
     */
    public function getZipCode()
    {
        return $this->zipCode;
    }

        /**
     * Set startDate
     *
     * @param \DateTime $startDate
     * @return Programme
     */
    public function setStartDate($startDate)
    {
        $this->startDate = $startDate;

        return $this;
    }

    /**
     * Get startDate
     *
     * @return \DateTime
     */
    public function getStartDate(): ?DateTime
    {
        return $this->startDate;
    }

    /**
     * Set endDate
     *
     * @param \DateTime $endDate
     * @return Formation
     */
    public function setEndDate($endDate): Formation
    {
        if($endDate == '') {
            $endDate = $this->getStartDate();
        }
        $this->endDate = $endDate;

        return $this;
    }

    /**
     * Get endDate
     *
     * @return \DateTime
     */
    public function getEndDate()
    {
        return $this->endDate;
    }

    public function getSearchStartDate(): ?DateTime
    {
        return $this->searchStartDate;
    }

    public function setSearchStartDate(?DateTime $searchStartDate): Formation
    {
        $this->searchStartDate = $searchStartDate;
        return $this;
    }

    public function getSearchEndDate(): ?DateTime
    {
        return $this->searchEndDate;
    }

    public function setSearchEndDate(?DateTime $searchEndDate): Formation
    {
        $this->searchEndDate = $searchEndDate;
        return $this;
    }

    public function getQuerySearchStartDate(): ?DateTime
    {
        return $this->querySearchStartDate;
    }

    public function setQuerySearchStartDate(?DateTime $querySearchStartDate): void
    {
        $this->querySearchStartDate = $querySearchStartDate;
    }

    public function getQuerySearchEndDate(): ?DateTime
    {
        return $this->querySearchEndDate;
    }

    public function setQuerySearchEndDate(?DateTime $querySearchEndDate): void
    {
        $this->querySearchEndDate = $querySearchEndDate;
    }

    public function getAttestationMissingDate(): ?DateTime
    {
        return $this->attestationMissingDate;
    }

    public function setAttestationMissingDate(?DateTime $attestationMissingDate): Formation
    {
        $this->attestationMissingDate = $attestationMissingDate;
        return $this;
    }

    public function getAttestationMissingDateN1(): ?DateTime
    {
        return $this->attestationMissingDateN1;
    }

    public function setAttestationMissingDateN1(?DateTime $attestationMissingDateN1): Formation
    {
        $this->attestationMissingDateN1 = $attestationMissingDateN1;
        return $this;
    }

    public function isMissingAttestation() {
        $today = (new \DateTime())->setTime(0, 0);
        return $this->getAttestationMissingDate() <= $today;
    }

    public function isMissingAttestationN1() {
        $today = (new \DateTime())->setTime(0, 0);
        if (!$this->isPluriAnnuelle() || !$this->getAttestationMissingDateN1()) {
            return false;
        }
        return $this->getAttestationMissingDateN1() <= $today;
    }

    public function getAttestationEcheanceN() {
        if (!$this->isPluriAnnuelle()) {
            return $this->getClosingDate();
        }
        return $this->getLastUnityDateOfYear($this->getOpeningDate()->format("Y"));
    }

    public function getAttestationEcheanceN1() {
        if (!$this->isPluriAnnuelle()) {
            return false;
        }
        return $this->getClosingDate();
    }

    /**
     * Add formateurs
     *
     * @param Formateur $formateurs
     * @return Programme
     */
    public function addFormateur(Formateur $formateurs)
    {
        $this->formateurs[] = $formateurs;
        $formateurs->setProgramme($this->getProgramme());
        $formateurs->setFormation($this);

        return $this;
    }

    /**
     * Remove formateurs
     *
     * @param Formateur $formateurs
     */
    public function removeFormateur(Formateur $formateurs)
    {
        $this->formateurs->removeElement($formateurs);
    }

    /**
     * Get formateurs
     *
     * @return Collection
     */
    public function getFormateurs()
    {
        return $this->formateurs;
    }

    /**
     * Add coordinators
     *
     * @param Coordinator $coordinators
     * @return Programme
     */
    public function addCoordinator(Coordinator $coordinators)
    {
        $this->coordinators[] = $coordinators;
        $coordinators->setFormation($this);
        return $this;
    }

    /**
     * Remove coordinators
     *
     * @param Coordinator $coordinators
     */
    public function removeCoordinator(Coordinator $coordinators)
    {
        $this->coordinators->removeElement($coordinators);
    }

    /**
     * Get coordinators
     *
     * @return Collection
     */
    public function getCoordinators()
    {
        return $this->coordinators;
    }

    public function eraseCoordinators()
    {
        $this->coordinators->clear();
        return $this;
    }

    public function setCoordinators(ArrayCollection|Collection|array $coordinators): self
    {
        $this->coordinators = $coordinators;

        return $this;
    }

/**
     * @return float
     */
    public function getCostKilometres()
    {
        return $this->costKilometres;
    }

    /**
     * @param float $costKilometres
     * @return Programme
     */
    public function setCostKilometres($costKilometres)
    {
        $this->costKilometres = $costKilometres;
        return $this;
    }

    /**
     * @return float
     */
    public function getCostBadges()
    {
        return $this->costBadges;
    }

    /**
     * @param float $costBadges
     * @return Programme
     */
    public function setCostBadges($costBadges)
    {
        $this->costBadges = $costBadges;
        return $this;
    }

    /**
     * @return float
     */
    public function getCostRetrocessions()
    {
        return $this->costRetrocessions;
    }

    /**
     * @param float $costRetrocessions
     * @return Programme
     */
    public function setCostRetrocessions($costRetrocessions)
    {
        $this->costRetrocessions = $costRetrocessions;
        return $this;
    }

    /**
     * @return float
     */
    public function getCostMateriel()
    {
        return $this->costMateriel;
    }

    /**
     * @param float $costMateriel
     * @return Programme
     */
    public function setCostMateriel($costMateriel)
    {
        $this->costMateriel = $costMateriel;
        return $this;
    }

    /**
     * @return float
     */
    public function getCostDivers()
    {
        return $this->costDivers;
    }

    /**
     * @param float $costDivers
     * @return Programme
     */
    public function setCostDivers($costDivers)
    {
        $this->costDivers = $costDivers;
        return $this;
    }

    /**
     * @return string
     */
    public function getRegion()
    {
        return $this->region;
    }

    /**
     * @param string $region
     * @return Programme
     */
    public function setRegion($region)
    {
        $this->region = $region;
        return $this;
    }

    /**
     * @return mixed
     */
    public function getLatitude()
    {
        return $this->latitude;
    }

    /**
     * @param mixed $latitude
     * @return Programme
     */
    public function setLatitude($latitude)
    {
        $this->latitude = $latitude;
        return $this;
    }

    /**
     * @return string
     */
    public function getLongitude()
    {
        return $this->longitude;
    }

    /**
     * @param string $longitude
     * @return Programme
     */
    public function setLongitude($longitude)
    {
        $this->longitude = $longitude;
        return $this;
    }

    /**
     * Set messageRappel
     *
     * @param string $messageRappel
     * @return Programme
     */
    public function setMessageRappel($messageRappel)
    {
        $this->messageRappel = $messageRappel;

        return $this;
    }

    /**
     * Get messageRappel
     *
     * @return string
     */
    public function getMessageRappel()
    {
        return $this->messageRappel;
    }

    /**
     * @return Collection|EvaluationGlobalAnswer[]
     */
    public function getEvaluationAnswers()
    {
        return $this->evaluationAnswers;
    }

    /**
     * @param Collection<int, EvaluationGlobalAnswer> $evaluationAnswers
     * @return Formation
     */
    public function setEvaluationAnswers($evaluationAnswers)
    {
        $this->evaluationAnswers = $evaluationAnswers;
        return $this;
    }

    /**
     * @return mixed
     */
    public function getZoomId()
    {
        return $this->zoomId;
    }

    /**
     * @param mixed $zoomId
     * @return Formation
     */
    public function setZoomId($zoomId)
    {
        $this->zoomId = $zoomId;
        return $this;
    }

    /**
     * @return string
     */
    public function getZoomLink()
    {
        return $this->zoomLink;
    }

    /**
     * @param string $zoomLink
     * @return Formation
     */
    public function setZoomLink($zoomLink)
    {
        $this->zoomLink = $zoomLink;
        return $this;
    }

    /**
     * Set accessibilite
     *
     * @param string $accessibilite
     * @return Formation
     */
    public function setAccessibilite($accessibilite)
    {
        $this->accessibilite = $accessibilite;

        return $this;
    }

    /**
     * Get accessibilite
     *
     * @return string
     */
    public function getAccessibilite()
    {
        return $this->accessibilite;
    }

    /**
     * Set commentaire
     *
     * @param string $commentaire
     * @return Formation
     */
    public function setCommentaire($commentaire)
    {
        $this->commentaire = $commentaire;

        return $this;
    }

    /**
     * Get commentaire
     *
     * @return string
     */
    public function getCommentaire()
    {
        return $this->commentaire;
    }

    /**
     * Set outil
     *
     * @param string $outil
     * @return Formation
     */
    public function setOutil($outil)
    {
        $this->outil = $outil;

        return $this;
    }

    /**
     * Get outil
     *
     * @return string
     */
    public function getOutil()
    {
        return $this->outil;
    }

         /**
     * @return bool
     */
    public function isPrivate()
    {
        return $this->private;
    }

    /**
     * @param bool $private
     */
    public function setPrivate($private): self
    {
        $this->private = $private;
        return $this;
    }

    /**
     * Set theme
     *
     * @param AuditCategory $theme
     *
     */
    public function setTheme(AuditCategory $theme): self
    {
        $this->theme = $theme;

        return $this;
    }

    /**
     * Get theme
     *
     * @return AuditCategory
     */
    public function getTheme()
    {
        return $this->theme;
    }

    /**
     * @param string $partenariat
     */
    public function getPartenariat() {
        return $this->partenariat;
    }

    /**
     * @param string $partenariat
     * @return Formation
     */
    public function setPartenariat($partenariat) {
        $this->partenariat = $partenariat;
        return $this;
    }

    /**
     * @param string $ots
     */
    public function getOts() {
        return $this->ots;
    }

    /**
     * @param string $ots
     * @return Formation
     */
    public function setOts($ots) {
        $this->ots = $ots;
    }

    /**
     * @param string $zoomStartTime
     */
    public function getZoomStartTime() {
        return $this->zoomStartTime;
    }

    /**
     * @param string $zoomStartTime
     * @return Formation
     */
    public function setZoomStartTime($zoomStartTime) {
        $this->zoomStartTime = $zoomStartTime;
    }

    /**
     * @param string $zoomEndTime
     */
    public function getZoomEndTime() {
        return $this->zoomEndTime;
    }

    /**
     * @param string $zoomEndTime
     * @return Formation
     */
    public function setZoomEndTime($zoomEndTime) {
        $this->zoomEndTime = $zoomEndTime;
    }

    /**
     * @return bool
     */
    public function isActive() {
        return !$this->archived;
    }

    /**
     * @return bool
     */
    public function isArchived()
    {
        return $this->archived;
    }

    /**
     * @param bool $archived
     */
    public function setArchived($archived): self
    {
        $this->archived = $archived;
        return $this;
    }

    /**
     * Add formationHistory
     *
     * @param FormationHistory $formationHistory
     * @return Formation
     */
    public function addFormationHistory(FormationHistory $formationHistory)
    {
        $this->formationHistories[] = $formationHistory;
        $formationHistory->setFormation($this);
        return $this;
    }

    /**
     * Remove formationHistory
     *
     * @param FormationHistory $formationHistory
     */
    public function removeFormationHistory(FormationHistory $formationHistory)
    {
        $this->formationHistories->removeElement($formationHistory);
    }

    /**
     * Get formationHistories
     *
     * @return Collection|FormationHistory[] s
     */
    public function getFormationHistories()
    {
        return $this->formationHistories;
    }

    public function addFormationHistoryRegister($type = FormationHistory::MANUAL_TYPE)
    {
        $this->addFormationHistory(FormationHistory::registerAction($this, $type));
    }

    public function addFormationHistoryUnregister($motif = null, $commentaire = null, $type = FormationHistory::MANUAL_TYPE)
    {
        $this->addFormationHistory(FormationHistory::unregisterAction($this, $motif, $commentaire, $type));
    }

    /**
     * @param string $formatPeriod
     */
    public function getFormatPeriod() {
        if (!$this->formatPeriod && $this->getProgramme()->isSurSite()) {
            return "Soirée";
        }
        return $this->formatPeriod;
    }

    /**
     * @param string $formatPeriod
     * @return Formation
     */
    public function setFormatPeriod($formatPeriod) {
        $this->formatPeriod = $formatPeriod;
        return $this;
    }

    public function getZone() {
        if ($this->getProgramme()->isSurSite()) {
            return $this->getFormatPeriod() === self::FORMAT_PERIOD_JOURNEE ? "journee" : "soiree";
        }
        return $this->getProgramme()->getPresence();
    }

    /**
     * @return boolean
     */
    public function isNational()
    {
        return $this->national;
    }

    /**
     * @param boolean $national
     * @return Formation
     */
    public function setNational($national)
    {
        $this->national = $national;
        return $this;
    }

    /**
     * @return string|null
     */
    public function getCategory() {
        if ($this->getParticipations() && $this->getParticipations()->first()) {
           return $this->getParticipations()->first()->getParticipant()->getCategory();
        } else {
            return null;
        }
    }

    public function isMedecin() {
        if ($this->getParticipations() && $this->getParticipations()->first()) {
            return $this->getParticipations()->first()->getParticipant()->isMedecin();
        }
        return false;
    }

    public function isPresentiel(): bool
    {
        return $this->programme->getDurationPresentielle() !== null && $this->programme->getDurationPresentielle() > 0;
    }

    /**
     * @return int
     */
    public function getParticipantCount() {
        if (!$this->getParticipations()) {
            return 0;
        }
        return $this->getParticipations()->count();
    }

    public function getTypeLabel() {

        if ($this->isCms()) {
            return "CMS";
        }

        if ($this->isCoordinatorLbi()) {
            return "Coordinateur LBI";
        }

        switch ($this->getDiscr()) {
            case self::TYPE_AUDIT:
                return "Audit";
                break;
            case self::TYPE_PRESENTIELLE:
                return "Présentielle";
                break;
            case self::TYPE_SEDD:
                return "SEDD";
                break;
            case self::TYPE_CONGRES:
                return "Congrés";
                break;
            case self::TYPE_ELEARNING:
                return "Elearning";
                break;
        }

        return null;
    }

    public function getGroup() {

        if ($this->isCms()) {
            return self::GROUP_CMS;
        }

        if ($this->isCoordinatorLbi()) {
            return self::GROUP_LBI;
        }

//        if ($this->isActalians()) {
//            return self::GROUP_ACTALIANS;
//        }

        switch ($this->getDiscr()) {
            case self::TYPE_AUDIT:
            case self::TYPE_PRESENTIELLE:
                return self::GROUP_EDUPRAT;
                break;
            case self::TYPE_SEDD:
                return self::GROUP_SEDD;
                break;
            case self::TYPE_CONGRES:
                return self::GROUP_CONGRES;
                break;
            case self::TYPE_ELEARNING:
            case self::TYPE_POWERPOINT:
                return self::GROUP_ELEARNING;
                break;
        }

        return null;
    }


    public function getCoordinatorname() {
        $coordinatorsName = array();
        if (!is_null($this->getCoordinators())) {
            foreach($this->getCoordinators() as $coordinator) {
                $coordinatorsName[] = $coordinator->getPerson()->getInvertedFullname();
            }
            return $coordinatorsName;
        }
        return null;
    }


    public function getFormateursNames() {
        $formateursNames = array();
        if (!is_null($this->getFormateurs())) {
            foreach($this->getFormateurs() as $formateur) {
                $formateursNames[] = $formateur->getPerson()->getInvertedFullname();
            }
            return $formateursNames;
        }
        return null;
    }

    public function getSupervisorName() {
        $supervisorsName = array();
        if (!is_null($this->getCoordinators())) {
            foreach($this->getCoordinators() as $coordinator) {
                if ($coordinator->getPerson()->getSupervisor()) {
                    $supervisorsName[] = $coordinator->getPerson()->getSupervisor()->getInvertedFullname();
                }
            }
            return $supervisorsName;
        }
        return null;
    }

    public function getLowercaseTitle() {
        return ucfirst(strtolower($this->getProgramme()->getLowercaseTitle()));
    }

    public function getStatus() {
        if ($this->closed) {
            return self::STATUS_CLOSED;
        } else if ($this->closedAndpc) {
            return self::STATUS_CLOSED_ANDPC;
        } else {
            $now = new \DateTime();
            $start = clone $this->getStartDate();
            $start->setTime(0, 0, 0);
            if ($start <= $now) {
                return self::STATUS_OPENED;
            } else if ($this->getStartDate() > $now) {
                return self::STATUS_FUTURE;
            }
        }
        return null;
    }

    public function getStatusApi() {
        if ($this->closed || $this->closedAndpc) {
            return self::STATUS_CLOSED;
        } else {
            $now = new \DateTime();
            $start = clone $this->getStartDate();
            $start->setTime(0, 0, 0);
            if ($start <= $now) {
                return self::STATUS_OPENED;
            } else if ($this->getStartDate() > $now) {
                return self::STATUS_FUTURE;
            }
        }
        return null;
    }

    /**
     * @return bool
     */
    public function getAccessibilityStatus() {
        if ($this->getClosed()) {
            return self::STATUS_CLOSED;
        }
        $now = new \DateTime();
        $start = clone ($this->getFormOpeningDate() ? $this->getFormOpeningDate() : $this->getOpeningDate());
        $end = clone ($this->getFormClosingDate() ? $this->getFormClosingDate() : $this->getClosingDate());
        $start->setTime(0, 0, 0);
        $end->setTime(0, 0, 0);
        if ($now >= $start) {
            return self::STATUS_OPENED;
        } else if ($now < $start) {
            return self::STATUS_FUTURE;
        }
        return null;
    }

    /**
     * @return bool
     */
    public function isDpc() {
        foreach ($this->getFinanceModes() as $financeMode) {
            if ($financeMode->isDpc()) {
                return true;
            }
        }
        return false;
    }

    public function getStatusLabel() {
        switch ($this->getStatus()) {
            case self::STATUS_CLOSED:
                return "Cloturée";
            break;
            case self::STATUS_CLOSED_ANDPC:
                return "Cloturée ANDPC";
            break;
            case self::STATUS_FUTURE:
                return "Prévues";
            break;
            case self::STATUS_OPENED:
                return "En cours";
            break;
        }
        return null;
    }

    public function getParticipants() {
        if (!$this->getParticipations()) {
            return [];
        }
        return $this->getParticipations()->map(function(Participation $p) { return $p->getParticipant();});
    }

    public function getParticipantsPerMode(FinanceSousMode $financeSousMode) {
        if (!$this->getParticipationsPerMode($financeSousMode)) {
            return [];
        }
        return $this->getParticipationsPerMode($financeSousMode)->map(function(Participation $p) { return $p->getParticipant();});
    }

    public function getSubject() {
        return null;
    }

    public function startDateMonth() {
        $fmt = \IntlDateFormatter::create('fr_FR', NULL, NULL, NULL, NULL, 'MMMM');
        return ucfirst($fmt->format($this->startDate->getTimestamp()));
    }

    public function startDateYear() {

        return $this->startDate->format('Y');
    }

    public function closingDateMonth(): string {
        $fmt = \IntlDateFormatter::create('fr_FR', NULL, NULL, NULL, NULL, 'MMMM');
        return ucfirst($fmt->format($this->closingDate->getTimestamp()));
    }
    public function closingDateYear(): string {
        return $this->closingDate->format('Y');
    }

    /**
     * @return string|null
     */
    public function getLocalization() {
        if ($this->getLatitude() && $this->getLongitude()) {
            return sprintf("%s,%s", $this->latitude, $this->longitude);
        }
        return null;
    }

    public function updateRegionName()
    {
        if ($this->zipCode) {
            $this->region = AdressesService::getRegionName($this->zipCode);
            $this->departement = AdressesService::getDepartementName($this->zipCode);
        }
    }

    /**
     * @return Collection|Person[]
     */
    public function getFormateursPersons()
    {
        return $this->getFormateurs()->map(function($f) {
            /** @var Formateur $f */
            return $f->getPerson();
        });
    }

    /**
     * @return Collection|Person[]
     */
    public function getCoordinatorsPerson($ignoreArchived = false)
    {
        return $this->getCoordinators()->map(function($c) {
            /** @var Coordinator $c */
            return $c->getPerson();
            /** @var Person $p */
        })->filter(function($p) use ($ignoreArchived) {
            return !$ignoreArchived || !$p->isArchived();
        });
    }

    public function getCoordinatorByPerson(Person $user)
    {
        foreach ($this->getCoordinators() as $coordinator) {
            if ($coordinator->getPerson() === $user) {
                return $coordinator;
            }
        }
        return null;
    }

    /**
     * Somme des honoraires des formateurs pour chaque coordinateur
     * @return int|number
     */
    public function getFormerHonorary() {
        if (!$this->getFormateurs()) {
            return null;
        }
        $values = $this->getFormateurs()->map(function (Formateur $f) {
            return $f->getHonorary() / count($this->getCoordinators());
        })->toArray();

        if (count(array_unique($values)) === 1 && is_null($values[0])) {
            return null;
        }

        return array_sum($values);
    }

    /**
     * Somme des honoraires des formateurs pour le programme
     * @return int|number
     */
    public function getFormersHonorary() {
        if (!$this->getCoordinators()) {
            return $this->getOriginalFormersHonorary();
        }

        $values = $this->getCoordinators()->map(function(Coordinator $c) {
            return $c->getSurchargedCostFormers();
        })->toArray();

        if (count(array_unique($values)) === 1 && is_null($values[0])) {
            return null;
        }

        return array_sum($values);
    }

    public function getOriginalFormersHonorary() {
        if (!$this->getFormateurs()) {
            return null;
        }
        $values = $this->getFormateurs()->map(function (Formateur $f) {
            return $f->getHonorary();
        })->toArray();

        if (count(array_unique($values)) === 1 && is_null($values[0])) {
            return null;
        }

        return array_sum($values);
    }

    /**
     * @return bool
     */
    public function isCoordinatorLbi()
    {
        if ($this->getCoordinators()) {
            foreach($this->getCoordinators() as $coordinator) {
                if($coordinator->getPerson()->isCoordinatorLbi()) {
                    return true;
                }
            }
        }
        return false;
    }

    public function isOneDayLong()
    {
        return $this->getStartDate()->format('Y-m-d') === $this->getEndDate()->format('Y-m-d');
    }

    public function isLocked() {
        return $this->accounted;
    }

    public function isLockedMidCourse() {
        return $this->accountedMidCourse;
    }

    /**
     * Somme des honoraires des coordinateurs
     * @return int|number
     */
    public function getCoordinatorHonorary() {
        if (!$this->getCoordinators()) {
            return null;
        }
        $values = $this->getCoordinators()->map(function(Coordinator $c) {
            return $c->getHonorary();
        })->toArray();

        if (count(array_unique($values)) === 1 && is_null($values[0])) {
            return null;
        }

        return array_sum($values);
    }

    /**
     * Somme des honoraires des coordinateurs
     * @return int|number
     */
    public function getCoordinatorCalculatedHonorary() {
        if (!$this->getCoordinators()) {
            return null;
        }
        $values = $this->getCoordinators()->map(function(Coordinator $c) {
            return $c->getCalculatedHonorary();
        })->toArray();

        if (count(array_unique($values)) === 1 && is_null($values[0])) {
            return null;
        }

        return array_sum($values);
    }

    /**
     * Somme des commission théoriques des coordinateurs
     * @return int|number
     */
    public function getCoordinatorCommissionTheorique() {
        if (!$this->getCoordinators()) {
            return null;
        }
        $values = $this->getCoordinators()->map(function(Coordinator $c) {
            return $c->getCommissionTheorique();
        })->toArray();

        if (count(array_unique($values)) === 1 && is_null($values[0])) {
            return null;
        }

        return array_sum($values);
    }

    // /**
    //  * Somme des honoraires des coordinateurs
    //  * @return int|number
    //  */
    // public function getCoordinatorHonoraryByCoordinator(Person $coordinator) {
    //     if (!$this->getCoordinators()) {
    //         return null;
    //     }
    //     return array_sum($this->getCoordinators()->map(function(Coordinator $c) use ($coordinator) {
    //         if ($c->getPerson() == $coordinator) {
    //             if (!$c->getHonorary()) {
    //                 if (count($this->getCoordinators()) > 1) {
    //                     foreach($this->getFormations() as $formation) {
    //                         foreach ($formation as $participations) {
    //                             if($participations->getCoordinator() && $participation->getCoordinator()->getId() != $c->getId()) {
    //                                 return $participation->getBudgetCR();
    //                             }
    //                         }
    //                     }
    //                 } else {
    //                     foreach($this->getFormations() as $formation) {
    //                         foreach ($formation as $participations) {
    //                             return $participation->getBudgetCR();
    //                         }
    //                     }
    //                 }
    //             } else {
    //                 return $c->getHonorary();
    //             }
    //         }

    //     })->toArray());
    // }

    /**
     * Somme des honoraires de restauration
     * @return int|number
     */
    public function getRestaurationHonorary() {
        if (!$this->getCoordinators()) {
            return null;
        }
        $values = $this->getCoordinators()->map(function (Coordinator $c) {
            return $c->getRestaurationHonorary();
        })->toArray();

        if (count(array_unique($values)) === 1 && is_null($values[0])) {
            return null;
        }

        return array_sum($values);
    }

    /**
     * Somme des avances formations
     * @return int|number
     */
    public function getAvancesCost() {
        if (!$this->getCoordinators()) {
            return null;
        }
        $values = $this->getCoordinators()->map(function (Coordinator $c) {
            return $c->getAvancesCost();
        })->toArray();

        if (count(array_unique($values)) === 1 && is_null($values[0])) {
            return null;
        }

        return array_sum($values);
    }

    /**
     * Somme des honoraires de restauration
     * @return int|number
     */
    public function getRestaurationHonoraryByCoordinator(Person $coordinator) {
        if (!$this->getCoordinators()) {
            return null;
        }
        $values = $this->getCoordinators()->map(function (Coordinator $c) use ($coordinator) {
            if ($c->getPerson() == $coordinator) {
                return $c->getRestaurationHonorary();
            }
        })->toArray();
        if (count(array_unique($values)) === 1 && is_null($values[0])) {
            return null;
        }
        return array_sum($values);
    }

    // /**
    //  * Cout de la formation = cout coordinateur + couts orateurs + cout restaurant
    //  * @return float
    //  */
    // public function getCost() {
    //     return $this->getCoordinatorHonorary() + $this->getFormersHonorary() + $this->getRestaurationHonorary();
    // }

    public function getFormationCost() {
        return $this->getFormersHonorary() + $this->getPhysicalFormationCost();
    }

    public function getPhysicalFormationCost() {

        if (!$this->getCoordinators()) {
            return $this->getCostBadges() + $this->getCostKilometres() + $this->getCostMateriel() + $this->getCostRetrocessions() + $this->getCostDivers();
        }
        $values = $this->getCoordinators()->map(function (Coordinator $c) {
            return $c->getPhysicalFormationCost();
        })->toArray();

        if (count(array_unique($values)) === 1 && is_null($values[0])) {
            return null;
        }

        return array_sum($values);
    }

    public function getElasticIndex(): string
    {
        return self::ELASTIC_INDEX;
    }

    #[Assert\Callback]
    public function checkCoordinator(ExecutionContextInterface $context)
    {
        if(count($this->getCoordinators()) < 1) {
            $context->buildViolation('Veuillez ajouter un coordinateur')
                ->atPath('coordinators')
                ->addViolation();
        }

        $unityViolation = false;
        foreach($this->getUnities() as $unityIndex => $unity) {
            if ($this->getProgramme()->getUnityByPosition($unityIndex + 1)->isOnSite() || $this->getProgramme()->getUnityByPosition($unityIndex + 1)->isVirtuelle()) {
                foreach ($unity->getUnitySessionDates() as $unityDate) {
                    if ($unityDate->getStartDate() == null || $unityDate->getEndDate() == null) {
                        $unityViolation = true;
                    }
                }
            } else {
                if ($unity->getOpeningDate() == null || $unity->getClosingDate() == null) {
                    $unityViolation = true;
                }
            }
        }

        if($unityViolation) {
            $context->buildViolation('Veuillez compléter tout les champs relatifs aux unités ci-dessous')
                ->atPath('unities')
                ->addViolation();
        }

        $coordinators = $this->getCoordinators() instanceof Collection ? $this->getCoordinators()->toArray() : $this->getCoordinators();

        if (count($this->getCoordinators()) > 0) {
            $coordinatorIds = array_map(function(Coordinator $coordinator) {
                return $coordinator->getPerson()->getId();
            }, $coordinators);

            if (count($coordinatorIds) !== count(array_unique($coordinatorIds))) {
                $context->buildViolation('Veuillez supprimer les coordinateurs qui apparaissent en double')
                    ->atPath('coordinators')
                    ->addViolation();
            }
        }

        if(count($this->getFormateurs()) > 0) {
            $formers = $this->getFormateurs() instanceof Collection ? $this->getFormateurs()->toArray() : $this->getFormateurs();

            foreach ($formers as $formateur) {
                if (!$formateur->getPerson() instanceof Person) {
                    $context->buildViolation('Veuillez sélectionner un formateur')
                        ->atPath('formateurs')
                        ->addViolation();;
                }
            }

            $formers = array_filter($formers, function (Formateur $formateur) {
                return $formateur->getPerson() instanceof Person;
            });

            $formerIds = array_map(function (Formateur $formateur) {
                return $formateur->getPerson()->getId();
            }, $formers);

            if (count($formerIds) !== count(array_unique($formerIds))) {
                $context->buildViolation('Veuillez supprimer les formateurs qui apparaissent en double')
                    ->atPath('formateurs')
                    ->addViolation();
            }
        }

    }

    /**
     * Dossiers complets = toutes les factures sont insérées dans l’extranet (CR et formateur)
     * Dossiers incomplets = factures manquantes
     * A noter :
     * Si le CR est salarié, il ne met jamais de facture coordinateur. Dans ce cas, ne pas compter comme une facture manquante.
     * Idem pour le formateur salarié.
     * Si coordinateur sans participant, ne pas compter.
     * On ne compte pas les factures formateurs manquantes sur les classes virtuelles nationales
     * @param bool $list
     * @return bool|array
     */
    public function processFileCompleted($list = false) {
        $missingFiles = array();

        if (!$this->isClasseVirtuelleNationale()) {
            /** @var Formateur $formateur */
            foreach ($this->getFormateurs() as $formateur) {
                if ($formateur->getPerson()->isLiberal()) {
                    $criteria = Criteria::create()->andWhere(Criteria::expr()->eq('formateur', $formateur));
                    /** @var FormateurFiles $formerFiles */
                    if ($formerFiles = $this->getFormateurFiles()->matching($criteria)->first()) {
                        if (is_null($formerFiles->getFacture()) && $formateur->getHonorary() > 0) {
                            if (!$list) {
                                return false;
                            }
                            $missingFiles[] = array(
                                "type" => "facture_former",
                                "person" => $formateur->getPerson()
                            );
                        }
                    } else {
                        if (!$list) {
                            return false;
                        }
                        if($formateur->getHonorary() > 0) {
                            $missingFiles[] = array(
                                "type" => "facture_former",
                                "person" => $formateur->getPerson()
                            );
                        }
                    }
                }
            }
        }


        $startDate = clone($this->getStartDate());
        $startDate->setTime(0, 0, 0);

        /** @var Coordinator $coordinator */
        foreach ($this->getCoordinators() as $coordinator) {
            if ($coordinator->getParticipantCount() > 0
                && $coordinator->getCaTotal() > 0
                && ($coordinator->getPerson()->getCrStatus() === Person::CR_STATUS_INDEPENDANT || is_null($coordinator->getPerson()->getCrStatus())
                || ($coordinator->getPerson()->getCrStatus() === Person::CR_STATUS_SALARIE && $startDate < $coordinator->getPerson()->getCrStatusDate()))
                   ) {
                $criteria = Criteria::create()->andWhere(Criteria::expr()->eq('coordinator', $coordinator));
                /** @var CoordinatorFiles $coordinatorFiles */
                if ($coordinatorFiles = $this->getCoordinatorFiles()->matching($criteria)->first()) {
                    if (is_null($coordinatorFiles->getFactureCoordinator())) {
                        if (!$list) {
                            return false;
                        }
                        $missingFiles[] = array(
                            "type" => "facture_cr",
                            "person" => $coordinator->getPerson()
                        );
                    }
                } else {
                    if (!$list) {
                        return false;
                    }
                    $missingFiles[] = array(
                        "type" => "facture_cr",
                        "person" => $coordinator->getPerson()
                    );
                }
            }
        }

        return $list ? $missingFiles : true;
    }

    /**
     * @return $this
     */
    public function updateFileCompleted() {
        $this->fileCompleted = $this->processFileCompleted();
        return $this;
    }

    /**
     * @param Coordinator|null $coordinator
     * @param bool $includeTopo
     * @return array|bool
     */
    public function getMissingFiles(Coordinator $coordinator = null, $includeTopo = true, Person $webmaster = null) {
        $files = $this->processFileCompleted(true);

        if ($coordinator) {

            if ($coordinator->getParticipantCount() === 0) {
                return array();
            }

            $files = array_filter($files, function($file) use ($coordinator) {
               return $file["type"] !== "facture_cr" || ($file["type"] === "facture_cr" && $coordinator->getPerson() == $file["person"]);
            });
        }

        if ($webmaster) {
            $files = array_filter($files, function($file) use ($webmaster) {
                return $file["type"] !== "facture_cr" || ($file["type"] === "facture_cr" && $file["person"]->getWebmaster() == $webmaster);
            });
        }

        if ($includeTopo && !$this->isElearningDiscr() && !$this->isClasseVirtuelleNationale() && ($this->getTopoFiles()->count() === 0 && $this->getProgramme()->getTopoProgrammeFiles()->count() === 0)) {
            $files[] = array(
                "type" => "topo"
            );
        }

        return $files;
    }

    public function attestationMandatory(): bool
    {
        foreach($this->getProgramme()->getUnities() as $unity) {
            if ($unity->getNbHoursOffline()) {
                return true;
            }
        }
        return false;
    }

    public function getAttestationManquantes(){
        $participations = array();
        foreach($this->getParticipations() as $participation) {
            if(!$participation->getAttestationHonneur()) {
                array_push($participations, $participation);
            }
        }
        return $participations;
    }

    public function hasOneAttestationHonneurUploaded() {
        if ($this->participations->count() === 0) {
            return false;
        }
        foreach ($this->participations as $participation) {
            if ($participation->getAttestationHonneur()) {
                return true;
            }
        }
        return false;
    }

    /*** GETTERS A SUPPRIMER ***/

     /**
      * @return bool
      */
      public function formAlreadyAnswered() {
        foreach($this->getParticipations() as $participation) {
            if($participation->getCompletedForm1() && !$participation->isArchived()) {
                return true;
            }
        }
        return false;
    }


    /**
      * @return bool
      */
      public function getQuizsCompletedsEmailSended() {
        return $this->quizsCompletedsEmailSended;
    }

    /**
     * @param bool $quizsCompletedsEmailSended
     * @return Formation
     */
    public function setQuizsCompletedsEmailSended($quizsCompletedsEmailSended)
    {
        $this->quizsCompletedsEmailSended = $quizsCompletedsEmailSended;
        return $this;
    }

    public function addFormateurFile(FormateurFiles $formateurFile): self
    {
        if (!$this->formateurFiles->contains($formateurFile)) {
            $this->formateurFiles[] = $formateurFile;
            $formateurFile->setFormation($this);
        }

        return $this;
    }

    public function addCoordinatorFile(CoordinatorFiles $coordinatorFile): self
    {
        if (!$this->coordinatorFiles->contains($coordinatorFile)) {
            $this->coordinatorFiles[] = $coordinatorFile;
            $coordinatorFile->setFormation($this);
        }

        return $this;
    }

    public function addTopoFile(TopoFiles $topoFile): self
    {
        if (!$this->topoFiles->contains($topoFile)) {
            $this->topoFiles[] = $topoFile;
            $topoFile->setFormation($this);
        }

        return $this;
    }

    public function containPresentiel() {
        return $this->getProgramme()->getDurationPresentielle() != null;
    }

    /**
     * @return array
     */
    public function getTopoProgammeDeleted()
    {
        return $this->topoProgrammeDeleted ?? array();
    }

    /**
     * @return array
     */
    public function deletedTopoProgramme($topo)
    {
        $this->topoProgrammeDeleted = $this->topoProgrammeDeleted == null ? array() : $this->topoProgrammeDeleted;
        array_push($this->topoProgrammeDeleted, $topo->getId());
        return $this;
    }

    /**
     * @return array
     */
    public function topoProgammeIsDeleted($topoId)
    {
       return $this->getTopoProgammeDeleted() ? in_array($topoId, $this->getTopoProgammeDeleted()) : false;
    }

    /**
     * Get topoProgrammeFiles
     *
     * @return TopoProgrammeFiles[]|Collection
     */
    public function getTopoProgrammeFiles()
    {
        $defaultTopos = [];
        foreach ($this->getProgramme()->getTopoProgrammeFiles() as $topo) {
            if (!$this->topoProgammeIsDeleted($topo->getId())) {
                array_push($defaultTopos, $topo);
            }
        }

        return $defaultTopos == [] ? false : $defaultTopos;
    }

    public function isMinTime ()
    {
        return (($this->isVignetteFirst() || $this->isAudit() || $this->isTcs()) && $this->getFormType() != "predefined") || ($this->isElearning() && $this->getFormType() == "audit");
    }

    public function isClotured (): bool {
        return $this->getClosingDate() < new Carbon('today midnight');
    }

    public function isFormPostAccessible() {
        return $this->getFormClosingDate() >= new \DateTime('today midnight');
    }

    // Retourne si la formation rentre dans les conditions d'accès au formulaire post 28jours après la date de réunion
    public function isFormPost28d ()
    {
        return (($this->isVignetteFirst() || $this->isFormAudit()) && ($this->getVariety() == "Mixte" || $this->getVariety() == "Présentiel")) ||
               (($this->isVignetteFirst() || $this->isFormAudit()) && $this->isElearning());
    }

    public function isThirdUnityOpen () {
        if ($this->getProgramme()->getYear() < 2023) {
            return true;
        }
        if ($this->isThreeUnity()) {
            return new \DateTime() >= $this->getUnityByPosition(3)->getOpeningDate()->setTime('0','0','0');
        }
        return true;
    }

    public function thirdUnityOpeningDate () {
        return $this->getUnityByPosition(3)->getOpeningDate();
    }

    public function has1Unities() : bool
    {
        return $this->unities->count() === 1;
    }

    public function has2Unities() : bool
    {
        return $this->unities->count() === 2;
    }

    public function has3Unities() : bool
    {
        return $this->unities->count() === 3;
    }

    /**
     * Add unity
     *
     * @param UnitySession $unity
     * @return Formation
     */
    public function addUnity(UnitySession $unity)
    {
        $this->unities[] = $unity;
        $unity->setFormation($this);

        return $this;
    }

    /**
     * Remove unity
     *
     * @param UnitySession $unity
     */
    public function removeUnity(UnitySession $unity)
    {
        $this->unities->removeElement($unity);
    }

    /**
     * Get unities
     *
     * @return Collection
     */
    public function getUnities()
    {
        return $this->unities;
    }

    /**
     * @param $position
     * @return false|UnitySession
     */
    public function getUnityByPosition ($position) {
        return $position && isset($this->getUnities()->toArray()[$position - 1]) ? $this->getUnities()->toArray()[$position - 1] : false;
    }

    public function getLastUnity() : ?UnitySession
    {
        if (count($this->getUnities())) {
            return $this->getUnityByPosition(count($this->getUnities()));
        }
        return null;
    }

    public function getFirstUnityDateOfYear($year) {
        for ($i = 1; $i <= count($this->getUnities()); $i++) {
            $unity = $this->getUnityByPosition($i);
            if (count($unity->getUnitySessionDates())) {
                $firstUnitySessionDate = $unity->getUnitySessionDates()->first();
                if ($firstUnitySessionDate->getStartDate()->format('Y') == $year) {
                    return $firstUnitySessionDate->getStartDate();
                }
            } else {
                if ($unity->getOpeningDate()->format('Y') == $year) {
                    return $unity->getOpeningDate();
                }
            }
        }
        return null;
    }

    public function getLastUnityDateOfYear($year) {
        $date = null;
        for ($i = 1; $i <= count($this->getUnities()); $i++) {
            $unity = $this->getUnityByPosition($i);
            if (count($unity->getUnitySessionDates())) {
                $firstUnitySessionDate = $unity->getUnitySessionDates()->last();
                if ($firstUnitySessionDate->getEndDate()->format('Y') == $year) {
                    $date = $firstUnitySessionDate->getEndDate();
                }
            } else {
                if ($unity->getClosingDate()->format('Y') == $year) {
                    $date = $unity->getClosingDate();
                }
            }
        }
        return $date;
    }

    // Récupère date ouverture dernière unitée de N (Utilisée pour les attestations N manquantes)
    // Correspond généralement à la date de réunion pour les pluriannuelles
    public function getLastOpeningDateOfYear($year) {
        $date = null;
        for ($i = 1; $i <= count($this->getUnities()); $i++) {
            $unity = $this->getUnityByPosition($i);
            if (count($unity->getUnitySessionDates())) {
                $firstUnitySessionDate = $unity->getUnitySessionDates()->first();
                if ($firstUnitySessionDate->getStartDate()->format('Y') == $year) {
                    $date = $firstUnitySessionDate->getStartDate();
                }
            } else {
                if ($unity->getOpeningDate()->format('Y') == $year) {
                    $date = $unity->getOpeningDate();
                }
            }
        }
        return $date;
    }

    /**
     * Add moduleTimes
     *
     * @param ModuleTimes $moduleTime
     * @return Formation
     */
    public function addModuleTime(ModuleTimes $moduleTime)
    {
        $this->moduleTimes[] = $moduleTime;
        $moduleTime->setFormation($this);

        return $this;
    }

    /**
     * Remove moduleTimes
     *
     * @param ModuleTimes $moduleTime
     */
    public function removeModuleTime(ModuleTimes $moduleTime)
    {
        $this->moduleTimes->removeElement($moduleTime);
    }

    /**
     * Get moduleTimes
     *
     * @return Collection
     */
    public function getModuleTimes()
    {
        return $this->moduleTimes;
    }

    /**
     * Add moduleTimes
     *
     * @param ModuleMinTimes $moduleMinTime
     * @return Formation
     */
    public function addModuleMinTime(ModuleMinTimes $moduleMinTime)
    {
        $this->moduleMinTimes[] = $moduleMinTime;
        $moduleMinTime->setFormation($this);

        return $this;
    }

    /**
     * Remove moduleMinTimes
     *
     * @param ModuleMinTimes $moduleMinTime
     */
    public function removeModuleMinTime(ModuleMinTimes $moduleMinTime)
    {
        $this->moduleMinTimes->removeElement($moduleMinTime);
    }

    /**
     * Get moduleMinTimes
     *
     * @return Collection
     */
    public function getModuleMinTimes()
    {
        return $this->moduleMinTimes;
    }

    /**
     * Set factureState
     *
     * @param FactureState $factureState
     * @return Formation
     */
    public function setFactureState(FactureState $factureState = null)
    {
        $this->factureState = $factureState;

        return $this;
    }

    /**
     * Get factureState
     *
     * @return FactureState
     */
    public function getFactureState()
    {
        return $this->factureState;
    }

    /**
     * Get the value of applyTimesToAll
     *
     * @return  bool
     */
    public function getApplyTimesToAll()
    {
        return $this->applyTimesToAll;
    }

    /**
     * Set the value of applyTimesToAll
     *
     * @param  bool  $applyTimesToAll
     *
     * @return  self
     */
    public function setApplyTimesToAll(bool $applyTimesToAll)
    {
        $this->applyTimesToAll = $applyTimesToAll;

        return $this;
    }

    // Mise à jour des anciens champs à partir des unités
    // Date d'ouverture prend date d'ouverture de l'unité 1 ou date de debut de première réunion si uniquement une unité sur site
    // Date de fermeture prend date de fermeture de l'unité 3 ou date de fin de derniere reunion si uniquement une unité sur site
    // Date de début de réunion prend première date de début reunion de l'unité sur site
    // Date de fin de réunion prend dernière date fin de réunion de l'unité sur site
    public function updateDates($create = false, ?PersistentCollection $cloneFormationUnities = null) {
        if (count($this->getUnities())) {
            foreach ($this->getUnities() as $unitySession) {
                $unitySession->synchroDate();
            }
            $this->overrideDateUnities($cloneFormationUnities);
            $firstUnity = $this->getUnityByPosition(1);
            $lastUnity = $this->getLastUnity();
            $reunionUnity = $this->getUnityByPosition($this->getProgramme()->getReunionUnityPosition());
            if ($reunionUnity) {
                $reunionUnityFirstDate = $create ? $reunionUnity->getUnitySessionDates()[0] : $reunionUnity->getUnitySessionDates()->first();
                $reunionUnityLastDate = $create ? $reunionUnity->getUnitySessionDates()[count($reunionUnity->getUnitySessionDates()) - 1] : $reunionUnity->getUnitySessionDates()->last();
                $this->setStartDate($reunionUnityFirstDate->getStartDate() ? $reunionUnityFirstDate->getStartDate() : $this->getStartDate());
                $this->setEndDate($reunionUnityLastDate->getEndDate() ? $reunionUnityLastDate->getEndDate() : $this->getEndDate());
            } elseif ($firstUnity != $lastUnity) {
                // pas de $reunionUnity si Elearning car pas de présentiel, donc unité 2 est à prendre en compte pour début fin reunion
                $this->setStartDate($this->getUnityByPosition(2)->getOpeningDate());
                $this->setEndDate($this->getUnityByPosition(2)->getClosingDate());
            }

            if ($this->has1Unities()) { // cas des 1 unités
                $unityFormation = $this->getProgramme()->getUnityByPosition(1);
                if ($unityFormation->isElearning() && $this->has1Unities()) {
                    $this->setOpeningDate($firstUnity->getOpeningDate() ?: $this->getOpeningDate());
                    $this->setClosingDate($lastUnity->getClosingDate() ?: $this->getClosingDate());
                    $this->setStartDate($firstUnity->getOpeningDate() ?: $this->getOpeningDate());
                    $this->setEndDate($lastUnity->getClosingDate() ?: $this->getClosingDate());
                } else {
                    $this->setOpeningDate($reunionUnityFirstDate->getStartDate() ?: $this->getStartDate());
                    $this->setClosingDate($reunionUnityLastDate->getEndDate() ?: $this->getEndDate());
                }
            } else {
                if ($reunionUnity === $lastUnity) {
                    $this->setOpeningDate($firstUnity->getOpeningDate() ? $firstUnity->getOpeningDate() : $this->getOpeningDate());
                    $this->setClosingDate($reunionUnityLastDate->getEndDate() ? $reunionUnityLastDate->getEndDate() : $this->getClosingDate());
                } else {
                    $this->setOpeningDate($firstUnity->getOpeningDate() ? $firstUnity->getOpeningDate() : $this->getOpeningDate());
                    $this->setClosingDate($lastUnity->getClosingDate() ?: $this->getClosingDate());
                }
            }

            $this->updateSearchDates();
            $this->updateAttestationMissingDates();
        }
    }

    public function updateSearchDates(): void
    {
        $isELpresenceEL = $this->isElearning() && $this->getProgramme() && $this->getProgramme()->isElearning();
        $this->setSearchStartDate(
            $isELpresenceEL ? $this->getOpeningDate() : $this->getStartDate()
        );
        $this->setSearchEndDate(
            $isELpresenceEL ? $this->getClosingDate() : $this->getEndDate()
        );

        $this->setQuerySearchStartDate(
            $isELpresenceEL ? $this->getOpeningDate() : $this->getStartDate()
        );
        $this->setQuerySearchEndDate(
            $isELpresenceEL ? $this->getOpeningDate() : $this->getEndDate()
        );
    }

    public function overrideDateUnities(?PersistentCollection $cloneFormationUnities): void
    {
        if ($this->isVfc() || $this->isTcs()) {
            /** @var UnitySession $unity3 */
            $unity3 = $cloneFormationUnities ? $cloneFormationUnities->get(2) : new UnitySession();
            $unity2 = $this->unities->get(1);
            $unity3->setFormation($this);
            $unity3->setOpeningDate($unity2->getOpeningDate());
            $unity3->setClosingDate($unity2->getClosingDate());
            foreach ($unity2->getUnitySessionDates() as $key => $unitySessionDate) {
                /** @var UnitySessionDate $usd */
                $usd = $unity3->getUnitySessionDates()->get($key);
                if (!$usd) {
                    $usd = new UnitySessionDate();
                }
                $usd->setStartDate($unitySessionDate->getStartDate());
                $usd->setEndDate($unitySessionDate->getEndDate());
                $usd->setUnitySession($unity3);
                $unity3->getUnitySessionDates()->set($key, $usd);
            }
            $this->getUnities()->set(2, $unity3);
        }
    }

    public function updateAttestationMissingDates(): void
    {
        $modifyN =  $this->getProgramme()->isFormatElearning() ? "+15 Days" : "+1 Day";
        if ($this->isPluriAnnuelle() && $this->shouldDisplayAttestationN1()) {
            $referenceDateMissingForN = clone($this->getLastOpeningDateOfYear($this->getOpeningDate()->format("Y"))); // Dernière date d'ouverture année N
            $referenceDateMissingForN1 = clone($this->getFirstUnityDateOfYear($this->getClosingDate()->format("Y"))); // Première date année N+1
            $this->setAttestationMissingDate($referenceDateMissingForN->modify($modifyN));
            $this->setAttestationMissingDateN1($referenceDateMissingForN1->modify('+1 Day')); // +1 jour systématique en N1
        } else {
            $referenceDateMissing = clone($this->getStartDate()); // Date de réu
            $this->setAttestationMissingDate($referenceDateMissing->modify($modifyN));
            $this->setAttestationMissingDateN1(null);
        }
    }

    public function cloneModuleTimes(Formation $formation) {
        $this->removeModuleTime($this->getModuleTimes()->toArray()[0]);
        $this->addModuleTime(clone $formation->getModuleTimes()->toArray()[0]);
    }

    public function cloneModuleMinTimes(Formation $formation) {
        $this->removeModuleMinTime($this->getModuleMinTimes()->toArray()[0]);
        $this->addModuleTime(clone $formation->getModuleMinTimes()->toArray()[0]);
    }

    public function getModuleTimesToArray() {
        $moduleTime = $this->getModuleTimes()->first();
        return $this->timeToArray($moduleTime);
    }

    public function getModuleMinTimesToArray() {
        $moduleMinTime = $this->getModuleMinTimes()->first();
        return $this->timeToArray($moduleMinTime);
    }

    public function timeToArray($module) {
        return [
            "video_presession" => $module->getVideoPresession(),
            "documents_pedagogiques" => $module->getDocumentsPedagogiques(),
            "form_presession" => $module->getFormPresession(),
            "prerestitution" => $module ->getPrerestitution(),
            "etutorat_1" => $module->getEtutorat1(),
            "fiche_action_1" => $module->getFicheAction1(),
            "video_postsession" => $module->getVideoPostsession(),
            "topos" => $module->getTopos(),
            "form_postsession" => $module->getFormPostsession(),
            "tool_box" => $module->getToolBox(),
            "etutorat_2" => $module->getEtutorat2(),
            "restitution" => $module->getRestitution(),
            "fiche_action_2" => $module->getFicheAction2(),
            "synthese" => $module->getSynthese(),
            "progression" => $module->getProgression(),
            "etutorat_3" => $module->getEtutorat3(),
            "end" => $module->getEnd(),
            "form_evaluation" => $module->getFormEvaluation()
        ];
    }

    public function getModuleTimesByUnityToArray() {
        $moduleTime = $this->getModuleTimes()->first();
        return [
            1 => [
                "video_presession" => $moduleTime->getVideoPresession(),
                "form_presession" => $moduleTime->getFormPresession(),
                "prerestitution" => $moduleTime ->getPrerestitution(),
                "etutorat_1" => $moduleTime->getEtutorat1(),
            ],
            2 => [
                "fiche_action_1" => $moduleTime->getFicheAction1(),
            ],
            3 => [
                "video_postsession" => $moduleTime->getVideoPostsession(),
                "form_postsession" => $moduleTime->getFormPostsession(),
                "tool_box" => $moduleTime->getToolBox(),
                "etutorat_2" => $moduleTime->getEtutorat2(),
                "restitution" => $moduleTime->getRestitution(),
                "fiche_action_2" => $moduleTime->getFicheAction2(),
                "synthese" => $moduleTime->getSynthese(),
                "progression" => $moduleTime->getProgression(),
                "etutorat_3" => $moduleTime->getEtutorat3(),
                "end" => $moduleTime->getEnd(),
                "form_evaluation" => $moduleTime->getFormEvaluation()
            ]
        ];
    }

    public function toArrayPlaquette($exclusions = array()) {
        return array(
            "id" => $this->getId(),
            "reference" => $this->getProgramme()->getReference(),
            "sessionNumber" => $this->getSessionNumber(),
            "nbParticipants" => $this->getParticipantCount(),
            "presence" => $this->getProgramme()->getPresence(),
            "title" => $this->getProgramme()->getTitle(),
            "startDate" => $this->getStartDate()->format("d/m/Y"),
            "isExcluded" => in_array($this->getId(), $exclusions),
            'token' => $this->getToken()
        );
    }

    public function sameDatesToString() {
        $daysConvert = ["Mon" => "LUNDI", "Tue" => "MARDI", "Wed" => "MERCREDI", "Thu" => "JEUDI", "Fri" => "VENDREDI", "Sat" => "SAMEDI", "Sun" => "DIMANCHE"];
        $mountConvert = ["01" => "JANVIER", "02" => "FÉVRIER", "03" => "MARS", "04" => "AVRIL", "05" => "MAI", "06" => "JUIN", "07" => "JUILLET", "08" => "AOÛT", "09" => "SEPTEMBRE", "10" => "OCTOBRE", "11" => "NOVEMBRE", "12" => "DÉCEMBRE"];
        $startHour = $this->getStartDate()->format("h")[0] == "0" ? $this->getStartDate()->format("g\\h") : $this->getStartDate()->format("h\\h");
        $startHour = $this->getStartDate()->format("i") == "00" ? $startHour : $startHour . $this->getStartDate()->format("i");
        $endHour = $this->getEndDate()->format("H")[0] == "0" ? $this->getEndDate()->format("g\\H") : $this->getEndDate()->format("H\\h");
        $endHour = $this->getEndDate()->format("i") == "00" ? $endHour : $endHour . $this->getEndDate()->format("i");
        $timestamp = strtotime($this->getStartDate()->format("Y-m-d"));
        $dates["date"] = $daysConvert[date('D', $timestamp)] . " " . $this->getStartDate()->format("d") . " " . $mountConvert[$this->getStartDate()->format("m")];
        $dates["hours"] = $startHour . " - " . $endHour;
        return $dates;
    }

    public function datesToString(): array
    {
        $daysConvert = ["Mon" => "LUNDI", "Tue" => "MARDI", "Wed" => "MERCREDI", "Thu" => "JEUDI", "Fri" => "VENDREDI", "Sat" => "SAMEDI", "Sun" => "DIMANCHE"];
        $mountConvert = ["01" => "JANVIER", "02" => "FÉVRIER", "03" => "MARS", "04" => "AVRIL", "05" => "MAI", "06" => "JUIN", "07" => "JUILLET", "08" => "AOÛT", "09" => "SEPTEMBRE", "10" => "OCTOBRE", "11" => "NOVEMBRE", "12" => "DÉCEMBRE"];
        $dates = [];
        $unity = $this->getUnityByPosition(2);
        if ($unity) {
            $dates = $unity->displayableDates($daysConvert, $mountConvert);
        }
        if (!empty($dates)) {
            return $dates;
        }

        if ($this->getStartDate()->format('d/m/Y') !== $this->getEndDate()->format('d/m/Y')) {
            $unity = $this->getUnityByPosition(1);
            return $unity->displayableDates($daysConvert, $mountConvert);
        }
        $startHour = $this->getStartDate()->format("H")[0] == "0" ? $this->getStartDate()->format("g\\h") : $this->getStartDate()->format("h\\h");
        $startHour = $this->getStartDate()->format("i") == "00" ? $startHour : $startHour . $this->getStartDate()->format("i");
        $endHour = $this->getEndDate()->format("H")[0] == "0" ? $this->getEndDate()->format("g\\h") : $this->getEndDate()->format("h\\h");
        $endHour = $this->getEndDate()->format("i") == "00" ? $endHour : $endHour . $this->getEndDate()->format("i");
        $timestamp = strtotime($this->getStartDate()->format("Y-m-d"));
        $dates[] = [
            "date" => $daysConvert[date('D', $timestamp)] . " " . $this->getStartDate()->format("d") . " " . $mountConvert[$this->getStartDate()->format("m")],
            "hours" => $startHour . " - " . $endHour
        ];
        return $dates;
    }

    public function startMounth() {
        $mountConvert = ["01" => "JANVIER", "02" => "FÉVRIER", "03" => "MARS", "04" => "AVRIL", "05" => "MAI", "06" => "JUIN", "07" => "JUILLET", "08" => "AOÛT", "09" => "SEPTEMBRE", "10" => "OCTOBRE", "11" => "NOVEMBRE", "12" => "DÉCEMBRE"];
        return $mountConvert[$this->startDate->format("m")];
    }

    /**
     * @return int
     */
    public function getNombrePlacesInitiales(): ?int
    {
        return $this->nombrePlacesInitiales;
    }

    /**
     * @param ?int $nombrePlacesInitiales
     * @return Formation
     */
    public function setNombrePlacesInitiales(?int $nombrePlacesInitiales): Formation
    {
        $this->nombrePlacesInitiales = $nombrePlacesInitiales;
        return $this;
    }

    public function getNbPlacesRestantes(): ?int
    {
        if (!$this->nombrePlacesInitiales) {
            return null;
        }
        return $this->nombrePlacesInitiales - count($this->getParticipants());
    }

    public function loadInfosFromProgramme(Programme $programme)
    {
        if ($programme->isClasseVirtuelle()) {
            $this->setOutil("Zoom");
            $this->setNombrePlacesInitiales(self::DEFAULT_MAX_PLACES_VIRTUELLE);
        } elseif ($programme->isElearning()) {
            $this->setOutil("E-Learning");
            $this->setNombrePlacesInitiales(self::DEFAULT_MAX_PLACES_ELEARNING);
        } elseif ($programme->isSurSite()) {
            $this->setNombrePlacesInitiales(self::DEFAULT_MAX_PLACES_SITE);
        }

        if ($programme->getCategory()) {
            $this->setTheme($programme->getCategory());
        }

        foreach ($programme->getUnities() as $unity) {
            $unitySession = new UnitySession();
            if ($unity->isOnSite() || $unity->isVirtuelle()) {
                for ($i = 1; $i <= $unity->getNbDays(); $i++) {
                    $unitySession->addUnitySessionDate(new UnitySessionDate());
                }
            }
            $this->addUnity($unitySession);
        }
    }

    /**
     * @return string
     */
    public function getDepartement(): ?string
    {
        return $this->departement;
    }

    /**
     * @param string $departement
     * @return Formation
     */
    public function setDepartement(?string $departement): Formation
    {
        $this->departement = $departement;
        return $this;
    }
    public function getDateLimiteInscription()
    {
        if ($this->getProgramme()->isElearning()) {
            return $this->getClosingDate();
        }
        $openingDate = clone $this->getOpeningDate();
        return $openingDate->modify("-1 day");
    }

    public function isPluriAnnuelle() : ?bool
    {
        return $this->getOpeningDate() && $this->getClosingDate() ? $this->getClosingDate()->format('Y') != $this->getOpeningDate()->format('Y') : null;
    }

    /**
     *  Vérifie s'il y a au moins une heure connectée dans les unités de l'année passée en paramètre
     * @param $year
     * @return bool
     */
    public function containOffline($year): bool
    {
        foreach ($this->getUnities() as $key => $unity) {
            $checkUnityFormation = false;
            $unityFormation = $this->getProgramme()->getUnityByPosition($key +1);
            if ($unity->getUnitySessionDates() && count($unity->getUnitySessionDates())) {
                $firstUnitySessionDate = $unity->getUnitySessionDates()->first();
                if ($firstUnitySessionDate->getStartDate()->format("Y") == $year) {
                    $checkUnityFormation = true;
                }
            } else {
                if ($unity->getOpeningDate()->format("Y") == $year) {
                    $checkUnityFormation = true;
                }
            }

            if ($checkUnityFormation) {
                if ($unityFormation->getNbHoursOffline()) {
                    return true;
                }
            }
        }
        return false;
    }

    public function hasReunionOnYear($year) : bool
    {
        return $this->startDate->format('Y') == $year;
    }

    public function shouldDisplayAttestationN1(): bool
    {
        return $this->isPluriAnnuelle() && $this->containOffline($this->closingDate->format('Y'));
    }

    public function getReunionDays() {
        if ($reunionUnity = $this->getUnityByPosition(2)) { // Réunions sur l'unité 2
            return $reunionUnity->getUnitySessionDates() ? $reunionUnity->getUnitySessionDates() : false;
        }
        if ($reunionUnity = $this->getUnityByPosition(1)) { // Réunions sur l'unité 1
            return $reunionUnity->getUnitySessionDates() ? $reunionUnity->getUnitySessionDates() : false;
        }
        return false;
    }

    public function hasElearningUnity(): bool
    {
        /** @var UnitySession $unity */
        for ($i = 1; $i <= count($this->unities); $i++) {
            $unityFormation = $this->programme->getUnityByPosition($i);
            if ($unityFormation && $unityFormation->isElearning()) {
                return true;
            }
        }
        return false;
    }

    public function isOneUnity() : bool
    {
        return count($this->getUnities()) === 1;
    }

    public function isTwoUnity() : bool
    {
        return count($this->getUnities()) === 2;
    }

    public function isThreeUnity() : bool
    {
        return count($this->getUnities()) === 3;
    }

    public function isElearningWithThreeUnity() {
        return $this->getProgramme()->isElearning() && $this->isElearning() && $this->isThreeUnity();
    }

    public function postCourseOpeningDate()
    {
        if ($this->isThreeUnity()) {
            return $this->getUnityByPosition(3)->getOpeningDate();
        }
        return false;
    }

    public function postCourseClosingDate()
    {
        return $this->getClosingDate();
    }

    /**
     * Set course
     *
     * @param Course $course
     * @return Formation
     */
    public function setCourse(Course $course = null)
    {
        $this->course = $course;

        return $this;
    }

    /**
     * Get course
     *
     * @return Course
     */
    public function getCourse()
    {
        return $this->course;
    }

    /**
     * Get course
     *
     * @return bool
     */
    public function hasCourse()
    {
        return $this->course !== null;
    }

    public function hasFormPost(): bool
    {
        return true;
    }

    public function shouldMergeAudit2Questions(): bool
    {
        return $this->isVignette() && !$this->isVfc() && !$this->isVignetteAudit();
    }

    public function isFourUnity() {
        $references = ["57202425402", "57202425403", "57202425404", "57202425405", "57202425406", "57202425407", "57202425408", "57202425409", "57202425410", "57202425411", "57202425412", "57202425413", "57202425414", "5**********", "57202425416", "57202425601", "57202425616", "57202425617", "57202425618", "57202425619", "57202425619", "57202425626"];
        return in_array($this->getProgramme()->getReference(), $references);
    }

    public function canHavePreRestitutionsForDownload(): bool
    {
        return false;
    }

    public function canDisplayDetailDateAndPlaceReunion(): bool
    {
        return false;
    }

    public function getDelayForSelectPatients(): string
    {
        return '6 semaines';
    }

    public function getDurationTotalN(): int
    {
        if ($this->isPluriAnnuelle()) {
            /** @var UnityFormation $unity */
            return array_reduce($this->getUnitiesFormationOfN(), function ($carry, $unity) {
                return $carry + $unity->getNbHours() + $unity->getNbHoursConnected() + $unity->getNbHoursOffline();
            }, 0);
        }
        return $this->programme->getDurationTotal() ?? 0;
    }

    public function getDurationPresentielleN(): int
    {
        if ($this->isPluriAnnuelle()) {
            return array_reduce($this->getUnitiesFormationOfN(), function ($carry, $unity) {
                return $carry + $unity->getNbHours();
            }, 0);
        }
        return $this->programme->getDurationPresentielle() ?? 0;
    }

    public function getDurationNotPresentielleN(): int
    {
        if ($this->isPluriAnnuelle()) {
            return array_reduce($this->getUnitiesFormationOfN(), function ($carry, $unity) {
                return $carry + $unity->getNbHoursConnected() + $unity->getNbHoursOffline();
            }, 0);
        }
        return $this->programme->getDurationNotPresentielle() ?? 0;
    }

    public function getDurationTotalN1(): int
    {
        if (!$this->isPluriAnnuelle()) {
            return 0;
        }
        return array_reduce($this->getUnitiesFormationOfN1(), function ($carry, $unity) {
            return $carry + $unity->getNbHours() + $unity->getNbHoursConnected() + $unity->getNbHoursOffline();
        }, 0);
    }

    public function getDurationPresentielleN1(): int
    {
        if (!$this->isPluriAnnuelle()) {
            return 0;
        }
        return array_reduce($this->getUnitiesFormationOfN1(), function ($carry, $unity) {
            return $carry + $unity->getNbHours();
        }, 0);
    }

    public function getDurationNotPresentielleN1(): int
    {
        if (!$this->isPluriAnnuelle()) {
            return 0;
        }
        return array_reduce($this->getUnitiesFormationOfN1(), function ($carry, $unity) {
            return $carry + $unity->getNbHoursConnected() + $unity->getNbHoursOffline();
        }, 0);
    }

    /**
     * @return UnityFormation[]
     */
    public function getUnitiesFormationOfN(): array
    {
        $res = [];
        foreach ($this->programme->getUnities() as $key => $unityProgramme) {
            $unitySession = $this->getUnityByPosition($key + 1);
            if ($unitySession->getOpeningDate() && $unitySession->getOpeningDate()->format('Y') === $this->getOpeningDate()?->format('Y')) {
                $res[] = $unityProgramme;
            }
        }
        return $res;
    }

    /**
     * @return UnityFormation[]
     */
    public function getUnitiesFormationOfN1(): array
    {
        $res = [];
        foreach ($this->programme->getUnities() as $key => $unityProgramme) {
            $unitySession = $this->getUnityByPosition($key + 1);
            if ($unitySession->getOpeningDate() && $unitySession->getOpeningDate()?->format('Y') === $this->getClosingDate()?->format('Y')) {
                $res[] = $unityProgramme;
            }
        }
        return $res;
    }

    public function calculateEcheanceNextModule(?string $nextModule = null): \DateTime
    {
        return $this->getClosingDate();
    }
}
