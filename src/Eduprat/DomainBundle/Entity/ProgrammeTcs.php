<?php

namespace Eduprat\DomainBundle\Entity;
use Doctrine\ORM\Mapping as ORM;
use Eduprat\DomainBundle\Entity\Programme;

/**
 * ProgrammeTcs
 */
#[ORM\HasLifecycleCallbacks]
#[ORM\Entity(repositoryClass: 'Eduprat\DomainBundle\Repository\ProgrammeTcsRepository')]
#[ORM\Table(name: 'programme_tcs')]
abstract class ProgrammeTcs extends Programme
{
    const NB_FILE_EMARGEMENT = 2;

    /**
     * Formation constructor.
     */
    public function __construct()
    {
        parent::__construct();
        $this->setSessionType(Formation::TYPE_TCS);
        $this->setFormType(Formation::FORM_TYPE_TCS);
        $this->setLinkVideoModule1("https://player.vimeo.com/video/968077583?h=fe5a662911");
    }

    public function disabledDatesForUnity(int $index): bool
    {
        return $index === 3;
    }

    public function disabledHoursForUnity(int $index): bool
    {
        return $index !== 3;
    }

    public function nbEmargementNeeded(): int
    {
        return self::NB_FILE_EMARGEMENT;
    }
}
