<?php

namespace Eduprat\DomainBundle\Entity;

use Doctrine\DBAL\Types\Types;
use Doctrine\Common\Collections\Order;
use Symfony\Component\HttpFoundation\File\UploadedFile;
use Symfony\Component\Validator\Constraints as Assert;
use Symfony\Component\Validator\Context\ExecutionContextInterface;
use Symfony\Component\HttpFoundation\File\File;
use Doctrine\ORM\Mapping as ORM;
use Doctrine\Common\Collections\ArrayCollection;
use Doctrine\Common\Collections\Collection;
use Eduprat\AdminBundle\Entity\Person;
use Vich\UploaderBundle\Mapping\Annotation as Vich;
use Doctrine\Common\Collections\Criteria;


/**
 * Programme
 */
#[ORM\HasLifecycleCallbacks]
#[ORM\Entity(repositoryClass: 'Eduprat\DomainBundle\Repository\ProgrammeRepository')]
#[ORM\Table(name: 'programme')]
#[ORM\InheritanceType('JOINED')]
#[ORM\DiscriminatorColumn(name: 'discr', type: 'string')]
#[ORM\DiscriminatorMap(['programme' => 'Programme', 'programme_vfc_on_site' => 'ProgrammeVfcOnSite', 'programme_vfc_virtual' => 'ProgrammeVfcVirtual', 'programme_tcs_virtual' => 'ProgrammeTcsVirtual', 'programme_tcs_on_site' => 'ProgrammeTcsOnSite'])]
#[Vich\Uploadable]
class Programme
{

    const TYPE_PROGRAMME = 'programme';
    const TYPE_VFC_ON_SITE = 'programme_vfc_on_site';
    const TYPE_VFC_VIRTUAL = 'programme_vfc_virtual';

    const TYPE_TCS_ON_SITE = 'programme_tcs_on_site';
    const TYPE_TCS_VIRTUAL = 'programme_tcs_virtual';

    const ELASTIC_INDEX = 'programme';

    const ANDPC_STATUS_SUBMITTED = 'submitted';
    const ANDPC_STATUS_PUBLISHED = 'published';
    const ANDPC_STATUS_EVALUATED = 'evaluated';
    const ANDPC_STATUS_REJECTED = 'rejected';
    const ANDPC_STATUSES = array(
        self::ANDPC_STATUS_SUBMITTED => "Déposée",
        self::ANDPC_STATUS_PUBLISHED => "Publiée",
        self::ANDPC_STATUS_EVALUATED => "Évaluée",
        self::ANDPC_STATUS_REJECTED => "Rejetée"
    );

    const METHOD_EPP = 'epp';
    const METHOD_CONTINUE = 'continue';
    const METHOD_INTEGRE = 'integre';
    const METHOD_GESTION = 'gestion';
    const METHODS = array(
        self::METHOD_EPP => 'EPP',
        self::METHOD_CONTINUE => 'Formation continue',
        self::METHOD_INTEGRE => 'Programme intégré',
        self::METHOD_GESTION => 'Gestion des risques',
    );

    const FORMAT_MIXTE = "Mixte";
    const FORMAT_ELEARNING = "Elearning";
    const FORMAT_PRESENTIEL = "Présentiel";
    const FORMAT_ELEARNING_TXT = "Non présentiel";

    const FORMATS = array(
        self::FORMAT_MIXTE => self::FORMAT_MIXTE,
        self::FORMAT_ELEARNING => self::FORMAT_ELEARNING,
        self::FORMAT_ELEARNING_TXT => self::FORMAT_PRESENTIEL,
    );

    const FORMATS_RAW = array(
        self::FORMAT_MIXTE => 'mixte',
        self::FORMAT_ELEARNING => 'elearning',
        self::FORMAT_PRESENTIEL => 'presentiel',
    );

    const PRESENCE_SITE = "Sur site";
    const PRESENCE_VIRTUELLE = "Classe virtuelle";
    const PRESENCE_ELEARNING = "E-Learning";
    const REFERENCES_VACCIN = ['57202325330'];
    const REFERENCES_ADMINISTRATION_VACCIN = ['57202325331'];
    const REFERENCES_VACCIN_SOUS_RESPONSABILITE = ['57202325228'];
    const REFERENCES_FC_CYSTITE_COMPLETE = ['57202425589'];
    const REFERENCES_FC_ANGINE_COMPLETE = ['57202425590'];
    const REFERENCE_FC_ANGINE_SANS_TROD = ['57202425591'];

    const DEFAULT_VIDEO_MODULE1 = 'https://player.vimeo.com/video/834028397?h=e695f26d3f';
    const DELAI_END_DATE_EL2UNITY = 14;
    const DELAI_START_DATE_SITE_1_UNITY = -14;
    const NB_FILE_EMARGEMENT = 1;

    /**
     * @var int
     */
    #[ORM\Column(name: 'id', type: Types::INTEGER)]
    #[ORM\Id]
    #[ORM\GeneratedValue(strategy: 'AUTO')]
    private ?int $id = null;

    /**
     * @var string
     */
    #[ORM\Column(name: 'title', type: Types::STRING, length: 255)]
    private ?string $title = null;

    /**
     * @var string
     */
    #[ORM\Column(name: 'sessionType', type: Types::STRING, length: 255, nullable: true)]
    private ?string $sessionType = null;

    /**
     * @var string
     */
    #[ORM\Column(name: 'formType', type: Types::STRING, length: 255, nullable: true)]
    private ?string $formType = null;

    /**
     * @var string
     */
    #[ORM\Column(name: 'reference', type: Types::STRING, length: 255)]
    private ?string $reference = null;

    /**
     * @var string
     */
    #[ORM\Column(name: 'cout', type: Types::STRING, length: 255)]
    private ?string $cout = "Nous contacter : <EMAIL> ou 05 56 51 65 14.";


    /**
     * @var string
     */
    #[ORM\Column(name: 'prerequis', type: Types::TEXT, length: 255)]
    private ?string $prerequis = "Aucun";

     /**
     * @var string
     */
    #[ORM\Column(name: 'picture', type: Types::STRING, length: 255, nullable: true)]
    private ?string $picture = null;

    /**
     * @var File
     */
    #[Vich\UploadableField(mapping: 'programme_picture', fileNameProperty: 'picture')]
    private $pictureFile;

     /**
     * @var string
     */
    #[ORM\Column(name: 'firstAdditionalInfosPicture', type: Types::STRING, length: 255, nullable: true)]
    private ?string $firstAdditionalInfosPicture = null;

    /**
     * @var File
     */
    #[Vich\UploadableField(mapping: 'programme_firstAdditionalInfosPicture', fileNameProperty: 'firstAdditionalInfosPicture')]
    private $firstAdditionalInfosPictureFile;

    /**
     * @var string
     */
    #[ORM\Column(name: 'secondAdditionalInfosPicture', type: Types::STRING, length: 255, nullable: true)]
    private ?string $secondAdditionalInfosPicture = null;

    /**
     * @var File
     */
    #[Vich\UploadableField(mapping: 'programme_secondAdditionalInfosPicture', fileNameProperty: 'secondAdditionalInfosPicture')]
    private $secondAdditionalInfosPictureFile;

    /**
     * @var string
     */
    #[ORM\Column(name: 'firstAdditionalInfosPictureLink', type: Types::STRING, length: 255, nullable: true)]
    private ?string $firstAdditionalInfosPictureLink = null;

    /**
     * @var string
     */
    #[ORM\Column(name: 'secondAdditionalInfosPictureLink', type: Types::STRING, length: 255, nullable: true)]
    private ?string $secondAdditionalInfosPictureLink = null;

    /**
     * @var array
     */
    #[ORM\Column(name: 'categories', type: Types::JSON, nullable: true)]
    private $categories;

    /**
     * @var array
     */
    #[ORM\Column(name: 'specialities', type: Types::JSON, nullable: true)]
    private $specialities;

    /**
     * @var Collection<int, PriseEnCharge>
     */
    #[ORM\ManyToMany(targetEntity: 'Eduprat\DomainBundle\Entity\PriseEnCharge', cascade: ['persist'])]
    #[ORM\JoinTable(name: 'programme_prise_en_charge', joinColumns: [new ORM\JoinColumn(name: 'programme', referencedColumnName: 'id', onDelete: 'CASCADE')], inverseJoinColumns: [new ORM\JoinColumn(name: 'prises_en_charge', referencedColumnName: 'id')])]
    #[ORM\OrderBy(['name' => 'ASC'])]
    private Collection $prisesEnCharge;

    /**
     * @var string
    */
    private $tempTags;

    /**
     * @var Collection<int, Tag>
     */
    #[ORM\ManyToMany(targetEntity: 'Eduprat\DomainBundle\Entity\Tag', cascade: ['persist'])]
    #[ORM\JoinTable(name: 'programme_tag', joinColumns: [new ORM\JoinColumn(name: 'programme', referencedColumnName: 'id', onDelete: 'CASCADE')], inverseJoinColumns: [new ORM\JoinColumn(name: 'tag', referencedColumnName: 'id')])]
    #[ORM\OrderBy(['name' => 'ASC'])]
    private Collection $tags;

    /**
     * @var string
     */
    #[ORM\Column(name: 'exercisesMode', type: Types::JSON, nullable: true)]
    private $exercisesMode;

    /**
     * @var string
     */
    #[ORM\Column(name: 'resume', type: Types::TEXT, nullable: true)]
    private ?string $resume = null;

    /**
     * @var string
     */
    #[ORM\Column(name: 'objectives', type: Types::TEXT, nullable: true)]
    private ?string $objectives = null;

    /**
     * @var string
     */
    #[ORM\Column(name: 'nationalOrientation', type: Types::TEXT, nullable: true)]
    private ?string $nationalOrientation = null;

    /**
     * @var string
     */
    #[ORM\Column(name: 'additionalInfos', type: Types::TEXT, nullable: true)]
    private ?string $additionalInfos = null;

    /**
     * @var bool
     */
    #[ORM\Column(name: 'certifying', type: Types::BOOLEAN)]
    private ?bool $certifying = false;

    /**
     * @var float
     */
    #[ORM\Column(name: 'durationPresentielle', type: Types::FLOAT, nullable: true)]
    private ?float $durationPresentielle = null;

/**
     * @var bool
     */
    #[ORM\Column(name: 'thereIsOfflineHours', type: Types::BOOLEAN, nullable: true)]
    private ?bool $thereIsOfflineHours = null;

    /**
     * @var float
     */
    #[ORM\Column(name: 'durationNotPresentielle', type: Types::FLOAT, nullable: true)]
    private ?float $durationNotPresentielle = null;

    /**
     * @var float
     */
    #[ORM\Column(name: 'durationNotPresentielleActalians', type: Types::FLOAT, nullable: true)]
    private ?float $durationNotPresentielleActalians = null;

    /**
     * @var string
     */
    #[ORM\Column(name: 'objectivesActalians', type: Types::TEXT, nullable: true)]
    private ?string $objectivesActalians = null;

    /**
     * @var string
     */
    #[ORM\Column(name: 'messageRappel', type: Types::TEXT, nullable: true)]
    private ?string $messageRappel = null;


    /**
     * @var bool
     */
    #[ORM\Column(name: 'autocompleteReference', type: Types::BOOLEAN, nullable: true)]
    protected ?bool $autocompleteReference = null;

    /**
     * @var Collection<int, Formation>
     */
    #[ORM\OneToMany(targetEntity: 'Eduprat\DomainBundle\Entity\Formation', mappedBy: 'programme')]
    private Collection $formations;

    /**
     * @var \DateTimeInterface
     */
    #[ORM\Column(name: 'createdAt', type: Types::DATETIME_MUTABLE)]
    private ?\DateTimeInterface $createdAt = null;

    /**
     * @var \DateTimeInterface
     */
    #[ORM\Column(name: 'updatedAt', type: Types::DATETIME_MUTABLE, nullable: true)]
    private ?\DateTimeInterface $updatedAt = null;

    /**
     * @var integer
     */
    #[ORM\Column(name: 'year', type: Types::INTEGER, nullable: true)]
    private ?int $year = null;

    /**
     * @var string
     */
    #[ORM\Column(name: 'presence', type: Types::STRING, nullable: true)]
    private ?string $presence = null;

    /*** CHAMPS A SUPPRIMER ***/
    /**
     * @var Collection<int, Formateur>
     */
    #[ORM\OneToMany(targetEntity: 'Eduprat\DomainBundle\Entity\Formateur', mappedBy: 'programme', cascade: ['persist'], orphanRemoval: true)]
    private Collection $formateurs;

    /**
     * @var string
     */
    #[ORM\Column(name: 'address', type: Types::STRING, length: 255, nullable: true)]
    private ?string $address = null;

    /**
     * @var string
     */
    #[ORM\Column(name: 'address2', type: Types::STRING, length: 255, nullable: true)]
    private ?string $address2 = null;

    /**
     * @var string
     */
    #[ORM\Column(name: 'city', type: Types::STRING, length: 255, nullable: true)]
    private ?string $city = null;

    /**
     * @var string
     */
    #[ORM\Column(name: 'zipCode', type: Types::STRING, length: 255, nullable: true)]
    private ?string $zipCode = null;

    /**
     * @var float
     */
    #[ORM\Column(name: 'duration', type: Types::FLOAT, nullable: true)]
    private ?float $duration = null;

    /**
     * @var \DateTimeInterface
     */
    #[ORM\Column(name: 'startDate', type: Types::DATETIME_MUTABLE, nullable: true)]
    private ?\DateTimeInterface $startDate = null;

    /**
     * @var \DateTimeInterface
     */
    #[ORM\Column(name: 'endDate', type: Types::DATETIME_MUTABLE, nullable: true)]
    private ?\DateTimeInterface $endDate = null;

    /**
     * @var Collection<int, Coordinator>
     */
    #[ORM\OneToMany(targetEntity: 'Eduprat\DomainBundle\Entity\Coordinator', mappedBy: 'programme', cascade: ['persist'], orphanRemoval: true)]
    private Collection $coordinators;

    /**
     * @var \DateTimeInterface
     */
    #[ORM\Column(name: 'evaluationParticipantAnswersCount', type: Types::INTEGER, nullable: true)]
    private ?int $evaluationParticipantAnswersCount = null;

    /**
     * @var \DateTimeInterface
     */
    #[ORM\Column(name: 'evaluationParticipantAnswersSum', type: Types::INTEGER, nullable: true)]
    private ?int $evaluationParticipantAnswersSum = null;

    /**
     * @var \DateTimeInterface
     */
    #[ORM\Column(name: 'evaluationFormerAnswersCount', type: Types::INTEGER, nullable: true)]
    private ?int $evaluationFormerAnswersCount = null;

    /**
     * @var \DateTimeInterface
     */
    #[ORM\Column(name: 'evaluationFormerAnswersSum', type: Types::INTEGER, nullable: true)]
    private ?int $evaluationFormerAnswersSum = null;

    /**
     * @var float
     */
    #[ORM\Column(name: 'costKilometres', type: Types::FLOAT, nullable: true)]
    protected ?float $costKilometres = null;

    /**
     * @var float
     */
    #[ORM\Column(name: 'costBadges', type: Types::FLOAT, nullable: true)]
    protected ?float $costBadges = null;

    /**
     * @var float
     */
    #[ORM\Column(name: 'costRetrocessions', type: Types::FLOAT, nullable: true)]
    protected ?float $costRetrocessions = null;

    /**
     * @var float
     */
    #[ORM\Column(name: 'costMateriel', type: Types::FLOAT, nullable: true)]
    protected ?float $costMateriel = null;

    /**
     * @var float
     */
    #[ORM\Column(name: 'costDivers', type: Types::FLOAT, nullable: true)]
    protected ?float $costDivers = null;

    /**
     * @var string
     */
    #[ORM\Column(name: 'region', type: Types::STRING, nullable: true)]
    protected ?string $region = null;

    /**
     * @var string
     */
    #[ORM\Column(name: 'latitude', type: Types::STRING, length: 255, nullable: true)]
    private ?string $latitude = null;

    /**
     * @var string
     */
    #[ORM\Column(name: 'longitude', type: Types::STRING, length: 255, nullable: true)]
    private ?string $longitude = null;

    /**
     * @var string
     */
    #[ORM\Column(name: 'andpcStatus', type: Types::STRING, nullable: true)]
    protected ?string $andpcStatus = null;

    /**
     * @var string
     */
    #[ORM\Column(name: 'method', type: Types::STRING, nullable: true)]
    protected ?string $method = null;

    /**
     * @var Collection<int, EvaluationGlobalAnswer>
     */
    #[ORM\OneToMany(targetEntity: 'Eduprat\DomainBundle\Entity\EvaluationGlobalAnswer', mappedBy: 'programme', cascade: ['persist', 'remove'])]
    protected Collection $evaluationAnswers;

    /**
     * @var string
     */
    #[ORM\Column(name: 'format', type: Types::STRING, nullable: true)]
    protected ?string $format = null;

    /**
     * @var AuditCategory
     */
    #[ORM\ManyToOne(targetEntity: 'Eduprat\DomainBundle\Entity\AuditCategory', cascade: ['persist'])]
    #[ORM\JoinColumn(name: 'category', nullable: true)]
    private ?AuditCategory $category = null;

    /**
     * @var Collection<int, Connaissance>
     */
    #[ORM\OneToMany(targetEntity: 'Eduprat\DomainBundle\Entity\Connaissance', mappedBy: 'programme', orphanRemoval: true, cascade: ['persist', 'remove'])]
    private Collection $connaissances;

    /**
     * @var Collection<int, Competence>
     */
    #[ORM\OneToMany(targetEntity: 'Eduprat\DomainBundle\Entity\Competence', mappedBy: 'programme', orphanRemoval: true, cascade: ['persist', 'remove'])]
    private Collection $competences;

    /**
     * @var string
     */
    #[ORM\Column(name: 'commentaireNotif', type: Types::STRING, nullable: true)]
    protected ?string $commentaireNotif = null;

    /**
     * @var Collection<int, TopoProgrammeFiles>
     */
    #[ORM\OneToMany(targetEntity: 'Eduprat\DomainBundle\Entity\TopoProgrammeFiles', mappedBy: 'programme', cascade: ['persist'])]
    protected Collection $topoProgrammeFiles;

    /**
     * @var Collection<int, ToolProgrammeFiles>
     */
    #[ORM\OneToMany(targetEntity: 'Eduprat\DomainBundle\Entity\ToolProgrammeFiles', mappedBy: 'programme', cascade: ['persist'])]
    protected Collection $toolProgrammeFiles;

    /**
     * @var Collection<int, DocumentsPedagogiquesFiles>
     */
    #[ORM\OneToMany(targetEntity: DocumentsPedagogiquesFiles::class, mappedBy: 'programme', cascade: ['persist'])]
    protected Collection $documentsPedagogiquesFiles;

    /**
     * @var Collection<int, UnityFormation>
     */
    #[ORM\OneToMany(targetEntity: 'Eduprat\DomainBundle\Entity\UnityFormation', mappedBy: 'programme', orphanRemoval: true, cascade: ['persist', 'remove'])]
    private Collection $unities;

    /**
     * @var ProgrammesAssocies
     */
    #[ORM\ManyToOne(targetEntity: 'Eduprat\DomainBundle\Entity\ProgrammesAssocies', inversedBy: 'id', cascade: ['persist'])]
    #[ORM\JoinColumn(name: 'programmesAssocies', nullable: true)]
    #[Assert\Valid]
    protected ?ProgrammesAssocies $programmesAssocies = null;

    /**
     * @var string
     */
    #[ORM\Column(name: 'link_video_module1', type: Types::STRING, nullable: true)]
    protected ?string $linkVideoModule1 = self::DEFAULT_VIDEO_MODULE1;

    /*** FIN CHAMPS A SUPPRIMER ***/

    /**
     * 3 champs qui ne sont utilisés et calculés que pour l'API
     */
    private ?int $exportNbVotants = null;
    private ?float $exportNote = null;
    private ?int $exportNbParticipants = null;
    /**
     * Programme constructor.
     */
    public function __construct()
    {
        $this->tags = new ArrayCollection();
        $this->documentsPedagogiquesFiles = new ArrayCollection();
        $this->createdAt = new \DateTime();
        $this->formateurs = new ArrayCollection();
        $this->formations = new ArrayCollection();
        $this->unities = new ArrayCollection();
        $this->coordinators = new ArrayCollection();
        $this->evaluationAnswers = new ArrayCollection();
        $this->prisesEnCharge = new ArrayCollection();
        $this->connaissances = new ArrayCollection();
        $this->competences = new ArrayCollection();
        $this->topoProgrammeFiles = new ArrayCollection();
        $this->toolProgrammeFiles = new ArrayCollection();
        $this->year = (int) date("Y");
        $this->presence = self::PRESENCE_SITE;
    }

    public function __clone() {
        $year = $this->year;
        $presence = $this->presence;
        if ($this->id) {
            $this->id = null;
            $this->isVfc() || $this->isTcs() ? $this->__construct(clone($this->getUnityByPosition(1)), clone($this->getUnityByPosition(2)), clone($this->getUnityByPosition(3))) : $this->__construct();
            $this->year = $year;
            $this->presence = $presence;
        }
    }

    #[ORM\PreUpdate]
    public function setPreUpdate()
    {
        $this->updatedAt = new \DateTime();
        $this->updateFormat();
    }

    /**
     * Get id
     *
     * @return integer
     */
    public function getId()
    {
        return $this->id;
    }

    public function getProgrammeDiscr() {
        return self::TYPE_PROGRAMME;
    }

    /**
     * Set title
     *
     * @param string $title
     * @return Programme
     */
    public function setTitle($title)
    {
        $this->title = $title;

        return $this;
    }

    /**
     * Get title
     *
     * @return string
     */
    public function getTitle()
    {
        return $this->title;
    }

    /**
     * @return string
     */
    public function getLowercaseTitle()
    {
        return ucfirst(strtolower($this->title));
    }

    /**
     * @return string
     */
    public function getSessionType()
    {
        return $this->sessionType;
    }

    /**
     * @param string $sessionType
     * @return Programme
     */
    public function setSessionType($sessionType)
    {
        $this->sessionType = $sessionType;
        return $this;
    }

    /**
     * @return string
     */
    public function getFormType()
    {
        return $this->formType;
    }

    /**
     * @param string $formType
     * @return Programme
     */
    public function setFormType($formType)
    {
        $this->formType = $formType;
        return $this;
    }

    /**
     * Set reference
     *
     * @param string $reference
     * @return Programme
     */
    public function setReference($reference)
    {
        $this->reference = $reference;

        return $this;
    }

    /**
     * Get reference
     *
     * @return string
     */
    public function getReference()
    {
        return $this->reference;
    }


    /**
     * Set cout
     *
     * @param string $cout
     * @return Programme
     */
    public function setCout($cout)
    {
        $this->cout = $cout;

        return $this;
    }

    /**
     * Get cout
     *
     * @return string
     */
    public function getCout()
    {
        return $this->cout;
    }


    /**
     * Set prerequis
     *
     * @param string $prerequis
     * @return Programme
     */
    public function setPrerequis($prerequis)
    {
        $this->prerequis = $prerequis;

        return $this;
    }

    /**
     * Get prerequis
     *
     * @return string
     */
    public function getPrerequis()
    {
        return $this->prerequis;
    }

    /**
     * Set picture
     *
     * @param string $picture
     * @return Programme
     */
    public function setPicture($picture)
    {
        $this->picture = $picture;
        return $this;
    }

    /**
     * Get picture
     *
     * @return string
     */
    public function getPicture()
    {
        return $this->picture;
    }

    /**
     * @return File
     */
    public function getPictureFile()
    {
        return $this->pictureFile;
    }

    /**
     * @param File $pictureFile
     */
    public function setPictureFile(File $pictureFile = null)
    {
        $this->pictureFile = $pictureFile;
        if ($this->pictureFile instanceof UploadedFile) {
            $this->setPreUpdate();
        }
        return $this;
    }

    /**
     * Set firstAdditionalInfosPicture
     *
     * @param string $firstAdditionalInfosPicture
     * @return Programme
     */
    public function setFirstAdditionalInfosPicture($firstAdditionalInfosPicture)
    {
        $this->firstAdditionalInfosPicture = $firstAdditionalInfosPicture;
        return $this;
    }

    /**
     * Get firstAdditionalInfosPicture
     *
     * @return string
     */
    public function getFirstAdditionalInfosPicture()
    {
        return $this->firstAdditionalInfosPicture;
    }

    /**
     * @return File
     */
    public function getFirstAdditionalInfosPictureFile()
    {
        return $this->firstAdditionalInfosPictureFile;
    }

    /**
     * @param File $firstAdditionalInfosPictureFile
     */
    public function setFirstAdditionalInfosPictureFile(File $firstAdditionalInfosPictureFile = null)
    {
        $this->firstAdditionalInfosPictureFile = $firstAdditionalInfosPictureFile;
        if ($this->firstAdditionalInfosPicture instanceof UploadedFile) {
            $this->setPreUpdate();
        }
        return $this;
    }

    /**
     * Set secondAdditionalInfosPicture
     *
     * @param string $secondAdditionalInfosPicture
     * @return Programme
     */
    public function setSecondAdditionalInfosPicture($secondAdditionalInfosPicture)
    {
        $this->secondAdditionalInfosPicture = $secondAdditionalInfosPicture;
        return $this;
    }

    /**
     * Get secondAdditionalInfosPicture
     *
     * @return string
     */
    public function getSecondAdditionalInfosPicture()
    {
        return $this->secondAdditionalInfosPicture;
    }

    /**
     * @return File
     */
    public function getSecondAdditionalInfosPictureFile()
    {
        return $this->secondAdditionalInfosPictureFile;
    }

    /**
     * @param File $secondAdditionalInfosPictureFile
     */
    public function setSecondAdditionalInfosPictureFile(File $secondAdditionalInfosPictureFile = null)
    {
        $this->secondAdditionalInfosPictureFile = $secondAdditionalInfosPictureFile;
        if ($this->secondAdditionalInfosPicture instanceof UploadedFile) {
            $this->setPreUpdate();
        }
        return $this;
    }

    /**
     * @return string
     */
    public function getFirstAdditionalInfosPictureLink()
    {
        return $this->firstAdditionalInfosPictureLink;
    }

    /**
     * @param string $firstAdditionalInfosPictureLink
     * @return Programme
     */
    public function setFirstAdditionalInfosPictureLink($firstAdditionalInfosPictureLink)
    {
        $this->firstAdditionalInfosPictureLink = $firstAdditionalInfosPictureLink;
        return $this;
    }

    /**
     * @return string
     */
    public function getSecondAdditionalInfosPictureLink()
    {
        return $this->secondAdditionalInfosPictureLink;
    }

    /**
     * @param string $secondAdditionalInfosPictureLink
     * @return Programme
     */
    public function setSecondAdditionalInfosPictureLink($secondAdditionalInfosPictureLink)
    {
        $this->secondAdditionalInfosPictureLink = $secondAdditionalInfosPictureLink;
        return $this;
    }
    /**
     * Set categories
     *
     * @param array $categories
     * @return Programme
     */
    public function setCategories($categories)
    {
        $this->categories = $categories;
        return $this;
    }
    /**
     * Get categories
     *
     * @return array
     */
    public function getCategories()
    {
        return $this->categories ? array_values($this->categories) : null;
    }

    /**
     * Set specialities
     *
     * @param array $specialities
     * @return Programme
     */
    public function setSpecialities($specialities)
    {
        $this->specialities = $specialities;
        return $this;
    }

    /**
     * Get specialities
     *
     * @return array
     */
    public function getSpecialities()
    {
        return $this->specialities ? array_values($this->specialities) : null;
    }

    /**
     * Get tempTags
     *
     * @return array
     */
    public function getTempTags()
    {
        return $this->tempTags;
    }

    /**
     * Set tempTags
     *
     * @return array
     */
    public function setTempTags($tempTags)
    {
        $this->tempTags = $tempTags;
        return $this;
    }

    /**
     * @return Collection|Tag[]
     */
    public function getTags()
    {
        return $this->tags;
    }

    public function addTag($tag)
    {
        if ($this->tags != null && !$this->tags->contains($tag)) {
            $this->tags[] = $tag;
        }

        return $this;
    }

    public function removeTag($tag)
    {
        if ($this->tags->contains($tag)) {
            $this->tags->removeElement($tag);
        }

        return $this;
    }
    /**
     *
     * @return Collection|PriseEnCharge[]
     */
    public function getPrisesEnCharge()
    {
        return $this->prisesEnCharge;
    }

    public function addPrisesEnCharge($priseEnCharge)
    {
        if ($this->prisesEnCharge != null && !$this->prisesEnCharge->contains($priseEnCharge)) {
            $this->prisesEnCharge[] = $priseEnCharge;
        }

        return $this;
    }

    public function removePrisesEnCharge($priseEnCharge)
    {
        if ($this->prisesEnCharge->contains($priseEnCharge)) {
            $this->prisesEnCharge->removeElement($priseEnCharge);
        }

        return $this;
    }

    public function hasPrisesEnChargeDpc()
    {
        foreach($this->getPrisesEnCharge() as $prisesEnCharge) {
            if ($prisesEnCharge->isDpc()) {
                return true;
            }
        }
        return false;
    }

    /**
     * Set exercisesMode
     *
     * @param array $exercisesMode
     * @return Programme
     */
    public function setExercisesMode($exercisesMode)
    {
        $this->exercisesMode = $exercisesMode;
        return $this;
    }

    /**
     * Get exercisesMode
     *
     * @return array
     */
    public function getExercisesMode()
    {
        return $this->exercisesMode;
    }

    /**
     * Set resume
     *
     * @param string $resume
     * @return Programme
     */
    public function setResume($resume)
    {
        $this->resume = $resume;

        return $this;
    }

    /**
     * Get resume
     *
     * @return string
     */
    public function getResume()
    {
        return $this->resume;
    }

    /**
     * @return string
     */
    public function getObjectives()
    {
        return $this->objectives;
    }

    /**
     * @param string $objectives
     * @return Programme
     */
    public function setObjectives($objectives)
    {
        $this->objectives = $objectives;
        return $this;
    }

    /**
     * Set nationalOrientation
     *
     * @param string $nationalOrientation
     * @return Programme
     */
    public function setNationalOrientation($nationalOrientation)
    {
        $this->nationalOrientation = $nationalOrientation;

        return $this;
    }

    /**
     * Get nationalOrientation
     *
     * @return string
     */
    public function getNationalOrientation()
    {
        return $this->nationalOrientation;
    }

    /**
     * Set additionalInfos
     *
     * @param string $additionalInfos
     * @return Programme
     */
    public function setAdditionalInfos($additionalInfos)
    {
        $this->additionalInfos = $additionalInfos;

        return $this;
    }

    /**
     * Get additionalInfos
     *
     * @return string
     */
    public function getAdditionalInfos()
    {
        return $this->additionalInfos;
    }


    /**
     * @return bool
     */
    public function isCertifying()
    {
        return $this->certifying;
    }

    /**
     * @param bool $certifying
     * @return Programme
     */
    public function setCertifying($certifying)
    {
        $this->certifying = $certifying;
        return $this;
    }

    /**
     * @param bool $thereIsOfflineHours
     * @return Programme
     */
    public function setThereIsOfflineHours($thereIsOfflineHours)
    {
        $this->thereIsOfflineHours = $thereIsOfflineHours;

        return $this;
    }

    /**
     * @return bool
     */
    public function thereIsOfflineHours()
    {
        return $this->thereIsOfflineHours;
    }

    /**
     * Set durationPresentielle
     *
     * @param string $durationPresentielle
     * @return Programme
     */
    public function setDurationPresentielle($durationPresentielle)
    {
        $this->durationPresentielle = $durationPresentielle;

        return $this;
    }

    /**
     * Get durationPresentielle
     *
     * @return string
     */
    public function getDurationPresentielle()
    {
        return $this->durationPresentielle;
    }

    /**
     * Set durationNotPresentielle
     *
     * @param string $durationNotPresentielle
     * @return Programme
     */
    public function setDurationNotPresentielle($durationNotPresentielle)
    {
        $this->durationNotPresentielle = $durationNotPresentielle;

        return $this;
    }

    /**
     * Get durationNotPresentielle
     *
     * @return string
     */
    public function getDurationNotPresentielle()
    {
        return $this->durationNotPresentielle;
    }

    /**
     * Set durationNotPresentielleActalians
     *
     * @param string $durationNotPresentielleActalians
     * @return Programme
     */
    public function setDurationNotPresentielleActalians($durationNotPresentielleActalians)
    {
        $this->durationNotPresentielleActalians = $durationNotPresentielleActalians;

        return $this;
    }

    /**
     * Get durationNotPresentielleActalians
     *
     * @return string
     */
    public function getDurationNotPresentielleActalians()
    {
        return $this->durationNotPresentielleActalians;
    }

    /**
     * @return float
     */
    public function getDurationNotPresentielleTotal()
    {
        if (is_null($this->durationNotPresentielle) && is_null($this->durationNotPresentielleActalians)) {
            return null;
        }
        return $this->durationNotPresentielle + $this->durationNotPresentielleActalians;
    }

    /**
     * @return float
     */
    public function getDurationTotal()
    {
        return $this->durationPresentielle + $this->durationNotPresentielle + $this->durationNotPresentielleActalians;
    }

    public function getFormatByDurations()
    {
        $format = self::FORMAT_MIXTE;
        $format = $this->getDurationNotPresentielleTotal() !== null && $this->getDurationNotPresentielleTotal() > 0 ? self::FORMAT_ELEARNING : $format;
        $format = $this->getDurationPresentielle() !== null && $this->getDurationPresentielle() > 0 ? self::FORMAT_PRESENTIEL : $format;
        $format = $this->getDurationNotPresentielleTotal() !== null && $this->getDurationNotPresentielleTotal() > 0 && $this->getDurationPresentielle() !== null && $this->getDurationPresentielle() > 0 ? self::FORMAT_MIXTE : $format;
        return $format;
    }

    public function getFormatByDurationsTxt()
    {
        $format = self::FORMAT_MIXTE;
        $format = $this->getDurationNotPresentielleTotal() !== null && $this->getDurationNotPresentielleTotal() > 0 ? self::FORMAT_ELEARNING_TXT : $format;
        $format = $this->getDurationPresentielle() !== null && $this->getDurationPresentielle() > 0 ? self::FORMAT_PRESENTIEL : $format;
        $format = $this->getDurationNotPresentielleTotal() !== null && $this->getDurationNotPresentielleTotal() > 0 && $this->getDurationPresentielle() !== null && $this->getDurationPresentielle() > 0 ? self::FORMAT_MIXTE : $format;
        return $format;
    }

    /**
     * @return string
     */
    public function getFormat()
    {
        return $this->format;
    }

    /**
     * @param string $format
     * @return Programme
     */
    public function setFormat($format)
    {
        $this->format = $format;
        return $this;
    }

    public function isFormatMixte() {
        return $this->format === self::FORMAT_MIXTE;
    }

    public function isFormatPresentiel() {
        return $this->format === self::FORMAT_PRESENTIEL;
    }

    public function isFormatElearning() {
        return $this->format === self::FORMAT_ELEARNING;
    }

    /**
     * @return $this
     */
    #[ORM\PrePersist]
    public function updateFormat()
    {
        return $this->setFormat($this->getFormatByDurations());
    }

    public function hasEtutorat()
    {
        return $this->getConnaissances()->count() || $this->getCompetences()->count();
    }

    /**
     * Set category
     *
     * @param AuditCategory $category
     *
     * @return Survey
     */
    public function setCategory(AuditCategory $category)
    {
        $this->category = $category;

        return $this;
    }

    /**
     * Get category
     *
     * @return AuditCategory
     */
    public function getCategory()
    {
        return $this->category;
    }

    /**
     * @return string
     */
    public function getObjectivesActalians()
    {
        return $this->objectivesActalians;
    }

    /**
     * @param string $objectivesActalians
     * @return Programme
     */
    public function setObjectivesActalians($objectivesActalians)
    {
        $this->objectivesActalians = $objectivesActalians;
        return $this;
    }

    // /**
    //  * Set messageRappel
    //  *
    //  * @param string $messageRappel
    //  * @return Programme
    //  */
    // public function setMessageRappel($messageRappel)
    // {
    //     $this->messageRappel = $messageRappel;

    //     return $this;
    // }

    // /**
    //  * Get messageRappel
    //  *
    //  * @return string
    //  */
    // public function getMessageRappel()
    // {
    //     return $this->messageRappel;
    // }


    // /**
    //  * @return bool
    //  */
    // public function isAutocompleteReference()
    // {
    //     return $this->autocompleteReference;
    // }
    // /**
    //  * @param bool $autocompleteReference
    //  * @return Programme
    //  */
    // public function setAutocompleteReference($autocompleteReference)
    // {
    //     $this->autocompleteReference = $autocompleteReference;
    //     return $this;
    // }

    /**
     * Set createdAt
     *
     * @param \DateTime $createdAt
     * @return Programme
     */
    public function setCreatedAt($createdAt)
    {
        $this->createdAt = $createdAt;

        return $this;
    }

    /**
     * Get createdAt
     *
     * @return \DateTime
     */
    public function getCreatedAt()
    {
        return $this->createdAt;
    }

    /**
     * Set updatedAt
     *
     * @param \DateTime $updatedAt
     * @return Programme
     */
    public function setUpdatedAt($updatedAt)
    {
        $this->updatedAt = $updatedAt;

        return $this;
    }

    /**
     * Get updatedAt
     *
     * @return \DateTime
     */
    public function getUpdatedAt()
    {
        return $this->updatedAt;
    }

    /**
     * @return int
     */
    public function getYear()
    {
        return $this->year;
    }

    /**
     * @param int $year
     * @return Programme
     */
    public function setYear($year)
    {
        $this->year = $year;
        return $this;
    }

    /**
     * @return string
     */
    public function getPresence()
    {
        return $this->presence;
    }

    /**
     * @param string $presence
     * @return Programme
     */
    public function setPresence($presence)
    {
        $this->presence = $presence;
        return $this;
    }

    /**
     * Presence Classe Virtuelle
     * @return bool
     */
    public function isClasseVirtuelle() {
        return $this->getPresence() === self::PRESENCE_VIRTUELLE;
    }

    /**
     * Presence Elearning
     * @return bool
     */
    public function isElearning() {
        return $this->getPresence() === self::PRESENCE_ELEARNING;
    }

    /**
     * Presence sur site
     * @return bool
     */
    public function isSurSite() {
        return $this->getPresence() === self::PRESENCE_SITE;
    }

    /**
     * @param Collection<int, Formation> $formations
     */
    public function setFormations($formations)
    {
        $this->formations = $formations;
        return $this;
    }

    /**
     * Add formations
     *
     * @param Formation $formations
     * @return Programme
     */
    public function addFormation(Formation $formations)
    {
        $this->formations[] = $formations;
        $formations->setProgramme($this);

        return $this;
    }

    /**
     * Remove formations
     *
     * @param Formation $formations
     */
    public function removeFormation(Formation $formations)
    {
        $this->formations->removeElement($formations);
    }

    /**
     * Get formations
     *
     * @return Collection
     */
    public function getFormations()
    {
        return $this->formations->matching(Criteria::create()->where(Criteria::expr()->eq('archived', false)));
    }

    /**
     * Get formations
     *
     * @return Collection
     */
    public function getAllFormations()
    {
        return $this->formations;
    }

    /**
     * @return \DateTime
     */
    public function getEvaluationParticipantAnswersCount()
    {
        return $this->evaluationParticipantAnswersCount;
    }

    /**
     * @param \DateTime $evaluationParticipantAnswersCount
     * @return Programme
     */
    public function setEvaluationParticipantAnswersCount($evaluationParticipantAnswersCount)
    {
        $this->evaluationParticipantAnswersCount = $evaluationParticipantAnswersCount;
        return $this;
    }

    /**
     * @return \DateTime
     */
    public function getEvaluationParticipantAnswersSum()
    {
        return $this->evaluationParticipantAnswersSum;
    }

    /**
     * @param \DateTime $evaluationParticipantAnswersSum
     * @return Programme
     */
    public function setEvaluationParticipantAnswersSum($evaluationParticipantAnswersSum)
    {
        $this->evaluationParticipantAnswersSum = $evaluationParticipantAnswersSum;
        return $this;
    }

    /**
     * @return \DateTime
     */
    public function getEvaluationFormerAnswersCount()
    {
        return $this->evaluationFormerAnswersCount;
    }

    /**
     * @param \DateTime $evaluationFormerAnswersCount
     * @return Programme
     */
    public function setEvaluationFormerAnswersCount($evaluationFormerAnswersCount)
    {
        $this->evaluationFormerAnswersCount = $evaluationFormerAnswersCount;
        return $this;
    }

    /**
     * @return \DateTime
     */
    public function getEvaluationFormerAnswersSum()
    {
        return $this->evaluationFormerAnswersSum;
    }

    /**
     * @param \DateTime $evaluationFormerAnswersSum
     * @return Programme
     */
    public function setEvaluationFormerAnswersSum($evaluationFormerAnswersSum)
    {
        $this->evaluationFormerAnswersSum = $evaluationFormerAnswersSum;
        return $this;
    }

    /**
     * Add unity
     *
     * @param UnityFormation $unity
     * @return Programme
     */
    public function addUnity(UnityFormation $unity)
    {
        $this->unities[] = $unity;
        $unity->setProgramme($this);

        return $this;
    }

    /**
     * Remove unity
     *
     * @param UnityFormation $unity
     */
    public function removeUnity(UnityFormation $unity)
    {
        $this->unities->removeElement($unity);
    }

    /**
     * Get unities
     *
     * @return Collection
     */
    public function getUnities()
    {
        return $this->unities;
    }

    /**
     * @return false|UnityFormation
     */
    public function getUnityByPosition ($position)
    {
        return isset($this->getUnities()->toArray()[$position - 1]) ? $this->getUnities()->toArray()[$position - 1] : false;
    }

    public function getLastUnity(): ?UnityFormation
    {
        return $this->getUnities()->count() ? $this->getUnities()->last() : null;
    }

    public function isRemovable()
    {
        return array_reduce($this->getFormations()->toArray(), function($isRemovable, Formation $formation) {
            return $isRemovable = $isRemovable && $formation->getParticipations()->isEmpty();
        }, true);
    }

    /**
     * @return int
     */
    public function getCaTotal()
    {
        if (!$this->getFormations()) {
            return 0;
        }
        return array_reduce(
            $this->getFormations()->map(function(Formation $f) { return $f->getCaTotal();})->toArray(),
            function($carry, $item) {
                $carry += $item;
                return $carry;
            },
            0
        );
    }

    // /**
    //  * @return int
    //  */
    // public function getCaTotalByCoordinator(Person $person)
    // {
    //     if (!$this->getFormations()) {
    //         return 0;
    //     }

    //     if (count($this->getCoordinators()) > 1) {
    //         return array_reduce(
    //             $this->getFormations()->map(function(Formation $f) use ($person) { return $f->getCaTotalByCoordinator($person);})->toArray()
    //             ,
    //             function($carry, $item) {
    //                 $carry += $item;
    //                 return $carry;
    //             },
    //             0
    //         );
    //     }
    //     else {
    //         return array_reduce(
    //             $this->getFormations()->map(function(Formation $f) { return $f->getCaTotal();})->toArray()
    //             ,
    //             function($carry, $item) {
    //                 $carry += $item;
    //                 return $carry;
    //             },
    //             0
    //         );
    //     }
    // }

    /**
     * @return int
     */
    public function getParticipantCount()
    {
        if (!$this->getFormations()) {
            return 0;
        }
        return array_reduce(
            $this->getFormations()->map(function(Formation $f) { return $f->getParticipantCount();})->toArray(),
            function($carry, $item) {
                $carry += $item;
                return $carry;
            },
            0
        );
    }

    /**
     * @return Collection|Person[]
     */
    public function getFormateursPersons()
    {
        return $this->getFormateurs()->map(function($f) {
            /** @var Formateur $f */
            return $f->getPerson();
        });
    }

    // /**
    //  * @return \Doctrine\Common\Collections\Collection|Person[]
    //  */
    // public function getCoordinatorsPerson()
    // {
    //     return $this->getCoordinators()->map(function($c) {
    //         /** @var Coordinator $c */
    //         return $c->getPerson();
    //     });
    // }

    public function getParticipations()
    {
        if (!$this->getFormations()) {
            return [];
        }
        $participations = $this->getFormations()->map(function(Formation $f) {
            $participationsFormation = $f->getParticipations();
            if($participationsFormation) {
                return $participationsFormation->toArray();
            }
            return array();
        })->toArray();

        return array_reduce(
            $participations,
            function($carry, $item) {
                $carry = array_merge($carry, $item);
                return $carry;
            },
            array()
        );
    }

    public function getParticipationsByCoordinator(Person $coordinator)
    {
        if (!$this->getFormations()) {
            return [];
        }

        if(count($this->getCoordinators()) > 1) {
            $participations = $this->getFormations()->map(function(Formation $f) use ($coordinator) { return $f->getParticipationsByCoordinator($coordinator)->toArray();})->toArray();
        }
        else {
            $participations = $this->getFormations()->map(function(Formation $f) { return $f->getParticipations()->toArray();})->toArray();
        }

        return array_reduce(
            $participations,
            function($carry, $item) {
                $carry = array_merge($carry, $item);
                return $carry;
            },
            array()
        );
    }

    // public function getCoordinatorname() {
    //     $coordinatorsName = array();
    //     if (!is_null($this->getCoordinators())) {
    //         foreach($this->getCoordinators() as $coordinator) {
    //             $coordinatorsName[] = $coordinator->getPerson()->getInvertedFullname();
    //         }
    //         return $coordinatorsName;
    //     }
    //     return null;
    // }

    // public function getSupervisorName() {
    //     $supervisorsName = array();
    //     if (!is_null($this->getCoordinators())) {
    //         foreach($this->getCoordinators() as $coordinator) {
    //             if($coordinator->getPerson()->getSupervisor()) {
    //                 $supervisorsName[] = $coordinator->getPerson()->getSupervisor()->getInvertedFullname();
    //             }
    //         }
    //         return $supervisorsName;
    //     }
    //     return null;
    // }

    // /**
    //  * Somme des honoraires des formateurs pour chaque coordinateur
    //  * @return int|number
    //  */
    // public function getFormerHonorary() {
    //     if (!$this->)) {
    //         return null;
    //     }
    //     $values = $this->)->map(function (Formateur $f) {
    //         return $f->getHonorary() / count($this->getCoordinators());
    //     })->toArray();

    //     if (count(array_unique($values)) === 1 && is_null($values[0])) {
    //         return null;
    //     }

    //     return array_sum($values);
    // }

    // /**
    //  * Somme des honoraires des formateurs pour le programme
    //  * @return int|number
    //  */
    // public function getFormersHonorary() {
    //     if (!$this->getCoordinators()) {
    //         return $this->getOriginalFormersHonorary();
    //     }

    //     $values = $this->getCoordinators()->map(function(Coordinator $c) {
    //         return $c->getSurchargedCostFormers();
    //     })->toArray();

    //     if (count(array_unique($values)) === 1 && is_null($values[0])) {
    //         return null;
    //     }

    //     return array_sum($values);
    // }

    // public function getOriginalFormersHonorary() {
    //     if (!$this->)) {
    //         return null;
    //     }
    //     $values = $this->)->map(function (Formateur $f) {
    //         return $f->getHonorary();
    //     })->toArray();

    //     if (count(array_unique($values)) === 1 && is_null($values[0])) {
    //         return null;
    //     }

    //     return array_sum($values);
    // }

    // /**
    //  * @return bool
    //  */
    // public function isCoordinatorLbi()
    // {
    //     if ($this->getCoordinators()) {
    //         foreach($this->getCoordinators() as $coordinator) {
    //             if($coordinator->getPerson()->isCoordinatorLbi()) {
    //                 return true;
    //             }
    //         }
    //     }
    //     return false;
    // }

    // public function isOneDayLong()
    // {
    //     return $this->getStartDate()->format('Y-m-d') === $this->getEndDate()->format('Y-m-d');
    // }

    // /**
    //  * Somme des honoraires des coordinateurs
    //  * @return int|number
    //  */
    // public function getCoordinatorHonorary() {
    //     if (!$this->getCoordinators()) {
    //         return null;
    //     }
    //     $values = $this->getCoordinators()->map(function(Coordinator $c) {
    //         return $c->getHonorary();
    //     })->toArray();

    //     if (count(array_unique($values)) === 1 && is_null($values[0])) {
    //         return null;
    //     }

    //     return array_sum($values);
    // }

    // /**
    //  * Somme des honoraires des coordinateurs
    //  * @return int|number
    //  */
    // public function getCoordinatorCalculatedHonorary() {
    //     if (!$this->getCoordinators()) {
    //         return null;
    //     }
    //     $values = $this->getCoordinators()->map(function(Coordinator $c) {
    //         return $c->getCalculatedHonorary();
    //     })->toArray();

    //     if (count(array_unique($values)) === 1 && is_null($values[0])) {
    //         return null;
    //     }

    //     return array_sum($values);
    // }

    // /**
    //  * Somme des commission théoriques des coordinateurs
    //  * @return int|number
    //  */
    // public function getCoordinatorCommissionTheorique() {
    //     if (!$this->getCoordinators()) {
    //         return null;
    //     }
    //     $values = $this->getCoordinators()->map(function(Coordinator $c) {
    //         return $c->getCommissionTheorique();
    //     })->toArray();

    //     if (count(array_unique($values)) === 1 && is_null($values[0])) {
    //         return null;
    //     }

    //     return array_sum($values);
    // }

    // /**
    //  * Somme des honoraires des coordinateurs
    //  * @return int|number
    //  */
    // public function getCoordinatorHonoraryByCoordinator(Person $coordinator) {
    //     if (!$this->getCoordinators()) {
    //         return null;
    //     }
    //     return array_sum($this->getCoordinators()->map(function(Coordinator $c) use ($coordinator) {
    //         if ($c->getPerson() == $coordinator) {
    //             if (!$c->getHonorary()) {
    //                 if (count($this->getCoordinators()) > 1) {
    //                     foreach($this->getFormations() as $formation) {
    //                         foreach ($formation as $participations) {
    //                             if($participations->getCoordinator() && $participation->getCoordinator()->getId() != $c->getId()) {
    //                                 return $participation->getBudgetCR();
    //                             }
    //                         }
    //                     }
    //                 } else {
    //                     foreach($this->getFormations() as $formation) {
    //                         foreach ($formation as $participations) {
    //                             return $participation->getBudgetCR();
    //                         }
    //                     }
    //                 }
    //             } else {
    //                 return $c->getHonorary();
    //             }
    //         }

    //     })->toArray());
    // }

    // /**
    //  * Somme des honoraires de restauration
    //  * @return int|number
    //  */
    // public function getRestaurationHonorary() {
    //     if (!$this->getCoordinators()) {
    //         return null;
    //     }
    //     $values = $this->getCoordinators()->map(function (Coordinator $c) {
    //         return $c->getRestaurationHonorary();
    //     })->toArray();

    //     if (count(array_unique($values)) === 1 && is_null($values[0])) {
    //         return null;
    //     }

    //     return array_sum($values);
    // }

    // /**
    //  * Somme des avances formations
    //  * @return int|number
    //  */
    // public function getAvancesCost() {
    //     if (!$this->getCoordinators()) {
    //         return null;
    //     }
    //     $values = $this->getCoordinators()->map(function (Coordinator $c) {
    //         return $c->getAvancesCost();
    //     })->toArray();

    //     if (count(array_unique($values)) === 1 && is_null($values[0])) {
    //         return null;
    //     }

    //     return array_sum($values);
    // }

    // /**
    //  * Somme des honoraires de restauration
    //  * @return int|number
    //  */
    // public function getRestaurationHonoraryByCoordinator(Person $coordinator) {
    //     if (!$this->getCoordinators()) {
    //         return null;
    //     }
    //     $values = $this->getCoordinators()->map(function (Coordinator $c) use ($coordinator) {
    //         if ($c->getPerson() == $coordinator) {
    //             return $c->getRestaurationHonorary();
    //         }
    //     })->toArray();
    //     if (count(array_unique($values)) === 1 && is_null($values[0])) {
    //         return null;
    //     }
    //     return array_sum($values);
    // }


    // /**
    //  * Cout de la formation = cout coordinateur + couts orateurs + cout restaurant
    //  * @return float
    //  */
    // public function getCost() {
    //     return $this->getCoordinatorHonorary() + $this->getFormersHonorary() + $this->getRestaurationHonorary();
    // }

    // public function getFormationCost() {
    //     return $this->getFormersHonorary() + $this->getPhysicalFormationCost();
    // }

    // public function getPhysicalFormationCost() {

    //     if (!$this->getCoordinators()) {
    //         return $this->getCostBadges() + $this->getCostKilometres() + $this->getCostMateriel() + $this->getCostRetrocessions() + $this->getCostDivers();
    //     }
    //     $values = $this->getCoordinators()->map(function (Coordinator $c) {
    //         return $c->getPhysicalFormationCost();
    //     })->toArray();

    //     if (count(array_unique($values)) === 1 && is_null($values[0])) {
    //         return null;
    //     }

    //     return array_sum($values);
    // }

    /**
     * On considère la formation closed quand au moins une des sessions est closed
     * @return bool|mixed
     */
    public function getClosed() {
        if (!$this->getFormations()) {
            return false;
        }
        return array_reduce(
            $this->getFormations()->map(function(Formation $f) { return $f->getClosed(); })->toArray(),
            function($isClosed, $closed) {
                return $isClosed || $closed;
            },
            false
        );
    }

    /**
     * @return bool|mixed
     */
    public function getClosedAndpc() {
        if (!$this->getFormations()) {
            return false;
        }
        return array_reduce(
            $this->getFormations()->map(function(Formation $f) { return $f->getClosedAndpc(); })->toArray(),
            function($isClosedAndpc, $closedAndpc) {
                return $isClosedAndpc || $closedAndpc;
            },
            false
        );
    }

    // /**
    //  * @return string|null
    //  * @throws \Exception
    //  */
    // public function getStatus() {
    //     if ($this->getClosed()) {
    //         return Formation::STATUS_CLOSED;
    //     } else if ($this->getClosedAndpc()) {
    //         return Formation::STATUS_CLOSED_ANDPC;
    //     } else {
    //         $now = new \DateTime();
    //         $start = clone $this->getStartDate();
    //         $start->setTime(0, 0, 0);
    //         if ($start <= $now) {
    //             return Formation::STATUS_OPENED;
    //         } else if ($this->getStartDate() > $now) {
    //             return Formation::STATUS_FUTURE;
    //         }
    //     }
    //     return null;
    // }
    /**
     * @return Formation|null
     */
    public function getPrimaryFormation() {
        $primaryFormation = null;
        if ($formations = $this->getFormations()) {
            /** @var Formation $formation */
            foreach ($formations as $formation) {
                $primaryFormation = $formation;
                if ($formation->isCms() || $formation->isAudit()) {
                    break;
                }
            }
        }
        return $primaryFormation;
    }

    public function getDiscr() {
        if ($primaryFormation = $this->getPrimaryFormation()) {
            return $primaryFormation->getDiscr();
        }
        return null;
    }

    public function getTypeLabel() {
        if ($primaryFormation = $this->getPrimaryFormation()) {
            return $primaryFormation->getTypeLabel();
        }
        return null;
    }

    public function getGroup() {
        if ($primaryFormation = $this->getPrimaryFormation()) {
            return $primaryFormation->getGroup();
        }
        return null;
    }

    public function getElasticIndex(): string
    {
        return self::ELASTIC_INDEX;
    }

    // /**
    //  * @Assert\Callback
    //  */
    // public function checkCoordinator(ExecutionContextInterface $context)
    // {
    //     if(count($this->getCoordinators()) < 1) {
    //         $context->buildViolation('Veuillez ajouter un coordinateur')
    //             ->atPath('coordinators')
    //             ->addViolation();
    //     }

    //     $coordinators = $this->getCoordinators() instanceof Collection ? $this->getCoordinators()->toArray() : $this->getCoordinators();

    //     if (count($this->getCoordinators()) > 0) {
    //         $coordinatorIds = array_map(function(Coordinator $coordinator) {
    //             return $coordinator->getPerson()->getId();
    //         }, $coordinators);

    //         if (count($coordinatorIds) !== count(array_unique($coordinatorIds))) {
    //             $context->buildViolation('Veuillez supprimer les coordinateurs qui apparaissent en double')
    //                 ->atPath('coordinators')
    //                 ->addViolation();
    //         }
    //     }

    //     if(count($this->)) > 0) {
    //         $formers = $this->) instanceof Collection ? $this->)->toArray() : $this->);

    //         foreach ($formers as $formateur) {
    //             if (!$formateur->getPerson() instanceof Person) {
    //                 $context->buildViolation('Veuillez sélectionner un formateur')
    //                     ->atPath('formateurs')
    //                     ->addViolation();;
    //             }
    //         }

    //         $formers = array_filter($formers, function (Formateur $formateur) {
    //             return $formateur->getPerson() instanceof Person;
    //         });

    //         $formerIds = array_map(function (Formateur $formateur) {
    //             return $formateur->getPerson()->getId();
    //         }, $formers);

    //         if (count($formerIds) !== count(array_unique($formerIds))) {
    //             $context->buildViolation('Veuillez supprimer les formateurs qui apparaissent en double')
    //                 ->atPath('formateurs')
    //                 ->addViolation();
    //         }
    //     }

    // }

    public function getSortedFormations()
    {
        $criteria = Criteria::create()
          ->orderBy(array("openingDate" => Order::Ascending));

        return $this->formations->matching($criteria);
    }

    /**
     * @return string
     */
    public function getToken() {
        return md5(sprintf('%s%s',
            $this->getId(), $this->getCreatedAt()->format('Y-m-d')
        ));
    }

    public function getFullTitle() {
        return sprintf("%s - %s", $this->reference, $this->title);
    }

    /**
     * Add connaissances
     *
     * @param Connaissance $connaissances
     * @return Programme
     */
    public function addConnaissance(Connaissance $connaissances)
    {
        $this->connaissances[] = $connaissances;
        $connaissances->setProgramme($this);
        return $this;
    }

    /**
     * Remove connaissances
     *
     * @param Connaissance $connaissances
     */
    public function removeConnaissance(Connaissance $connaissances)
    {
        $this->connaissances->removeElement($connaissances);
    }

    /**
     * Get connaissances
     *
     * @return Collection
     */
    public function getConnaissances()
    {
        return $this->connaissances;
    }

    /**
     * Add competences
     *
     * @param Competence $competences
     * @return Programme
     */
    public function addCompetence(Competence $competences)
    {
        $this->competences[] = $competences;
        $competences->setProgramme($this);
        return $this;
    }

    /**
     * Remove competences
     *
     * @param Competence $competences
     */
    public function removeCompetence(Competence $competences)
    {
        $this->competences->removeElement($competences);
    }

    /**
     * Get competences
     *
     * @return Collection
     */
    public function getCompetences()
    {
        return $this->competences;
    }

    /*** GETTERS A SUPPRIMER ***/

    public function getMessageRappel(): ?string
    {
        return $this->messageRappel;
    }

    public function setMessageRappel(?string $messageRappel): self
    {
        $this->messageRappel = $messageRappel;

        return $this;
    }

    public function getAutocompleteReference(): ?bool
    {
        return $this->autocompleteReference;
    }

    public function setAutocompleteReference(?bool $autocompleteReference): self
    {
        $this->autocompleteReference = $autocompleteReference;

        return $this;
    }

    public function getAddress(): ?string
    {
        return $this->address;
    }

    public function setAddress(?string $address): self
    {
        $this->address = $address;

        return $this;
    }

    public function getAddress2(): ?string
    {
        return $this->address2;
    }

    public function setAddress2(?string $address2): self
    {
        $this->address2 = $address2;

        return $this;
    }

    public function getCity(): ?string
    {
        return $this->city;
    }

    public function setCity(?string $city): self
    {
        $this->city = $city;

        return $this;
    }

    public function getZipCode(): ?string
    {
        return $this->zipCode;
    }

    public function setZipCode(?string $zipCode): self
    {
        $this->zipCode = $zipCode;

        return $this;
    }

    public function getDuration(): ?float
    {
        return $this->duration;
    }

    public function setDuration(?float $duration): self
    {
        $this->duration = $duration;

        return $this;
    }

    public function getStartDate(): ?\DateTimeInterface
    {
        return $this->startDate;
    }

    public function setStartDate(?\DateTimeInterface $startDate): self
    {
        $this->startDate = $startDate;

        return $this;
    }

    public function getEndDate(): ?\DateTimeInterface
    {
        return $this->endDate;
    }

    public function setEndDate(?\DateTimeInterface $endDate): self
    {
        $this->endDate = $endDate;

        return $this;
    }

    // public function getEvaluationParticipantAnswersCount(): ?int
    // {
    //     return $this->evaluationParticipantAnswersCount;
    // }

    // public function setEvaluationParticipantAnswersCount(?int $evaluationParticipantAnswersCount): self
    // {
    //     $this->evaluationParticipantAnswersCount = $evaluationParticipantAnswersCount;

    //     return $this;
    // }

    // public function getEvaluationParticipantAnswersSum(): ?int
    // {
    //     return $this->evaluationParticipantAnswersSum;
    // }

    // public function setEvaluationParticipantAnswersSum(?int $evaluationParticipantAnswersSum): self
    // {
    //     $this->evaluationParticipantAnswersSum = $evaluationParticipantAnswersSum;

    //     return $this;
    // }

    // public function getEvaluationFormerAnswersCount(): ?int
    // {
    //     return $this->evaluationFormerAnswersCount;
    // }

    // public function setEvaluationFormerAnswersCount(?int $evaluationFormerAnswersCount): self
    // {
    //     $this->evaluationFormerAnswersCount = $evaluationFormerAnswersCount;

    //     return $this;
    // }

    // public function getEvaluationFormerAnswersSum(): ?int
    // {
    //     return $this->evaluationFormerAnswersSum;
    // }

    // public function setEvaluationFormerAnswersSum(?int $evaluationFormerAnswersSum): self
    // {
    //     $this->evaluationFormerAnswersSum = $evaluationFormerAnswersSum;

    //     return $this;
    // }

    public function getCostKilometres(): ?float
    {
        return $this->costKilometres;
    }

    public function setCostKilometres(?float $costKilometres): self
    {
        $this->costKilometres = $costKilometres;

        return $this;
    }

    public function getCostBadges(): ?float
    {
        return $this->costBadges;
    }

    public function setCostBadges(?float $costBadges): self
    {
        $this->costBadges = $costBadges;

        return $this;
    }

    public function getCostRetrocessions(): ?float
    {
        return $this->costRetrocessions;
    }

    public function setCostRetrocessions(?float $costRetrocessions): self
    {
        $this->costRetrocessions = $costRetrocessions;

        return $this;
    }

    public function getCostMateriel(): ?float
    {
        return $this->costMateriel;
    }

    public function setCostMateriel(?float $costMateriel): self
    {
        $this->costMateriel = $costMateriel;

        return $this;
    }

    public function getCostDivers(): ?float
    {
        return $this->costDivers;
    }

    public function setCostDivers(?float $costDivers): self
    {
        $this->costDivers = $costDivers;

        return $this;
    }

    public function getRegion(): ?string
    {
        return $this->region;
    }

    public function setRegion(?string $region): self
    {
        $this->region = $region;

        return $this;
    }

    public function getLatitude(): ?string
    {
        return $this->latitude;
    }

    public function setLatitude(?string $latitude): self
    {
        $this->latitude = $latitude;

        return $this;
    }

    public function getLongitude(): ?string
    {
        return $this->longitude;
    }

    public function setLongitude(?string $longitude): self
    {
        $this->longitude = $longitude;

        return $this;
    }

    /**
     * @return string
     */
    public function getAndpcStatus()
    {
        return $this->andpcStatus;
    }

    /**
     * @param string $andpcStatus
     * @return Programme
     */
    public function setAndpcStatus($andpcStatus)
    {
        $this->andpcStatus = $andpcStatus;
        return $this;
    }

    /**
     * @return string
     */
    public function getMethod()
    {
        return $this->method;
    }

    /**
     * @param string $method
     * @return Programme
     */
    public function setMethod($method)
    {
        $this->method = $method;
        return $this;
    }

    public function isEpp() : bool
    {
        return $this->method === self::METHOD_EPP;
    }

    /**
     * @return string
     */
    public function getCommentaireNotif()
    {
        return $this->commentaireNotif;
    }

    /**
     * @param string $commentaireNotif
     * @return Programme
     */
    public function setCommentaireNotif($commentaireNotif)
    {
        $this->commentaireNotif = $commentaireNotif;
        return $this;
    }


    /**
     * @return Collection|Formateur[]
     */
    public function getFormateurs(): Collection
    {
        return $this->formateurs;
    }

    public function addFormateur(Formateur $formateur): self
    {
        if (!$this->formateurs->contains($formateur)) {
            $this->formateurs[] = $formateur;
            $formateur->setProgramme($this);
        }

        return $this;
    }

    public function removeFormateur(Formateur $formateur): self
    {
        if ($this->formateurs->contains($formateur)) {
            $this->formateurs->removeElement($formateur);
            // set the owning side to null (unless already changed)
            if ($formateur->getProgramme() === $this) {
                $formateur->setProgramme(null);
            }
        }

        return $this;
    }

    /**
     * @return Collection|Coordinator[]
     */
    public function getCoordinators(): Collection
    {
        return $this->coordinators;
    }

    public function addCoordinator(Coordinator $coordinator): self
    {
        if (!$this->coordinators->contains($coordinator)) {
            $this->coordinators[] = $coordinator;
            $coordinator->setProgramme($this);
        }

        return $this;
    }

    public function removeCoordinator(Coordinator $coordinator): self
    {
        if ($this->coordinators->contains($coordinator)) {
            $this->coordinators->removeElement($coordinator);
            // set the owning side to null (unless already changed)
            if ($coordinator->getProgramme() === $this) {
                $coordinator->setProgramme(null);
            }
        }

        return $this;
    }

    /**
     * @return Collection|EvaluationGlobalAnswer[]
     */
    public function getEvaluationAnswers(): Collection
    {
        return $this->evaluationAnswers;
    }

    public function addEvaluationAnswer(EvaluationGlobalAnswer $evaluationAnswer): self
    {
        if (!$this->evaluationAnswers->contains($evaluationAnswer)) {
            $this->evaluationAnswers[] = $evaluationAnswer;
            $evaluationAnswer->setProgramme($this);
        }

        return $this;
    }

    public function removeEvaluationAnswer(EvaluationGlobalAnswer $evaluationAnswer): self
    {
        if ($this->evaluationAnswers->contains($evaluationAnswer)) {
            $this->evaluationAnswers->removeElement($evaluationAnswer);
            // set the owning side to null (unless already changed)
            if ($evaluationAnswer->getProgramme() === $this) {
                $evaluationAnswer->setProgramme(null);
            }
        }

        return $this;
    }

    /**
     * Add topoProgrammeFiles
     *
     * @param TopoProgrammeFiles $topoProgrammeFiles
     * @return Formation
     */
    public function addTopoProgrammeFiles(TopoProgrammeFiles $topoProgrammeFiles)
    {
        $this->topoProgrammeFiles[] = $topoProgrammeFiles;

        return $this;
    }

    /**
     * Remove topoProgrammeFiles
     *
     * @param TopoProgrammeFiles $topoProgrammeFiles
     */
    public function removeTopoProgrammeFile(TopoProgrammeFiles $topoProgrammeFiles)
    {
        $this->topoProgrammeFiles->removeElement($topoProgrammeFiles);
    }

    /**
     * Get topoProgrammeFiles
     *
     * @return TopoProgrammeFiles[]|Collection
     */
    public function getTopoProgrammeFiles()
    {
        return $this->topoProgrammeFiles;
    }

    /**
     * Get first topoProgrammeFiles
     *
     * @return TopoProgrammeFiles
     */
    public function getFirstTopoFile()
    {
        foreach($this->getTopoProgrammeFiles() as $topoProgrammeFile) {
            return $topoProgrammeFile;
        }
        return false;
    }

    public function addTopoProgrammeFile(TopoProgrammeFiles $topoProgrammeFile): self
    {
        if (!$this->topoProgrammeFiles->contains($topoProgrammeFile)) {
            $this->topoProgrammeFiles[] = $topoProgrammeFile;
            $topoProgrammeFile->setProgramme($this);
        }
        return $this;
    }

    /**
     * Add toolProgrammeFiles
     *
     * @param ToolProgrammeFiles $toolProgrammeFiles
     * @return Formation
     */
    public function addToolProgrammeFiles(ToolProgrammeFiles $toolProgrammeFiles)
    {
        $this->toolProgrammeFiles[] = $toolProgrammeFiles;

        return $this;
    }

    /**
     * Remove toolProgrammeFiles
     *
     * @param ToolProgrammeFiles $toolProgrammeFiles
     */
    public function removeToolProgrammeFile(ToolProgrammeFiles $toolProgrammeFiles)
    {
        $this->toolProgrammeFiles->removeElement($toolProgrammeFiles);
    }

    /**
     * Get toolProgrammeFiles
     *
     * @return ToolProgrammeFiles[]|Collection
     */
    public function getToolProgrammeFiles()
    {
        return $this->toolProgrammeFiles;
    }

    /**
     * Get first toolProgrammeFiles
     *
     * @return ToolProgrammeFiles
     */
    public function getFirstToolFile()
    {
        foreach($this->getToolProgrammeFiles() as $toolProgrammeFile) {
            return $toolProgrammeFile;
        }
        return false;
    }

    public function addToolProgrammeFile(ToolProgrammeFiles $toolProgrammeFile): self
    {
        if (!$this->toolProgrammeFiles->contains($toolProgrammeFile)) {
            $this->toolProgrammeFiles[] = $toolProgrammeFile;
            $toolProgrammeFile->setProgramme($this);
        }
        return $this;
    }

    /**
     * Add documentsPedagogiquesFiles
     *
     * @param DocumentsPedagogiquesFiles $documentsPedagogiquesFiles
     * @return Formation
     */
    public function addDocumentsPedagogiquesFiles (DocumentsPedagogiquesFiles $documentsPedagogiquesFiles)
    {
        $this->documentsPedagogiquesFiles[] = $documentsPedagogiquesFiles;

        return $this;
    }

    /**
     * Remove documentsPedagogiquesFiles
     *
     * @param DocumentsPedagogiquesFiles $documentsPedagogiquesFiles
     */
    public function removeDocumentsPedagogiquesFile(DocumentsPedagogiquesFiles $documentsPedagogiquesFiles)
    {
        $this->documentsPedagogiquesFiles->removeElement($documentsPedagogiquesFiles);
    }

    /**
     * Get documentsPedagogiquesFiles
     *
     * @return Collection<int, DocumentsPedagogiquesFiles>
     */
    public function getDocumentsPedagogiquesFiles ()
    {
        return $this->documentsPedagogiquesFiles;
    }

    public function addDocumentsPedagogiquesFile(DocumentsPedagogiquesFiles $toolProgrammeFile): self
    {
        if (!$this->documentsPedagogiquesFiles->contains($toolProgrammeFile)) {
            $this->documentsPedagogiquesFiles[] = $toolProgrammeFile;
            $toolProgrammeFile->setProgramme($this);
        }
        return $this;
    }

    public function updateDurations() {
        if ($this->getYear() < 2023) {
            return;
        }
        $durationPresentielle = null;
        $durationNotPresentielle = null;
        $thereIsOfflineHours = false;
        foreach ($this->getUnities() as $unity) {
            if ($unity->isElearning()) {
                $durationNotPresentielle += $unity->getNbHoursConnected() + $unity->getNbHoursOffline();
                $thereIsOfflineHours = $unity->getNbHoursOffline() > 0 ? true : $thereIsOfflineHours;
            } else {
                $durationPresentielle += $unity->getNbHours();
            }
        }
        $this->setDurationPresentielle($durationPresentielle == 0 ? null : $durationPresentielle);
        $this->setDurationNotPresentielle($durationNotPresentielle == 0 ? null : $durationNotPresentielle);
        $this->setThereIsOfflineHours($thereIsOfflineHours);
    }

    /**
     * @return false|int
     * Retourne la position la première unité sur site ou classe virtuelle
     */
    public function getReunionUnityPosition() {
        foreach($this->unities as $index => $unity) {
            if ($unity->isOnsite() || $unity->isVirtuelle()) {
                return $index + 1;
            }
        }
        return false;
    }

    public function isOneUnity(): bool
    {
        return $this->getNbUnities() == 1;
    }

    public function isThreeUnity(): bool
    {
        return $this->getNbUnities() == 3;
    }

    public function isElearningOneUnity() {
        if ($this->isOneUnity()) {
            return $this->getUnityByPosition(1)->isElearning();
        }
        return false;
    }

    public function isElearningTwoUnity() {
        return (
            $this->getSessionType() == Formation::TYPE_ELEARNING &&
            $this->isSurSite() &&
            (
                $this->getNbUnities() == 2 ||
                ($this->getNbUnities() == 3 && $this->isFullFc())
            )
        );
    }

    public function isElearningThreeUnity() {
        return $this->getSessionType() == Formation::TYPE_ELEARNING && $this->isThreeUnity();
    }

    public function isVfc() {
        return $this->sessionType === Formation::TYPE_VFC;
    }

    public function isTcs() {
        return $this->sessionType === Formation::TYPE_TCS;
    }

    public function isFullFc()
    {
        foreach($this->getUnities() as $unity) {
            if(!$unity->isFc()) {
                return false;
            }
        }
        return true;
    }

    #[Assert\Callback]
    public function checkProgramme(ExecutionContextInterface $context)
    {
        if ($this->isOneUnity()) {
            $unity = $this->getUnityByPosition(1);
            if ($unity->isElearning() && !$unity->isFC()) {
                $context->buildViolation("Une formation composée d'une seule unité de présence E-Learning ne peut pas avoir une méthode différente de Formation Continue")
                    ->atPath('unities')
                    ->addViolation();
            }
        }
    }

    #[Assert\Callback]
    public function checkTwoUnities(ExecutionContextInterface $context): void
    {
        if ($this->getNbUnities() == 2) {
            if ($this->sessionType != Formation::TYPE_ELEARNING) {
                $context->buildViolation("Une formation composée de 2 unités doit avoir un type de session E-Learning.")
                    ->atPath('sessionType')
                    ->addViolation();

            }
            if (!$this->isSurSite()) {
                $context->buildViolation("Une formation composée de 2 unités doit être de présence sur site.")
                    ->atPath('presence')
                    ->addViolation();
            }
        }
    }

    public function hasEppInUnityFormation(): bool
    {
        return $this->unities && $this->unities->filter(function ($elem) {
            return $elem->isEPP();
        })->count();
    }

    public function getProgrammesAssocies(): ?ProgrammesAssocies
    {
        return $this->programmesAssocies;
    }

    public function setProgrammesAssocies(?ProgrammesAssocies $programmesAssocies): Programme
    {
        $this->programmesAssocies = $programmesAssocies;
        return $this;
    }


    /*** FIN GETTERS A SUPPRIMER ***/

    public function allowPresciptionVaccin(): bool
    {
     return in_array($this->reference, self::REFERENCES_VACCIN);
    }

    public function allowAdministrationVaccin(): bool
    {
        return in_array($this->reference, self::REFERENCES_ADMINISTRATION_VACCIN);
    }

    public function allowAdministrationSousResponsabiliteVaccin(): bool
    {
        return in_array($this->reference, self::REFERENCES_VACCIN_SOUS_RESPONSABILITE);
    }

    public function isFCAngineComplete(): bool
    {
        return in_array($this->reference, self::REFERENCES_FC_ANGINE_COMPLETE);
    }

    public function isFCCystiteComplete(): bool
    {
        return in_array($this->reference, self::REFERENCES_FC_CYSTITE_COMPLETE);
    }

    public function isFCAngineSansTrod(): bool
    {
        return in_array($this->reference, self::REFERENCE_FC_ANGINE_SANS_TROD);
    }

    public function getExportNbVotants(): ?int
    {
        return $this->exportNbVotants;
    }

    public function setExportNbVotants(?int $exportNbVotants): Programme
    {
        $this->exportNbVotants = $exportNbVotants;
        return $this;
    }

    public function getExportNote(): ?float
    {
        return $this->exportNote;
    }

    public function setExportNote(?float $exportNote): Programme
    {
        $this->exportNote = $exportNote;
        return $this;
    }

    public function getExportNbParticipants(): ?int
    {
        return $this->exportNbParticipants;
    }

    public function setExportNbParticipants(?int $exportNbParticipants): Programme
    {
        $this->exportNbParticipants = $exportNbParticipants;
        return $this;
    }

    public function getLinkVideoModule1(): string
    {
        return $this->linkVideoModule1 ?? self::DEFAULT_VIDEO_MODULE1;
    }

    public function setLinkVideoModule1(string $linkVideoModule1): Programme
    {
        $this->linkVideoModule1 = $linkVideoModule1;
        return $this;
    }

    public function getNbUnities(): int
    {
        return count($this->getUnities());
    }

    public function getNbDayLastUnity(): ?int
    {
        return $this->getLastUnity() ? $this->getLastUnity()->getNbDays() : null;
    }

    public function getDelaiStartDate(): int
    {
        if ($this->isSurSite() && $this->isOneUnity()) {
            return self::DELAI_START_DATE_SITE_1_UNITY;
        }
        return 0;
    }

    public function getDelaiEndDate(): int
    {
        if (($this->isSurSite() && $this->isOneUnity())
            || ($this->isElearningTwoUnity())){
            return self::DELAI_END_DATE_EL2UNITY;
        }
        return 0;
    }

    public function disabledDatesForUnity(int $index): bool
    {
        return false;
    }

    public function disabledHoursForUnity(int $index): bool
    {
        return true;
    }

    public function nbEmargementNeeded(): int
    {
        return self::NB_FILE_EMARGEMENT;
    }
}
