<?php

namespace Eduprat\DomainBundle\Entity;

use Doctrine\DBAL\Types\Types;
use Doctrine\ORM\Mapping as ORM;

/**
 * ParticipationLog
 */
#[ORM\Entity(repositoryClass: 'Eduprat\DomainBundle\Repository\ParticipationLogRepository')]
class ParticipationLog
{
    public const ACTION_FORM_PRE = "form_presession";
    public const ACTION_FORM_POST = "form_postsession";
    public const ACTION_FORM_EVAL = "form_evaluation";
    public const ACTION_DOWNLOAD_PRERESTITUTION = "download_prerestitution";
    public const ACTION_DOWNLOAD_RESTITUTION = "download_restitution";
    public const ACTION_DOWNLOAD_TOPO = "download_topo";
    public const ACTION_DOWNLOAD_ACTION = "download_action";
    public const ACTION_DOWNLOAD_ATTESTATION = "download_attestation";
    public const ACTION_DOWNLOAD_ATTESTATION_HORAIRE = "download_attestation_horaire";
    public const ACTION_DOWNLOAD_ATTESTATION_HONNEUR = "download_attestation_honneur";
    public const ACTION_UPLOAD_ACTION = "upload_action";
    public const ACTION_UPLOAD_ATTESTATION_HONNEUR = "upload_attestation_honneur";
    public const ACTION_ELEARNING = "elearning";

    public const ACTION_CLASSE_VIRTUELLE = "classe_virtuelle";

    public const STEPS = [
        "step_1" => ["video_presession", 'documents_pedagogiques', "form_presession", "prerestitution", "etutorat_1"],
        "step_2" => ["elearning", 'classe_virtuelle'],
        "step_3" => ["video_postsession", "form_postsession", 'tool_box', "etutorat_2"],
        "step_4" => ["restitution", "fiche_action_2","synthese", "progression", "etutorat_3", "end"],
    ];

    /**
     * @var int
     */
    #[ORM\Column(name: 'id', type: Types::INTEGER)]
    #[ORM\Id]
    #[ORM\GeneratedValue(strategy: 'AUTO')]
    protected ?int $id = null;

    /**
     * @var Participation
     */
    #[ORM\ManyToOne(targetEntity: 'Eduprat\DomainBundle\Entity\Participation', inversedBy: 'participationLogs', cascade: ['persist'])]
    #[ORM\JoinColumn(name: 'participation', nullable: false, onDelete: 'CASCADE')]
    protected Participation $participation;

    /**
     * @var \DateTimeInterface
     */
    #[ORM\Column(name: 'startDate', type: Types::DATETIME_MUTABLE)]
    protected \DateTime $startDate;

    /**
     * @var \DateTimeInterface|null
     */
    #[ORM\Column(name: 'endDate', type: Types::DATETIME_MUTABLE, nullable: true)]
    protected ?\DateTime $endDate = null;

    /**
     * @var string
     */
    #[ORM\Column(name: 'action', type: Types::STRING)]
    protected string $action;

    /**
     * @var integer
     */
    #[ORM\Column(name: 'lesson', type: Types::INTEGER, nullable: true)]
    protected ?int $lesson = null;

    /**
     * @var integer
     */
    #[ORM\Column(name: 'activity', type: Types::INTEGER, nullable: true)]
    protected ?int $activity = null;

    /**
     * @var integer|null
     */
    #[ORM\Column(name: 'progression', type: Types::INTEGER, nullable: true)]
    protected ?int $progression;

    /**
     * @var string
     */
    #[ORM\Column(name: 'stringProgression', type: Types::STRING, nullable: true)]
    protected ?string $stringProgression;

    #[ORM\Column(name: 'step', type: Types::STRING, nullable:true)]
    protected ?string $step;

    /**
     * @var string
     */
    #[ORM\Column(name: 'ip', type: Types::STRING, length: 255, nullable: true)]
    private ?string $ip = null;

    public function __construct(Participation $participation, string $action)
    {
        $this->participation = $participation;
        $this->action = $action;
        $this->startDate = new \DateTime();
    }

    /**
     * Get id
     *
     * @return int
     */
    public function getId()
    {
        return $this->id;
    }

    /**
     * @return Participation
     */
    public function getParticipation(): Participation
    {
        return $this->participation;
    }

    /**
     * @param Participation $participation
     * @return ParticipationLog
     */
    public function setParticipation(Participation $participation): ParticipationLog
    {
        $this->participation = $participation;
        return $this;
    }

    /**
     * @return string
     */
    public function getAction(): string
    {
        return $this->action;
    }

    /**
     * @param string $action
     * @return ParticipationLog
     */
    public function setAction(string $action): ParticipationLog
    {
        $this->action = $action;
        return $this;
    }

    /**
     * @return \DateTime
     */
    public function getStartDate(): \DateTime
    {
        return $this->startDate;
    }

    /**
     * @param \DateTime $startDate
     * @return ParticipationLog
     */
    public function setStartDate(\DateTime $startDate): ParticipationLog
    {
        $this->startDate = $startDate;
        return $this;
    }

    /**
     * @return \DateTime
     */
    public function getEndDate(): ?\DateTime
    {
        return $this->endDate;
    }

    /**
     * @param \DateTime|null $endDate
     * @return ParticipationLog
     */
    public function setEndDate(?\DateTime $endDate): ParticipationLog
    {
        $this->endDate = $endDate;
        return $this;
    }

    /**
     * @return int
     */
    public function getProgression(): ?int
    {
        return $this->progression;
    }

    /**
     * @param int|null $progression
     * @return ParticipationLog
     */
    public function setProgression(?int $progression): ParticipationLog
    {
        $this->progression = $progression;
        return $this;
    }

    /**
     * @return string
     */
    public function getStringProgression(): ?string
    {
        return $this->stringProgression;
    }

    /**
     * @param string $StringProgression
     * @return ParticipationLog
     */
    public function setStringProgression(?string $stringProgression): ParticipationLog
    {
        $this->stringProgression = $stringProgression;
        return $this;
    }

    /**
     * @return int
     */
    public function getLesson()
    {
        return $this->lesson;
    }

    /**
     * @param int $lesson
     * @return ParticipationLog
     */
    public function setLesson(int $lesson = null): ParticipationLog
    {
        $this->lesson = $lesson;
        return $this;
    }

    /**
     * @return int
     */
    public function getActivity()
    {
        return $this->activity;
    }

    /**
     * @param int $activity
     * @return ParticipationLog
     */
    public function setActivity(int $activity = null): ParticipationLog
    {
        $this->activity = $activity;
        return $this;
    }

    /**
     * @return string
     */
    public function getStep(): ?string
    {
        return $this->step;
    }

    /**
     * @param string $step
     * @return ParticipationLog
     */
    public function setStep(?string $step): ParticipationLog
    {
        $this->step = $step;
        return $this;
    }

        /**
     * Set ip
     *
     * @param string $ip
     * @return ParticipationLog
     */
    public function setIp($ip)
    {
        $this->ip = $ip;
        return $this;
    }

    /**
     * Get ip
     *
     * @return string
     */
    public function getIp()
    {
        return $this->ip;
    }

    public function isDownloadAction()
    {
        return strpos($this->action, "load_") !== false;
    }

    public function isSurveyAction()
    {
        return in_array($this->action, array(self::ACTION_FORM_PRE, self::ACTION_FORM_POST));
    }

}

