<?php

namespace Eduprat\DomainBundle\Security;

use <PERSON><PERSON><PERSON>\AdminBundle\Entity\Person;
use Ed<PERSON>rat\DomainBundle\Entity\Participant;
use Symfony\Component\Security\Core\Authentication\Token\TokenInterface;
use Symfony\Component\Security\Core\Authorization\Voter\Voter;
use Symfony\Bundle\SecurityBundle\Security;

class ParticipantVoter extends Voter
{
    const VIEW = 'view';

    private Security $security;

    public function __construct(Security $security)
    {
        $this->security = $security;
    }

    protected function supports($attribute, $subject): bool
    {
        if (!in_array($attribute, [self::VIEW])) {
            return false;
        }

        if (!$subject instanceof Participant) {
            return false;
        }

        return true;
    }

    protected function voteOnAttribute($attribute, $subject, TokenInterface $token): bool
    {
        /** @var Person $user */
        $user = $token->getUser();

        if (!$user instanceof Person) {
            // the user must be logged in; if not, deny access
            return false;
        }

        /** @var Participant $participant */
        $participant = $subject;

        switch ($attribute) {
            case self::VIEW:
                return $this->canView($participant, $user);
        }

        throw new \LogicException('This code should not be reached!');
    }

    private function canView(Participant $participant, Person $user): bool
    {
        // Le superviseur peut voir tous les participants
        if ($this->security->isGranted('ROLE_SUPERVISOR')) {
            return true;
        }

        // Le coordinateur peut voir uniquement :
        // - les participants ceux qui lui sont associés
        // - les prospects qui sont dans ses UGA
        // - les prospects sans UGA
        if ($user->isCoordinator()) {
            return
                ($participant->getCoordinator() === $user)
                || ($participant->isProspect() && in_array($participant->getUga(), $user->getUgas()))
                || ($participant->isProspect() && $participant->getUga() === null);
        }

        return false;
    }
}
