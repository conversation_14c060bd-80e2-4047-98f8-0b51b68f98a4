<?php

namespace Eduprat\DomainBundle\Security;

use <PERSON><PERSON><PERSON>\AdminBundle\Entity\Person;
use Ed<PERSON>rat\DomainBundle\Entity\Formation;
use Symfony\Component\Security\Core\Authentication\Token\TokenInterface;
use Symfony\Component\Security\Core\Authorization\Voter\Voter;
use Symfony\Bundle\SecurityBundle\Security;

class FormationVoter extends Voter
{
    const VIEW = 'view';

    private Security $security;

    public function __construct(Security $security)
    {
        $this->security = $security;
    }

    protected function supports($attribute, $subject): bool
    {
        if (!in_array($attribute, [self::VIEW])) {
            return false;
        }

        if (!$subject instanceof Formation) {
            return false;
        }

        return true;
    }

    protected function voteOnAttribute($attribute, $subject, TokenInterface $token): bool
    {
        /** @var Person $user */
        $user = $token->getUser();

        if (!$user instanceof Person) {
            // the user must be logged in; if not, deny access
            return false;
        }

        /** @var Formation $formation */
        $formation = $subject;

        switch ($attribute) {
            case self::VIEW:
                return $this->canView($formation, $user);
        }

        throw new \LogicException('This code should not be reached!');
    }

    private function canView(Formation $formation, Person $user): bool
    {
        // Le superviseur peut voir toutes les sessions
        if ($this->security->isGranted('ROLE_SUPERVISOR')) {
            return true;
        }

        // Le coordinateur peut voir uniquement les sessions dans lequel il est présent
        if ($user->isCoordinator() && $formation->getCoordinatorsPerson()->contains($user)) {
            return true;
        }

        return false;
    }
}
