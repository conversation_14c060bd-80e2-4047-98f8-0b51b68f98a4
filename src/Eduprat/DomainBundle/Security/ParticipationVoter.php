<?php

namespace Eduprat\DomainBundle\Security;

use <PERSON><PERSON>rat\AdminBundle\Entity\Person;
use Ed<PERSON>rat\DomainBundle\Entity\Participation;
use Symfony\Component\Security\Core\Authentication\Token\TokenInterface;
use Symfony\Component\Security\Core\Authorization\Voter\Voter;
use Symfony\Bundle\SecurityBundle\Security;

class ParticipationVoter extends Voter
{
    const VIEW = 'view';
    const EDIT = 'edit';

    private Security $security;

    public function __construct(Security $security)
    {
        $this->security = $security;
    }

    protected function supports($attribute, $subject): bool
    {
        if (!in_array($attribute, [self::VIEW, self::EDIT])) {
            return false;
        }

        if (!$subject instanceof Participation) {
            return false;
        }

        return true;
    }

    protected function voteOnAttribute($attribute, $subject, TokenInterface $token): bool
    {
        /** @var Person $user */
        $user = $token->getUser();

        if (!$user instanceof Person) {
            // the user must be logged in; if not, deny access
            return false;
        }

        /** @var Participation $participation */
        $participation = $subject;

        switch ($attribute) {
            case self::VIEW:
                return $this->canView($participation, $user);
            case self::EDIT:
                return $this->canEdit($participation, $user);
        }

        throw new \LogicException('This code should not be reached!');
    }

    private function canView(Participation $participation, Person $user): bool
    {
        return $this->canEdit($participation, $user);
    }

    private function canEdit(Participation $participation, Person $user): bool
    {
        // Le superviseur peut voir toutes les participations
        if ($this->security->isGranted('ROLE_SUPERVISOR')) {
            return true;
        }

        // Le coordinateur peut voir uniquement les participations des participants qui lui sont associées
        if ($user->isCoordinator() && ($participation->getParticipant()->getCoordinator() === $user || ($participation->getCoordinator() && $participation->getCoordinator()->getPerson()) === $user)) {
            return true;
        }

        return false;
    }
}
