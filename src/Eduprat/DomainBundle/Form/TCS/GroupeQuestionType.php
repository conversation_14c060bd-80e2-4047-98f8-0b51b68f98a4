<?php

namespace Eduprat\DomainBundle\Form\TCS;

use Ed<PERSON><PERSON>\DomainBundle\DTO\GroupeQuestionTCSCreateDto;
use Symfony\Component\Form\AbstractType;
use Symfony\Component\Form\Extension\Core\Type\TextareaType;
use Symfony\Component\Form\FormBuilderInterface;
use Symfony\Component\OptionsResolver\OptionsResolver;
use Symfony\Component\Validator\Constraints\File;
use Vich\UploaderBundle\Form\Type\VichFileType;

class GroupeQuestionType extends AbstractType
{
    public function buildForm(FormBuilderInterface $builder, array $options): void
    {
        $builder
            ->add('description', TextareaType::class, array(
                'attr' => array(
                    'class' => 'tinymce',
                ),
                'label' => 'Description',
            ))
            ->add('syntheseEducative', TextareaType::class, array(
                'attr' => array(
                    'class' => 'tinymce',
                ),
                'label' => 'Synthèse éducative',
                'required' => false,
            ))
            ->add('descriptionImageFile', VichFileType::class, array(
                'label' => false,
                'required' => false,
                'download_uri' => false,
                'allow_delete' => true,
                'error_bubbling' => true,
                'constraints' => new File(
                    array(
                        'mimeTypes' => array(
                            'image/png', 'image/jpeg', 'image/gif',
                        ),
                        'maxSize' => "2048k"
                    )
                )
            ))
            ->add('syntheseEducativeImageFile', VichFileType::class, array(
                'label' => false,
                'required' => false,
                'download_uri' => false,
                'allow_delete' => true,
                'error_bubbling' => true,
                'constraints' => new File(
                    array(
                        'mimeTypes' => array(
                            'image/png', 'image/jpeg', 'image/gif',
                        ),
                        'maxSize' => "2048k"
                    )
                )
            ))
        ;
    }

    public function configureOptions(OptionsResolver $resolver): void
    {
        $resolver->setDefaults([
            'data_class' => GroupeQuestionTCSCreateDto::class,
        ]);
    }
}
