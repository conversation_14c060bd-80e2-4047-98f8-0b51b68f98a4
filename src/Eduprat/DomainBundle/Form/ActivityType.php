<?php

namespace Eduprat\DomainBundle\Form;

use Ed<PERSON>rat\DomainBundle\Entity\Activity;
use Symfony\Component\Form\AbstractType;
use Symfony\Component\Form\Extension\Core\Type\CheckboxType;
use Symfony\Component\Form\Extension\Core\Type\ChoiceType;
use Symfony\Component\Form\Extension\Core\Type\CollectionType;
use Symfony\Component\Form\Extension\Core\Type\HiddenType;
use Symfony\Component\Form\FormBuilderInterface;
use Symfony\Component\OptionsResolver\OptionsResolver;
use Vich\UploaderBundle\Form\Type\VichFileType;
use Vich\UploaderBundle\Form\Type\VichImageType;
use Symfony\Component\Validator\Constraints\File;


class ActivityType extends AbstractType
{
    /**
     * {@inheritdoc}
     */
    public function buildForm(FormBuilderInterface $builder, array $options): void
    {
        $builder
            ->add('label', null, array(
                'label' => 'admin.activity.label',
                'attr' => array(
                    'class' => 'activity-label'
                )
            ))
            ->add('position', HiddenType::class, array(
                'label' => 'admin.question.position',
                'attr' => array(
                    'class' => 'activity-position'
                )
            ))
            ->add('format', ChoiceType::class, array(
                'label' => 'admin.activity.format',
                'required' => true,
                'choices'  => self::getFormats(),
                'placeholder' => 'admin.global.select',
                'attr' => array(
                    'class' => 'format'
                )
            ))
            ->add('pictureFile', VichImageType::class, array(
                'label' => false,
                'required' => false,
                'allow_delete' => true,
                'download_uri' => false,
                'translation_domain' => 'messages',
                'attr' => array('max_width' => 400, 'max_height' => 400, 'class' => 'activity-content activity-pictureFile')
            ))
            ->add('pictureForm', HiddenType::class, array(
                'label' => false,
                'mapped' => false,
                'required' => false,
                'attr' => array(
                    'class' => 'activity-content activity-pictureForm'
                )
            ))
            ->add('pdfFile', VichFileType::class, array(
                'label' => false,
                'required' => false,
                'download_uri' => false,
                'allow_delete' => true,
                'translation_domain' => 'messages',
                'constraints' => new File(
                    array(
                        'mimeTypes' => array(
                            'application/pdf', 'application/x-pdf'
                        )
                    )
                ),
                'attr' => array(
                    'class' => 'activity-content activity-pdfFile'
                )
            ))
            ->add('powerpointFile', VichFileType::class, array(
                'label' => false,
                'required' => false,
                'download_uri' => false,
                'allow_delete' => true,
                'translation_domain' => 'messages',
                'constraints' => new File(
                    array(
                        'mimeTypes' => array(
                            'application/zip'
                        )
                    )
                ),
                'attr' => array(
                    'class' => 'activity-content activity-powerpointFile'
                )
            ))
            ->add('isNotDownloadablePdf', CheckboxType::class, array(
                'label' => 'admin.lesson.isNotDownloadablePdf',
                'required' => false
            ))
            ->add('videoUrl', null, array(
                'label' => 'admin.activity.videoUrl',
                'required' => false,
                'attr' => array(
                    'class' => 'activity-content activity-video'
                )
            ))
            ->add('preziUrl', null, array(
                'label' => 'admin.activity.preziUrl',
                'required' => false,
                'attr' => array(
                    'class' => 'activity-content activity-prezi'
                )
            ))
            ->add('text', null, array(
                'required' => false,
                'label' => 'admin.activity.text',
                'attr' => array(
                    'class' => 'activity-content tinymce',
                ),
            ))
            ->add('pictureTextFile', VichImageType::class, array(
                'label' => false,
                'required' => false,
                'allow_delete' => true,
                'download_uri' => false,
                'translation_domain' => 'messages',
                'attr' => array('max_width' => 400, 'max_height' => 400, 'class' => 'activity-content activity-pictureTextFile')
            ))
            ->add('pictureTextForm', HiddenType::class, array(
                'label' => false,
                'mapped' => false,
                'required' => false,
                'attr' => array(
                    'class' => 'activity-content activity-pictureTextForm'
                )
            ))
            ->add('bibliographies', CollectionType::class, array(
                'label' => false,
                'entry_options' => array(
                    'label' => false
                ),
                'entry_type' => BibliographyType::class,
                'allow_add'    => true,
                'allow_delete'    => true,
                'by_reference' => false,
                'prototype_name' => '__bibliography__'
            ))
            ->add('questions', CollectionType::class, array(
                'label' => false,
                'entry_options' => array(
                    'label' => false
                ),
                'entry_type' => ActivityQuestionType::class,
                'allow_add'    => true,
                'allow_delete'    => true,
                'by_reference' => false,
                'prototype_name' => '__question__'
            ))
            ->add('files', CollectionType::class, array(
                'entry_type'   		=> ImageType::class,
                'prototype'			=> true,
                'allow_add'			=> true,
                'allow_delete'		=> true,
                'by_reference' 		=> false,
                'required'			=> false,
                'label'			=> false,
                'entry_options' => array(
                    'label' => false,
                    'mimeTypes' => ['application/pdf', 'application/x-pdf']
                ),
                'attr' => array(
                    'class' => 'files'
                ),
                'prototype_name' => '__file__',
            ));
    }
    
    /**
     * {@inheritdoc}
     */
    public function configureOptions(OptionsResolver $resolver): void
    {
        $resolver->setDefaults(array(
            'data_class' => 'Eduprat\DomainBundle\Entity\Activity'
        ));
    }

    /**
     * {@inheritdoc}
     */
    public function getBlockPrefix(): string
    {
        return 'eduprat_domainbundle_activity';
    }

    /**
     * @return array
     */
    static public function getFormats()
    {
        return array(
            'Video' => Activity::FORMAT_VIDEO,
            'Prezi' => Activity::FORMAT_PREZI,
            'Pdf' => Activity::FORMAT_PDF,
            'Image' => Activity::FORMAT_PICTURE,
            'Texte' => Activity::FORMAT_TEXT,
            'Liste de documents' => Activity::FORMAT_FILE_LIST,
            'Bibliographie' => Activity::FORMAT_BIBLIOGRAPHY,
            'Questionnaire de progression' => Activity::FORMAT_QUIZ,
            'Powerpoint' => Activity::FORMAT_POWERPOINT,
        );
    }

}
