<?php

namespace Eduprat\DomainBundle\Form;

use Doctrine\ORM\EntityManagerInterface;
use Doctrine\ORM\EntityRepository;
use Eduprat\AdminBundle\Entity\Person;
use Eduprat\DomainBundle\Entity\Participant;
use Symfony\Bridge\Doctrine\Form\Type\EntityType;
use Symfony\Component\Form\AbstractType;
use Symfony\Component\Form\Event\PostSetDataEvent;
use Symfony\Component\Form\Extension\Core\Type\CheckboxType;
use Symfony\Component\Form\Extension\Core\Type\ChoiceType;
use Symfony\Component\Form\Extension\Core\Type\EmailType;
use Symfony\Component\Form\Extension\Core\Type\TextareaType;
use Symfony\Component\Form\Extension\Core\Type\DateTimeType;
use Symfony\Component\Form\FormBuilderInterface;
use Symfony\Component\Form\FormEvent;
use Symfony\Component\Form\FormEvents;
use Symfony\Component\OptionsResolver\OptionsResolver;
use Symfony\Component\Security\Core\Authorization\AuthorizationCheckerInterface;
use Symfony\Component\Validator\Constraints\Callback;
use Symfony\Component\Validator\Context\ExecutionContextInterface;
use Eduprat\AdminBundle\Services\Regions;

class ParticipantType extends AbstractType
{
    /**
     * @var array|null
     */
    private $ugas;

    private EntityManagerInterface $entityManager;
    private AuthorizationCheckerInterface $authorizationChecker;
    private Regions $regionsService;

    public function __construct(EntityManagerInterface $entityManager, $ugas, AuthorizationCheckerInterface $authorizationChecker, Regions $regionsService)
    {
        $this->ugas = $ugas;
        $this->entityManager = $entityManager;
        $this->authorizationChecker = $authorizationChecker;
        $this->regionsService = $regionsService;
    }

    /**
     * {@inheritdoc}
     */
    public function buildForm(FormBuilderInterface $builder, array $options): void
    {
        $builder
            ->add('civility', ChoiceType::class, array(
                'label' => 'admin.participant.civility',
                'expanded' => false,
                'multiple' => false,
                'required' => false,
                'choices'  => self::getCivilities(),
                'placeholder' => 'admin.participant.civility',
            ))
            ->add('lastname', null, array(
                'label' => 'admin.participant.lastname',
            ))
            ->add('firstname', null, array(
                'label' => 'admin.participant.firstname',
            ))
            ->add('birthdate', DateTimeType::class, array(
                'format' => 'dd/MM/yyyy',
                'widget' => 'single_text',
                'html5' => false,
                'required' => false,
                'attr' => ['provider' => 'datepicker', 'class' => 'datepicker'],
                'label' => 'admin.participant.birthdate',
            ))
            ->add('address', null, array(
                'label' => 'admin.participant.address',
            ))
            ->add('address2', null, array(
                'label' => 'admin.participant.address2',
            ))
            ->add('zipCode', null, array(
                'label' => 'admin.participant.zipCode',
            ))
            ->add('city', null, array(
                'label' => 'admin.participant.city',
            ))
            ->add('region', null, array(
                'label' => 'admin.participant.region',
            ))
            ->add('phone', null, array(
                'label' => 'admin.participant.phone',
            ))
            ->add('mobile', null, array(
                'label' => 'admin.participant.mobile',
            ))
            ->add('birthName', null, array(
                'label' => 'admin.participant.birthName',
            ))
            ->add('rpps', null, array(
                'label' => 'admin.participant.rpps'
            ))
            ->add('adeli', null, array(
                'label' => 'admin.participant.adeli'
            ))
            ->add('email', EmailType::class, array(
                'label' => 'admin.participant.email',
                'required' => false
            ))
            ->add('category', ChoiceType::class, array(
                'label' => 'admin.formation.categories.title',
                'expanded' => false,
                'multiple' => false,
                'required' => true,
                'choices'  => ProgrammeType::getCategories(),
                'placeholder' => 'admin.participant.category',
            ))
            ->add('speciality', ChoiceType::class, array(
                'label' => 'admin.formation.specialities.title',
                'expanded' => false,
                'multiple' => false,
                'required' => true,
                'choices'  => ProgrammeType::getSpecialities(),
                'placeholder' => 'admin.participant.speciality',
            ))
            ->add('uga', ChoiceType::class, array(
                'label' => 'admin.formation.ugas.title',
                'expanded' => false,
                'multiple' => false,
                'required' => false,
                'choices'  => $this->getUgas(),
                'placeholder' => 'admin.global.select',
            ))
            ->add('status', ChoiceType::class, array(
                'label' => 'admin.participant.statusPs',
                'expanded' => false,
                'multiple' => false,
                'required' => false,
                'choices'  => self::getStatuses(),
                'placeholder' => 'admin.global.select',
            ))
            ->add('comments', TextareaType::class, array(
                'label' => 'admin.participant.comments',
                "required" => false
            ))
            ->add('noMailing', CheckboxType::class, array(
                'label' => 'admin.participant.noMailing',
                'required' => false
            ))
            ->add('leadType', ChoiceType::class, array(
                'label' => 'admin.participant.leadType',
                'expanded' => false,
                'multiple' => false,
                'required' => false,
                'choices'  => self::getLeadTypes(),
                'placeholder' => 'admin.global.select',
            ))
            ->add('partenariat', ChoiceType::class, array(
                'label' => 'admin.participant.partenariat',
                'expanded' => false,
                'multiple' => false,
                'required' => false,
                'choices'  => self::getPartenariats(),
                'placeholder' => 'admin.global.select',
                ))
            ->add('ots', ChoiceType::class, array(
                'label' => 'admin.participant.ots',
                'expanded' => false,
                'multiple' => false,
                'required' => false,
                'choices'  => self::getOts(),
                'placeholder' => 'admin.global.select',
            ))
            ->add('advisor', EntityType::class, array(
                'label' => 'admin.participant.advisor',
                'class' => 'Eduprat\AdminBundle\Entity\Person',
                'choice_label' =>  function (Person $person) {
                    return sprintf('%s %s', $person->getLastname(), $person->getFirstname());
                },
                'query_builder' => function(EntityRepository $repository) {
                    $queryBuilder = $repository->createQueryBuilder('p');
                    return $queryBuilder
                        ->where('p.roles like :role')
                        ->setParameter('role', '%ROLE_ADVISOR%')
                        ->orderBy('p.lastname');
                },
                'placeholder' => 'admin.formation.coordinator.empty',
                'attr' => array('class' => 'formateur-person-select'),
                'required' => false
            ))
            ->add('leadContactDate', null, array(
                'label' => 'admin.participant.leadContactDate',
                'required' => false
            ))
            ->add('leadComment', null, array(
                'label' => 'admin.participant.leadComment'
            ))
            ->add('leadCommentEduprat', null, array(
                'label' => 'admin.participant.leadCommentEduprat'
            ))
            ->add('gpmMemberNumber', null, array(
                'label' => 'admin.participant.gpmMemberNumber'
            ))
            ->add('leadReferent', EntityType::class, array(
                'label' => 'admin.participant.leadReferent',
                'class' => 'Eduprat\AdminBundle\Entity\Person',
                'choice_label' =>  function (Person $person) {
                    return sprintf('%s %s', $person->getLastname(), $person->getFirstname());
                },
                'query_builder' => function(EntityRepository $repository) {
                    $queryBuilder = $repository->createQueryBuilder('p');
                    return $queryBuilder
                        ->where('p.roles like :role')
                        ->setParameter('role', '%ROLE_COORDINATOR%')
                        ->andWhere("p.isArchived = false")
                        ->orderBy('p.lastname');
                },
                'placeholder' => 'admin.formation.coordinator.empty',
                'attr' => array('class' => 'formateur-person-select'),
                'required' => false
            ))
            // ->add('exerciceMode', ChoiceType::class, array(
            //     'label' => 'admin.formation.exerciseMode.title',
            //     'expanded' => false,
            //     'multiple' => false,
            //     'required' => false,
            //     'choices'  => self::getExerciseModes(),
            //     'placeholder' => 'admin.formation.exerciseMode.empty',
            // ))
        ;

        if ($this->authorizationChecker->isGranted("ROLE_WEBMASTER")) {
            $builder->add('coordinator', EntityType::class, array(
                'label' => 'admin.participant.coordinator',
                'required' => false,
                'class' => Person::class,
                'choice_label' =>  'invertedFullname',
                'query_builder' => function(EntityRepository $repository) {
                    $queryBuilder = $repository->createQueryBuilder('p');
                    return $queryBuilder
                        ->where('p.roles like :role')
                        ->setParameter('role', '%"ROLE_COORDINATOR%')
                        ->orderBy("p.lastname", "ASC")
                        ->addOrderBy("p.firstname", "ASC")
                        ;
                },
            ));
        }

        $builder->addEventListener(FormEvents::POST_SET_DATA, function (FormEvent $event) {
            // get the form from the event
            $form = $event->getForm();
            /** @var Participant $data */
            $data = $event->getData();

            $civility = $data->getCivility();

            if (!empty($civility && !in_array($civility, self::getCivilities()))) {
                // get the field options
                $options = $form->get('civility')->getConfig()->getOptions();
                // add the mode to the choices array
                $options['choices'][$civility] = $civility;
                $form->add('civility', ChoiceType::class, $options);
            }

            if ($data->getId() === null) {
                // get the field options
                $options = $form->get('status')->getConfig()->getOptions();
                // add the mode to the choices array
                $options['data'] = Participant::STATUS_ACTIF;
                $form->add('status', ChoiceType::class, $options);
            }

            $form->add('leadStatus', ChoiceType::class, array(
                'label' => 'admin.participant.leadStatus',
                'expanded' => false,
                'multiple' => false,
                'required' => false,
                'choices'  => self::getLeadStatus(),
                'placeholder' => 'admin.global.select',
                // 'disabled' => $data->isLeadOn()
            ))
            ->add('leadState', ChoiceType::class, array(
                'label' => 'admin.participant.leadState',
                'expanded' => false,
                'multiple' => false,
                'required' => false,
                'choices'  => self::getLeadStates(),
                'placeholder' => 'admin.global.select',
                'data' => $data->getLeadState() ? $data->getLeadState() : 'A traiter'
            ));
        });

        $builder->addEventListener(FormEvents::POST_SET_DATA, function (PostSetDataEvent $event) use (&$submitted_data) {
            /** @var Participant $data */
            $originalData = $event->getData();
            $form = $event->getForm();

            $currentAdeli = $originalData->getAdeli();
            $currentRpps = $originalData->getRpps();

            $adeliOptions = $form->get('adeli')->getConfig()->getOptions();
            $rppsOptions = $form->get('rpps')->getConfig()->getOptions();

            $adeliOptions['constraints'] = [
                new Callback([
                    'callback' => function (?string $value, ExecutionContextInterface $context) use ($currentAdeli) {
                        if (!$value || $value === $currentAdeli) {
                            return;
                        }

                        $existing = $this->entityManager->getRepository(Participant::class)->findOneByAdeli($value);

                        if (!is_null($existing)) {
                            $context
                                ->buildViolation('participant.adeli.exists')
                                ->atPath('[adeli]')
                                ->addViolation()
                            ;
                        }
                    },
                ]),
            ];

            $rppsOptions['constraints'] = [
                new Callback([
                    'callback' => function (?string $value, ExecutionContextInterface $context) use ($currentRpps) {
                        if (!$value || $value === $currentRpps) {
                            return;
                        }

                        $existing = $this->entityManager->getRepository(Participant::class)->findOneByRpps($value);

                        if (!is_null($existing)) {
                            $context
                                ->buildViolation('participant.rpps.exists')
                                ->atPath('[rpps]')
                                ->addViolation()
                            ;
                        }
                    },
                ]),
            ];

            $form->add('adeli', null, $adeliOptions);
            $form->add('rpps', null, $rppsOptions);
        });
    }

    /**
     * @return array
     */
    public function getUgas()
    {
        $ugas = array();

        foreach ($this->ugas as $index => $uga) {
            $ugas[$uga["label"]] = $uga["id"];
        }

        ksort($ugas);

        return $ugas;
    }

    /**
     * @return array
     */
    public static function getStatuses(): array
    {
        return array(
            Participant::STATUS_ACTIF  => Participant::STATUS_ACTIF,
            Participant::STATUS_ACTIF_REMPLACANT => Participant::STATUS_ACTIF_REMPLACANT,
            Participant::STATUS_RETRAITE_ACTIF => Participant::STATUS_RETRAITE_ACTIF,
            Participant::STATUS_RETRAITE_INNACTIF => Participant::STATUS_RETRAITE_INNACTIF,
            Participant::STATUS_DECEDE => Participant::STATUS_DECEDE,
            Participant::STATUS_AUTRE_INNACTIF => Participant::STATUS_AUTRE_INNACTIF,
        );
    }

    /**
     * @return array
     */
    public static function getCivilities(): array
    {
        $civilities = array(
            "Docteur",
            "Docteur (M)",
            "Docteur (Mme)",
            "Madame",
            "Monsieur",
            "Maitre",
            "Maitre (M)",
            "Maitre (Mme)",
            "Professeur",
            "Professeur (M)",
            "Professeur (Mme)",
        );
        return array_combine($civilities, $civilities);
    }

    public static function getLeadStatus() : array
    {
        $leadStatus = array (
            Participant::LEAD_ON_VALUE,
            Participant::LEAD_OFF_VALUE
        );
        return array_combine($leadStatus, $leadStatus);
    }

    public static function getLeadStates() : array
    {
        $leadStates = array (
           "A traiter",
           "En cours",
           "Inscrit",
           "Déjà client",
           "Non intéressé",
           "Non joignable",
           "Non éligible",
           "Rien à proposer"
        );

        
        return array_combine($leadStates, $leadStates);
    }

    public static function getLeadTypes() : array
    {
        $leadTypes = array (
            Participant::TYPE_GPM,
            Participant::TYPE_MSOIGNER,
            Participant::TYPE_MFM,
            Participant::TYPE_PODOLOGUE,
            Participant::TYPE_SITE_INTERNET,
        );
        return array_combine($leadTypes, $leadTypes);
    }

    public static function getPartenariats() : array
    {
        $partenariats = array (
            Participant::PART_GPM,
            Participant::PART_MSOIGNER,
            Participant::PART_MFM,
            Participant::PART_PODOLOGUE,
            Participant::PART_SITE_INTERNET,
            Participant::PART_LBI,
            Participant::PART_PHARMAZON
        );
        return array_combine($partenariats, $partenariats);
    }

    public static function getOts() : array
    {
        $ots = array (
            Participant::OTS_CPTS,
            Participant::OTS_MSP,
            Participant::OTS_CDS,
            Participant::OTS_CH,
            Participant::OTS_CLINIQUE,
            Participant::OTS_ASSOCIATION
        );
        return array_combine($ots, $ots);
    }

    /**
     * @return array
     */
    public static function getExerciseModes()
    {
        return array(
            'Monomode' => array(
                'Libéral' => 'Libéral',
                'Salarié' => 'Salarié',
                'Salarié en centre de santé conventionné' => 'Salarié en centre de santé conventionné',
                'Salarié hospitalier' => 'Salarié hospitalier',
                'Salarié de l’industrie' => 'Salarié de l’industrie'
            ),
            'Monomode 2017' => array(
                'Mixte' => 'Mixte',
                'Salarié' => 'Salarié',
                'Service de Santé des Armées' => 'Service de Santé des Armées'
            )
        );
    }

    
    /**
     * {@inheritdoc}
     */
    public function configureOptions(OptionsResolver $resolver): void
    {
        $resolver->setDefaults(array(
            'data_class' => Participant::class
        ));
    }

    /**
     * {@inheritdoc}
     */
    public function getBlockPrefix(): string
    {
        return 'eduprat_domainbundle_participant';
    }


}
