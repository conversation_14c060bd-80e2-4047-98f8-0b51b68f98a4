<?php

namespace Eduprat\DomainBundle\Form;

use Symfony\Component\Form\AbstractType;
use Symfony\Component\Form\FormBuilderInterface;
use Symfony\Component\Validator\Constraints\File;
use Vich\UploaderBundle\Form\Type\VichFileType;
use Symfony\Component\OptionsResolver\OptionsResolver;

class FormateurFilesType extends AbstractType
{
    /**
     * {@inheritdoc}
     */
    public function buildForm(FormBuilderInterface $builder, array $options): void
    {
        $builder
            ->add('contratFile', VichFileType::class, array(
                'label' => false,
                'required' => false,
                'download_uri' => false,
                'allow_delete' => false,
                'translation_domain' => 'messages',
                'constraints' => new File(
                    array(
                        'mimeTypes' => array(
                            'image/jpeg', 'application/pdf', 'application/x-pdf'
                        )
                    )
                )
            ));
    }

    /**
     * {@inheritdoc}
     */
    public function configureOptions(OptionsResolver $resolver): void
    {
        $resolver->setDefaults(array(
            'data_class' => 'Eduprat\DomainBundle\Entity\FormateurFiles'
        ));
    }

    /**
     * {@inheritdoc}
     */
    public function getBlockPrefix(): string
    {
        return 'eduprat_domainbundle_formateur_files';
    }

}
