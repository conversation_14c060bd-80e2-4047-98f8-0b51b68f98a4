<?php

namespace Eduprat\DomainBundle\Form;

use DateTime;
use Doctrine\ORM\EntityManagerInterface;
use Ed<PERSON>rat\AdminBundle\Entity\Person;
use Eduprat\DomainBundle\Entity\AuditCategory;
use Ed<PERSON>rat\DomainBundle\Entity\FormationActalians;
use Eduprat\DomainBundle\Entity\FormationAudit;
use Eduprat\DomainBundle\Entity\FormationCongres;
use Eduprat\DomainBundle\Entity\FormationElearning;
use Eduprat\DomainBundle\Entity\FormationPowerpoint;
use Eduprat\DomainBundle\Entity\FormationPresentielle;
use Eduprat\DomainBundle\Entity\FormationSedd;
use Eduprat\DomainBundle\Entity\FormationVignette;
use Eduprat\DomainBundle\Entity\FormationVignetteAudit;
use Eduprat\DomainBundle\Entity\PriseEnCharge;
use Eduprat\DomainBundle\Model\PlaquetteSearch;
use Eduprat\DomainBundle\Services\AdressesService;
use Symfony\Component\Form\AbstractType;
use Symfony\Component\Form\Extension\Core\Type\ChoiceType;
use Symfony\Component\Form\FormBuilderInterface;
use Symfony\Component\Form\Extension\Core\Type\DateTimeType;
use Symfony\Component\OptionsResolver\OptionsResolver;
use Symfony\Component\Security\Core\Authentication\Token\Storage\TokenStorageInterface;

class PlaquetteSearchType extends AbstractType
{

    /**
     * @var Person
     */
    private $user;
    private EntityManagerInterface $entityManager;

    public function __construct(TokenStorageInterface $tokenStorage, EntityManagerInterface $entityManager)
    {
        $this->user = $tokenStorage->getToken()->getUser();
        $this->entityManager = $entityManager;
    }

    /**
     * {@inheritdoc}
     */
    public function buildForm(FormBuilderInterface $builder, array $options): void
    {
        $prisesEnChargeRepo = $this->entityManager->getRepository(PriseEnCharge::class);
        $prisesEnChargeAC = $prisesEnChargeRepo->findAll();
        $prisesEnCharge = [];
        foreach ($prisesEnChargeAC as $priseEnChargeAC) {
            $prisesEnCharge[$priseEnChargeAC->getName()] = $priseEnChargeAC->getId();
        }

        $auditCategoryRepo = $this->entityManager->getRepository(AuditCategory::class);
        $auditCategoryAC = $auditCategoryRepo->findAll();
        $auditCategories = [];
        foreach ($auditCategoryAC as $auditCategory) {
            $auditCategories[$auditCategory->getName()] = $auditCategory->getId();
        }
        $types = array(
            "Audit" => FormationAudit::class,
            "Cas cliniques" => "predefined",
            "Presentielle" => FormationPresentielle::class,
            "Vignettes cliniques" => FormationVignette::class,
            "Vignettes / Audit" => FormationVignetteAudit::class,
        );

        if (!$this->user->isCoordinatorLbi()) {
            $types = array_merge($types, array(
                "Actalians" => FormationActalians::class,
                "Elearning" => FormationElearning::class,
                "Powerpoint" => FormationPowerpoint::class,
                "Congres" => FormationCongres::class,
                "Sedd" => FormationSedd::class,
                "Coordinateur LBI" => "lbi",
            ));
        }

        $builder
            ->add('key', null, array(
                'label' => 'admin.formation.search.key',
                'attr' => ['class' => 'form-control'],
                'required' => false,
                'disabled' => $options['disabled_edit'],
            ))
            ->add('nbSession', null, array(
                'label' => 'admin.formation.search.session',
                'attr' => ['class' => 'form-control'],
                'required' => false,
                'disabled' => $options['disabled_edit'],
            ))
            ->add('start', DateTimeType::class, array(
                'format' => 'dd/MM/yyyy',
                'widget' => 'single_text',
                'html5' => false,
                'attr' => ['provider' => 'datepicker', 'class' => 'datepicker form-control'],
                'label' => 'admin.formation.search.start',
                'required' => false,
                'disabled' => $options['disabled_edit'],
            ))
            ->add('end', DateTimeType::class, array(
                'format' => 'dd/MM/yyyy',
                'widget' => 'single_text',
                'html5' => false,
                'attr' => ['provider' => 'datepicker', 'class' => 'datepicker form-control'],
                'label' => 'admin.formation.search.end',
                'required' => true,
                'disabled' => $options['disabled_edit'],
                'data' => new DateTime((date("Y")+1).'-12-31'),
            ))
            ->add('presence', ChoiceType::class, array(
                'required' => false,
                'label' => 'admin.programme.presence',
                'choices'  => ProgrammeType::getPresences(),
                'disabled' => $options['disabled_edit'],
            ))
            ->add('categories', ChoiceType::class, array(
                'label' => 'admin.formation.categories.title',
                'expanded' => false,
                'multiple' => true,
                'required' => false,
                'choices'  => ProgrammeType::getCategories(),
                'placeholder' => 'admin.formation.categories.empty',
                'disabled' => $options['disabled_edit'],
            ))
            ->add('specialities', ChoiceType::class, array(
                'label' => 'admin.formation.specialities.title',
                'expanded' => false,
                'multiple' => true,
                'required' => false,
                'choices'  => ProgrammeType::getSpecialities(true),
                'placeholder' => 'admin.formation.specialities.empty',
                'disabled' => $options['disabled_edit'],
            ))
            ->add('regions', ChoiceType::class, array(
                'label' => 'admin.formation.regions.title',
                'expanded' => false,
                'multiple' => true,
                'required' => false,
                'choices'  => $this->getRegions(),
                'placeholder' => 'admin.formation.regions.empty',
                'disabled' => $options['disabled_edit'],
            ))
            ->add('departements', ChoiceType::class, array(
                'label' => 'admin.formation.departements.title',
                'expanded' => false,
                'multiple' => true,
                'required' => false,
                'choices'  => $this->getDepartements(),
                'placeholder' => 'admin.formation.departements.empty',
                'disabled' => $options['disabled_edit'],
            ))
            ->add('thematiques', ChoiceType::class, array(
                'label'    => 'admin.programme.category.label',
                'expanded' => false,
                'multiple' => true,
                'required' => false,
                'placeholder' => 'admin.programme.category.empty',
                'choices' => $auditCategories,
                'disabled' => $options['disabled_edit'],
            ))
            ->add('prisesEnCharge', ChoiceType::class, array(
                'label' => 'admin.formation.priseEnCharge.title',
                'placeholder' => 'admin.formation.priseEnCharge.empty',
                'expanded' => false,
                'multiple' => true,
                'required' => false,
                'choices'  => $prisesEnCharge,
                'disabled' => $options['disabled_edit'],
            ))
            ->add('virtualUnrelated', ChoiceType::class, array(
                'required' => true,
                'label' => 'admin.programme.virtualUnrelated',
                'expanded' => false,
                'multiple' => false,
                'choices'  => self::getVirtuelUnrelated(),
                'disabled' => $options['disabled_edit'],
            ))

            ->add('elearningUnrelated', ChoiceType::class, array(
                'required' => true,
                'label' => 'admin.programme.elearningUnrelated',
                'expanded' => false,
                'multiple' => false,
                'choices'  => self::getElearningUnrelated(),
                'disabled' => $options['disabled_edit'],
            ))
            ;
    }

    /**
     * {@inheritdoc}
     */
    public function configureOptions(OptionsResolver $resolver): void
    {
        $resolver->setDefaults(array(
            'data_class' => PlaquetteSearch::class,
            'coordinatorOrSupervisorId' => null,
            'comptabilite' => false,
            'disabled_edit' => false,
        ));
    }

    /**
     * {@inheritdoc}
     */
    public function getBlockPrefix(): string
    {
        return 'eduprat_search';
    }

    public function getRegions(): array
    {
        $regions = array_values(AdressesService::REGIONS);
        return array_combine($regions, $regions);
    }

    public function getDepartements(): array
    {
        $departements = [];
        foreach (AdressesService::DEPARTEMENTS as $departement) {
            $departements[$departement["code"] . ' - '. $departement["label"]] = $departement["code"];
        }
        return $departements;
    }

    public function getVirtuelUnrelated(): array
    {
        return array(
            "Toutes" => "unrelated",
            "admin.global.mesSessions" => "mysessions",
        );
    }

    public function getElearningUnrelated(): array
    {
        return array(
            "Toutes" => "unrelated",
            'admin.global.mesSessions' => 'mysessions',
        );
    }
}
