<?php

namespace Eduprat\DomainBundle\Twig;

use Eduprat\DomainBundle\Entity\Participation;
use Twig\Extension\AbstractExtension;
use Twig\TwigFilter;

class ParticipationAuditExtension extends AbstractExtension
{
    public function getFilters(): array
    {
        return array(
            new TwigFilter('auditCompleted', $this->participationAuditCompleted(...)),
        );
    }

    public function participationAuditCompleted(Participation $participation, $auditId, $patient = 1): bool
    {
        if (!$participation->getFormation()->hasLinkedForm()) {
            return false;
        }
        return $participation->getCompletedForm($auditId);
    }

    public function getName(): string
    {
        return 'participation_audit_extension';
    }
}