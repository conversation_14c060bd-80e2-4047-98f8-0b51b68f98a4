<?php

namespace Eduprat\DomainBundle\Twig;

use Twig\Extension\AbstractExtension;
use Twig\TwigFunction;

class ColorExtension extends AbstractExtension
{

    public function getFunctions(): array
    {
        return array(
            new TwigFunction('hexToRGBA', $this->hexToRGBA(...)),
        );
    }

    public function hexToRGBA($color, $transparence = null): string
    {
        $color = ltrim($color, '#');

        // Convert the shorthand hex to the full hex (09F => 0099FF)
        if (strlen($color) == 3) {
            $color = $color[0] . $color[0] . $color[1] . $color[1] . $color[2] . $color[2];
        }

        return 'rgba('.implode(',', [
            (int)hexdec(substr($color, 0, 2)),
            (int)hexdec(substr($color, 2, 2)),
            (int)hexdec(substr($color, 4, 2)),
            $transparence
        ]).')';
    }

    public function getName(): string
    {
        return 'color_extension';
    }
}