<?php

namespace Eduprat\DomainBundle\Twig;

use Twig\Extension\AbstractExtension;
use Twig\TwigFunction;

class ImageEncoderExtension extends AbstractExtension
{

    public function getFilters(): array {
        $filters = [];
        $filters [] = new \Twig\TwigFilter('base64_encode', function (string $encode) {
            return base64_encode(file_get_contents($encode));
        });
        $filters [] = new \Twig\TwigFilter('imageType', function (string $encode) {
            return match (pathinfo($encode, PATHINFO_EXTENSION)) {
                'jpg' => 'image/jpeg',
                'jpeg' => 'image/jpeg',
                'png' => 'image/png',
                'gif' => 'image/gif',
                'webp' => 'image/webp',
                default => 'image/png',
            };
        });
        return $filters;
    }

    public function getName(): string
    {
        return 'base64_encode';
    }
}
