<?php

namespace Eduprat\DomainBundle\Twig;

use Twig\Extension\AbstractExtension;
use Twig\TwigFunction;

class ModuleExtension extends AbstractExtension
{

    public function getFunctions(): array
    {
        return array(
            new TwigFunction('moduleInStep', $this->moduleInStep(...)),
        );
    }

    public function moduleInStep($step, $moduleId): ?array
    {
        foreach($step["modules"] as $module) {
            if ($module["id"] === $moduleId) {
                return $module;
            }
        }
        return false;
    }

    public function getName(): string
    {
        return 'module_step';
    }
}