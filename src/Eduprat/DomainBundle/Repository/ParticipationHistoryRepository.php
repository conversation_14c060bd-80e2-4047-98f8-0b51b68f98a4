<?php

namespace Eduprat\DomainBundle\Repository;

use Doctrine\ORM\EntityRepository;
use Eduprat\AdminBundle\Entity\Person;
use Eduprat\CrmBundle\Model\ComptabiliteSearch;
use Eduprat\DomainBundle\Entity\Participant;

/**
 * ParticipationHistoryRepository
 *
 * This class was generated by the Doctrine ORM. Add your own custom
 * repository methods below.
 */
class ParticipationHistoryRepository extends EntityRepository
{

    public function findInscriptions(ComptabiliteSearch $search) {

        $qb = $this->createQueryBuilder('pah')
            ->innerJoin("pah.participation", "pa")
            ->innerJoin("pa.formation", "f")
            ->orderBy("pah.createdAt", "desc")
        ;

        if($search->startInscription) {
            $start =  $search->startInscription;
            $start->setTime(0, 0, 0);
            $qb->andWhere("pah.createdAt >= :start")
            ->setParameter("start", $start);
        }

        if($search->endInscription) {
            $end = $search->endInscription;
            $end->setTime(23, 59, 59);
            $qb->andWhere("pah.createdAt <= :end")
            ->setParameter("end", $end);
        }

        if ($search->coordinator) {
            $qb
                ->innerJoin("f.coordinators", "c")
                ->innerJoin("c.person", "pe")
                ->leftJoin("pa.coordinator", "pac")
                ->leftJoin("pac.person", "pec")
                ->andWhere("c.person = :id1")->setParameter("id1", $search->coordinator->getId())
                ->andWhere(
                    $qb->expr()->orX(
                        $qb->expr()->isNull("pa.coordinator"),
                        $qb->expr()->andX(
                            $qb->expr()->isNotNull("pa.coordinator"),
                            $qb->expr()->eq("pec.id", $search->coordinator->getId()),
                        ),
                    )
                )
            ;
        }

        if ($search->supervisor) {
            $qb
                ->innerJoin("f.coordinators", "cs")
                ->innerJoin("cs.person", "pes")
                ->leftJoin("pa.coordinator", "pacs")
                ->leftJoin("pacs.person", "pecs")
                ->andWhere("pes.supervisor = :supervisor")->setParameter("supervisor", $search->supervisor->getId())
                ->andWhere(
                    $qb->expr()->orX(
                        $qb->expr()->isNull("pa.coordinator"),
                        $qb->expr()->andX(
                            $qb->expr()->isNotNull("pa.coordinator"),
                            $qb->expr()->eq("pecs.supervisor", $search->supervisor->getId()),
                        ),
                    )
                )
            ;
        }

        if ($search->webmaster) {
            $qb
                ->innerJoin("f.coordinators", "cw")
                ->innerJoin("cw.person", "pew")
                ->leftJoin("pa.coordinator", "pacw")
                ->leftJoin("pacw.person", "pecw")
                ->andWhere("pew.webmaster = :webmaster")->setParameter("webmaster", $search->webmaster->getId())
                ->andWhere(
                    $qb->expr()->orX(
                        $qb->expr()->isNull("pa.coordinator"),
                        $qb->expr()->andX(
                            $qb->expr()->isNotNull("pa.coordinator"),
                            $qb->expr()->eq("pecw.webmaster", $search->webmaster->getId()),
                        ),
                    )
                )
            ;
        }

        if ($search->partenariat) {
            $qb->andWhere("pa.partenariat = :partenariat")->setParameter("partenariat", $search->partenariat);
        }

        if ($search->type) {
            $qb->andWhere("pah.action = :action")->setParameter('action', $search->type);
        }

        return $qb;
    }

    public function countSearchInscriptionsResults($search) {
        $queryBuilder = $this->findInscriptions($search)->select('COUNT(DISTINCT pah.id)');
        return $queryBuilder->getQuery()->getSingleScalarResult();
    }

    public function findSearchInscriptions($search, $page, $number, Person $user = null) {
        return $this->findInscriptions($search)
            ->setFirstResult(($page-1) * $number)
            ->setMaxResults($number)->getQuery()->getResult();
    }

    public function findAllInscriptions($search) {
        return $this->findInscriptions($search)->getQuery()->getResult();
    }

    public function findByParticipant(Participant $participant)
    {
        $qb = $this->createQueryBuilder('ph');
        $qb->innerJoin('ph.participation', 'pa')
            ->innerJoin('pa.participant', 'p')
            ->where('p.id = :id')
            ->setParameter('id', $participant->getId())
            ->addOrderBy('ph.createdAt', 'ASC')
        ;
        return $qb->getQuery()->getResult();
    }
}
