<?php

namespace Eduprat\DomainBundle\Repository;

use Alienor\ApiBundle\Repository\PageableAbstractRepository;
use Doctrine\ORM\QueryBuilder;
use Eduprat\AdminBundle\Entity\LeadSearch;
use Eduprat\AdminBundle\Entity\ParticipantSearch;
use Eduprat\CrmBundle\Services\LeadsService;
use Eduprat\DomainBundle\Entity\Audit;
use Eduprat\DomainBundle\Entity\FormationActalians;
use Eduprat\DomainBundle\Entity\FormationAudit;
use Eduprat\DomainBundle\Entity\Participant;

/**
 * ParticipantRepository
 *
 * This class was generated by the Doctrine ORM. Add your own custom
 * repository methods below.
 */
class ParticipantRepository extends PageableAbstractRepository
{
    /**
     * @param $code
     * @return array
     */
    public function findOneByRppsOrAdeliOrLogin($code) {
        $queryBuilder = $this->createQueryBuilder('p');
        $queryBuilder
            ->where('p.rpps like :code')
            ->orWhere('p.adeli like :code')
            ->orWhere('p.login like :code')
            ->setParameter('code', $code);

        return $queryBuilder->getQuery()->getOneOrNullResult();
    }

    public function findOneByRppsAndAdeli($rpps, $adeli) {
        $qb = $this->createQueryBuilder('p');

        if($rpps !== "") {
            $qb->orWhere('p.rpps = :rpps')->setParameter('rpps', $rpps);
        }

        if($adeli !== "") {
            $qb->orWhere('p.adeli = :adeli')->setParameter('adeli', $adeli);
        }

        return $qb->getQuery()->getOneOrNullResult();
    }

    public function findOneByNameAndEmail($firstname, $lastname, $email) {
        $qb = $this->createQueryBuilder('p');

        $qb->andWhere('p.firstname = :firstname')->setParameter('firstname', $firstname);
        $qb->andWhere('p.lastname = :lastname')->setParameter('lastname', $lastname);
        $qb->andWhere('p.email = :email')->setParameter('email', $email);
        $qb->andWhere('p.adeli IS NULL');
        $qb->andWhere('p.rpps IS NULL');

        return $qb->getQuery()->getOneOrNullResult();
    }

    public function getBaseQuery()
    {
        $queryBuilder = $this->createQueryBuilder('p')->select('p');

        return $queryBuilder;
    }

    function getQueryBuilder()
    {
        return $this->getBaseQuery();
    }

    function countQueryBuilder()
    {
        return $this->getBaseQuery()->select('count(p)');
    }

    function listing($page, $nombre)
    {
        return $this->getQueryBuilder()
            ->setFirstResult(($page - 1) * $nombre)
            ->setMaxResults($nombre);
    }

    function getZipCodes($zipSearch)
    {
        $queryBuilder = $this->getQueryBuilder()
            ->select('DISTINCT p.zipCode')
            ->andWhere("p.zipCode != ''");

            if (isset($zipSearch['coordinatorId'])) {
                $queryBuilder->join('p.participations', 'pa')
                            ->join('pa.formation', 'f')
                            ->join('f.coordinators', 'c')
                            ->join('c.person', 'pe')
                            ->andWhere('pe.id = :coordinator')
                            ->setParameter(':coordinator', $zipSearch['coordinatorId']);
            }
            else if (isset($zipSearch['supervisorId'])) {
                $queryBuilder->join('p.participations', 'pa')
                            ->join('pa.formation', 'f')
                            ->join('f.coordinators', 'c')
                            ->join('c.person', 'pe')
                            ->andWhere('pe.supervisor = :supervisor')
                            ->setParameter(':supervisor', $zipSearch['supervisorId']);
            }

        return $queryBuilder->getQuery()->getScalarResult();
    }

    public function countSearchResults(ParticipantSearch $search) {
        return $this->createSearchResultsQueryBuilder($search)
            ->select('COUNT(DISTINCT p.id)')
            ->getQuery()->getSingleScalarResult()
            ;
    }

    public function findSearchResults(ParticipantSearch $search, $page, $number, $sortBy = null, $order = "ASC") {
        $qb = $this->createSearchResultsQueryBuilder($search)
            ->setFirstResult(($page-1) * $number)
            ->setMaxResults($number);
        $this->addSortBy($qb, $sortBy, $order);
        return $qb->getQuery()->getResult();
    }

    public function countLeadSearchResults(LeadSearch $search) {
        return $this->createLeadSearchResultsQueryBuilder($search)
            ->select('COUNT(DISTINCT p.id)')
            ->getQuery()->getSingleScalarResult()
            ;
    }

    public function findLeadSearchResults(LeadSearch $search, $page, $number, $sortBy = null, $order = "ASC") {
        $qb = $this->createLeadSearchResultsQueryBuilder($search)
            ->setFirstResult(($page-1) * $number)
            ->setMaxResults($number);
        $this->addSortBy($qb, $sortBy, $order);
        return $qb->getQuery()->getResult();
    }

    public function findSearchResultsCsv(ParticipantSearch $search, $sortBy = null, $order = "ASC", $psAnalysisCsv = false) {
        $qb = $this->createSearchResultsQueryBuilder($search);
        $this->addSortBy($qb, $sortBy, $order, $psAnalysisCsv);
        return $qb->getQuery()->getResult();
    }

    public function findLeadSearchResultsCsv(LeadSearch $search, $sortBy = null, $order = "ASC") {
        $qb = $this->createLeadSearchResultsQueryBuilder($search);
        $this->addSortBy($qb, $sortBy, $order);
        return $qb->getQuery()->getResult();
    }

    public function findSearchAllow(ParticipantSearch $search) {
        return $this->createSearchResultsQueryBuilder($search)
            ->getQuery()->getResult();
    }

    public function addSortBy(QueryBuilder $qb, $sortBy = null, $order = "ASC", $psAnalysisCsv = false) {
        if ($sortBy) {
            $hasFormations = strpos($sortBy, 'formations_') === 0;
            $hasHours = strpos($sortBy, 'hours_')  === 0;
            if  ($hasFormations || $hasHours) {
                $currentYear = (int) (new \DateTime())->format("Y");
                $startYear = $psAnalysisCsv ? 2017 :  $currentYear - 3;
                $years = range($currentYear, $startYear);
                $qbs = array();
                foreach ($years as $year) {
                    $alias = "p_" . $year;
                    $qbs[$year] = $this->createQueryBuilder($alias);
                    if ($hasFormations) {
                        $select = "COUNT(DISTINCT f$alias.id)";
                        $fieldName = "formations_" . $year;
                    } else {
                        $select = "SUM(COALESCE(pa$alias.nbHour, 0))";
                        $fieldName = "hours_" . $year;
                    }
                    $subQuery = "(" . $qbs[$year]->select($select)
                            ->leftJoin("$alias.participations", "pa$alias")
                            ->leftJoin("pa$alias.formation", "f$alias")
                            ->leftJoin("f$alias.programme", "pr$alias")
                            ->andWhere("$alias.id = p.id")
                            ->andWhere("pr$alias.year = $year")
                            ->andWhere("pa$alias.archived = false")
                            ->getDQL() . ") AS HIDDEN " . $fieldName;
                    $qb->addSelect($subQuery);
                }
            }

            $qb->orderBy($sortBy, $order);
        }
        $qb->addOrderBy("p.createdAt", "DESC");
        $qb->addGroupBy("p.id");
        return $qb;
    }

    /**
     * @param ParticipantSearch $search
     * @return QueryBuilder
     */
    public function createSearchResultsQueryBuilder(ParticipantSearch $search) {
        $queryBuilder = $this->getQueryBuilder();

        if($search->id) {
            $queryBuilder
            ->andWhere('p.id = :id')
            ->setParameter(':id', $search->id);
        }

        if ($search->lastname) {
            $queryBuilder->andWhere($queryBuilder->expr()->like('p.lastname', ':lastname'))
                ->setParameter(':lastname', '%'.$search->lastname.'%');
        }

        if ($search->firstname) {
            $queryBuilder->andWhere($queryBuilder->expr()->like('p.firstname', ':firstname'))
                ->setParameter(':firstname', '%'.$search->firstname.'%');
        }

        if ($search->zipCode) {
            $queryBuilder->andWhere($queryBuilder->expr()->like('p.zipCode', ':zipCode'))
                ->setParameter(':zipCode', '%'.$search->zipCode.'%');
        }

        if ($search->adeli) {
            $queryBuilder->andWhere($queryBuilder->expr()->like('p.adeli', ':adeli'))
                ->setParameter(':adeli', '%'.$search->adeli.'%');
        }

        if ($search->rpps) {
            $queryBuilder->andWhere($queryBuilder->expr()->like('p.rpps', ':rpps'))
                ->setParameter(':rpps', '%'.$search->rpps.'%');
        }

        if (!is_null($search->isCreated)) {
            $queryBuilder->leftJoin('p.user', 'u');
            $queryBuilder->andWhere($queryBuilder->expr()->eq('u.hasCreatedPassword', ':enabled'))
                ->setParameter(':enabled', json_decode($search->isCreated));
        }

        if (!is_null($search->isProspect)) {
            $queryBuilder->andWhere('p.isProspect = :prospect')->setParameter('prospect', json_decode($search->isProspect));
        }

        if (!is_null($search->gdprAgreement)) {
            if ($search->gdprAgreement === "null") {
                $queryBuilder->andWhere($queryBuilder->expr()->isNull('p.gdprAgreement'));
            } else {
                $queryBuilder->andWhere($queryBuilder->expr()->eq('p.gdprAgreement', ':accepted'))
                    ->setParameter(':accepted', json_decode($search->gdprAgreement));
            }
        }

        if (!is_null($search->gdprAgreementPost)) {
            if ($search->gdprAgreementPost === "null") {
                $queryBuilder->andWhere($queryBuilder->expr()->isNull('p.gdprAgreementPost'));
            } else {
                $queryBuilder->andWhere($queryBuilder->expr()->eq('p.gdprAgreementPost', ':acceptedPost'))
                    ->setParameter(':acceptedPost', json_decode($search->gdprAgreementPost));
            }
        }

        if (!is_null($search->gdprAgreementCall)) {
            if ($search->gdprAgreementCall === "null") {
                $queryBuilder->andWhere($queryBuilder->expr()->isNull('p.gdprAgreementCall'));
            } else {
                $queryBuilder->andWhere($queryBuilder->expr()->eq('p.gdprAgreementCall', ':acceptedCall'))
                    ->setParameter(':acceptedCall', json_decode($search->gdprAgreementCall));
            }
        }

        if($search->city) {
            $queryBuilder
            ->andWhere('p.city LIKE :city')
            ->setParameter(':city', '%'.$search->city.'%');
        }

        if($search->speciality) {
            $queryBuilder
            ->andWhere('p.speciality = :speciality')
            ->setParameter(':speciality', $search->speciality);
        }

        if($search->category) {
            $queryBuilder
            ->andWhere('p.category LIKE :category')
            ->setParameter(':category', '%'.$search->category.'%');
        }

        if($search->ugaName) {
            $queryBuilder
            ->andWhere('p.uga LIKE :uga')
            ->setParameter(':uga', '%'.$search->ugaName.'%');
        }

        if ($search->coordinatorId) {

            $conditions = array();
            foreach($search->coordinatorUgas as $index => $uga) {
                $conditions[] = $queryBuilder->expr()->eq("p.uga", ':uga_'.$index);
                $queryBuilder->setParameter(':uga_'.$index, $uga);
            }

            $orXUga = $queryBuilder->expr()->orX();
            $orXUga->addMultiple($conditions);

            $queryBuilder->andWhere(
                $queryBuilder->expr()->orX(
                    $queryBuilder->expr()->eq("p.coordinator",":coordinatorId"),
                    $queryBuilder->expr()->andX(
                        $queryBuilder->expr()->eq("p.isProspect", true),
                        $orXUga
                    ),
                    $queryBuilder->expr()->andX(
                        $queryBuilder->expr()->eq("p.isProspect", true),
                        $queryBuilder->expr()->isNull("p.uga")
                    )
                )
            );
            $queryBuilder->setParameter("coordinatorId", $search->coordinatorId);
        }

        if ($search->supervisorId) {
            $queryBuilder->leftJoin('p.coordinator', 'spc')->select('DISTINCT p');

            $conditions = array();

            if($search->coordinatorUgas) {
                foreach($search->coordinatorUgas as $index => $uga) {
                    $conditions[] = $queryBuilder->expr()->eq("p.uga", ':uga_'.$index);
                    $queryBuilder->setParameter(':uga_'.$index, $uga);
                }
            }

            $orXUga = $queryBuilder->expr()->orX();
            $orXUga->addMultiple($conditions);

            $queryBuilder->andWhere(
                $queryBuilder->expr()->orX(
                    $queryBuilder->expr()->eq("spc.supervisor",":supervisorId"),
                    $queryBuilder->expr()->andX(
                        $queryBuilder->expr()->eq("p.isProspect", true),
                        $orXUga
                    ),
                    $queryBuilder->expr()->andX(
                        $queryBuilder->expr()->eq("p.isProspect", true),
                        $queryBuilder->expr()->isNull("p.uga")
                    )
                )
            );
            $queryBuilder->setParameter(':supervisorId', $search->supervisorId);
        }

        if ($search->formerId) {
            $queryBuilder->join('p.participations', 'fpa')
                        ->select('DISTINCT p')
                        ->join('fpa.formation', 'ff')
                        ->join('ff.formateurs', 'ffo')
                        ->andWhere('ffo.person = :former')
                        ->andWhere('fpa.archived = false')
                        ->setParameter(':former', $search->formerId);
        }

        if($search->regionZipCodes) {
            $conditions = array();
            foreach($search->regionZipCodes as $dep) {
                if(strlen($dep) == 3) {
                    $conditions[] = $queryBuilder->expr()->eq($queryBuilder->expr()->substring('p.zipCode', 1, 3), ':dep_'.$dep);
                    $queryBuilder->setParameter(':dep_'.$dep, $dep);
                }
                else {
                    $conditions[] = $queryBuilder->expr()->eq($queryBuilder->expr()->substring('p.zipCode', 1, 2), ':dep_'.$dep);
                    $queryBuilder->setParameter(':dep_'.$dep, $dep);
                }
            }
            
            $orX = $queryBuilder->expr()->orX();
            $orX->addMultiple($conditions);
            $queryBuilder->andWhere($orX);
        }

        if ($search->email) {
            $queryBuilder
                ->andWhere('p.email LIKE :email')
                ->setParameter(':email', '%'.$search->email.'%');
        }

        if ($search->programmeTitle) {
            $queryBuilder
                ->join('p.participations', 'tpa')
                ->join('tpa.formation', 'tf')
                ->join('tf.programme', 'tfp');
            if ($search->withTitre === "non") {
                $queryBuilder->andWhere('tfp.title NOT LIKE :title');
            } else {
                $queryBuilder->andWhere('tfp.title LIKE :title');
            } 
            $queryBuilder->andWhere('tpa.archived = false')
                ->setParameter(':title', "%" . $search->programmeTitle . "%");
        }

        if ($search->exerciseMode) {

            $exerciceModes = (array) $search->exerciseMode;

            $queryBuilder
                ->join('p.participations', 'epa')
                ->join('epa.formation', 'ef')
                ->andWhere('epa.archived = false')
                ->andWhere($queryBuilder->expr()->in("epa.exerciseMode", $exerciceModes))
                ;
        }

        if ($search->formationClass && count($search->formationClass) > 0) {
            $queryBuilder->join('p.participations', 'pa')->join('pa.formation', 'f');
            $ors = array_map(function($class) use ($queryBuilder) {
                return $queryBuilder->expr()->isInstanceOf('f', $class);
            }, $search->formationClass);
            $queryBuilder->andWhere(call_user_func_array(array($queryBuilder->expr(), "orX"), $ors));
        }

        if($search->reference) {
            // Total exclusion des participants ayant participé a la formation $search->reference
            if ($search->withReference === "non") {
                $avecRef = $this->createQueryBuilder('participantB')
                    ->leftJoin('participantB.participations', 'participationsB')
                    ->leftJoin('participationsB.formation', 'formationB')
                    ->leftJoin('formationB.programme', 'programmeB')
                    ->where("programmeB.reference = :referenceB");

                $queryBuilder->setParameter("referenceB", $search->reference);

                $queryBuilder->andWhere('p.id NOT IN (' . $avecRef->getDQL() . ')');
            } else {
                $queryBuilder->leftJoin('p.participations', "partiRef");
                $queryBuilder->leftJoin('partiRef.formation', "formaRef");
                $queryBuilder->leftJoin('formaRef.programme', "progRef");
                $queryBuilder->andWhere("progRef.reference = :reference")->setParameter("reference", $search->reference);
            }
        }

        if($search->presence) {
            $queryBuilder->leftJoin('p.participations', "partiPres");
            $queryBuilder->leftJoin('partiPres.formation', "formaPres");
            $queryBuilder->leftJoin('formaPres.programme', "progPres"); 
            $queryBuilder->andWhere('progPres.presence = :presence')->setParameter('presence', $search->presence);
        }
        
        if($search->nbSession || $search->nbSession == "0") {
            $nbSession = $search->nbSession == "0" ? 0 : $search->nbSession;

            // Total exclusion des participants qui participent à une session numéro $nbSession
            if ($search->withNbSession === "non") {
                $avecNb = $this->createQueryBuilder('participantC')
                    ->leftJoin('participantC.participations', 'participationsC')
                    ->leftJoin('participationsC.formation', 'formationC')
                    ->where('formationC.sessionNumber = :sessionNumberC');

                $queryBuilder->setParameter("sessionNumberC", $nbSession);

                $queryBuilder->andWhere('p.id NOT IN (' . $avecNb->getDQL() . ')');
            } else {
                $queryBuilder->leftJoin('p.participations', "partiPres");
                $queryBuilder->leftJoin('partiPres.formation', "formaPres"); 
                $queryBuilder->andWhere("formaPres.sessionNumber = :nbSession")->setParameter("nbSession", $nbSession);
            }
        }


        if ($search->type && $search->withType) {
            $queryBuilder->leftJoin('p.participations', "partiWt");
            $queryBuilder->leftJoin('partiWty.formation', "formaWt");
        }

        if ($search->type && $search->withType === "non") {
            if ($search->type === "predefined") {
                $queryBuilder->leftJoin(FormationAudit::class, 'wffa', 'WITH', 'wffa.id = forma.id');
                $queryBuilder->leftJoin(Audit::class, 'au', 'WITH', 'au.id = wffa.audit');
                $queryBuilder->andWhere("au.type NOT LIKE :auditType")->setParameter('auditType', 'predefined');
            } else if ($search->type === "lbi") {
                $queryBuilder->innerJoin('partiWt.coordinator', "cor");
                $queryBuilder->innerJoin('cor.person', "co");
                $queryBuilder->andWhere("co.roles NOT LIKE '%ROLE_COORDINATOR_LBI%'");
            } else if ($search->type === FormationActalians::class) {
                $queryBuilder->andWhere("formaWt.actaliansPdf = 0");
            } else {
                $queryBuilder->andWhere($queryBuilder->expr()->not($queryBuilder->expr()->isInstanceOf('forma', $search->type)));
            }
        }

        if ($search->type && $search->withType !== "non") {
            if ($search->type === "predefined") {
                $queryBuilder->leftJoin(FormationAudit::class, 'wffa', 'WITH', 'wffa.id = formaWt.id');
                $queryBuilder->leftJoin(Audit::class, 'au', 'WITH', 'au.id = wffa.audit');
                $queryBuilder->andWhere("au.type LIKE :auditType")->setParameter('auditType', 'predefined');
            } else if ($search->type === "lbi") {
                $queryBuilder->innerJoin('partiWt.coordinator', "cor");
                $queryBuilder->innerJoin('cor.person', "co");
                $queryBuilder->andWhere("co.roles like '%ROLE_COORDINATOR_LBI%'");
            } else if ($search->type === FormationActalians::class) {
                $queryBuilder->andWhere("formaWt.actaliansPdf = 1");
            } else {
                $queryBuilder->andWhere($queryBuilder->expr()->isInstanceOf('formaWt', $search->type));
            }
        }

       if($search->departement) {
            $conditions = array();
            foreach($search->departement as $dep) {
                if(strlen($dep) == 3) {
                    $conditions[] = $queryBuilder->expr()->eq($queryBuilder->expr()->substring('p.zipCode', 1, 3), ':depa_'.$dep);
                    $queryBuilder->setParameter(':depa_'.$dep, $dep);
                }
                else {
                    $conditions[] = $queryBuilder->expr()->eq($queryBuilder->expr()->substring('p.zipCode', 1, 2), ':depa_'.$dep);
                    $queryBuilder->setParameter(':depa_'.$dep, $dep);
                }
            }
            
            $orX = $queryBuilder->expr()->orX();
            $orX->addMultiple($conditions);
            $queryBuilder->andWhere($orX);
        }

       if ($search->status) {
            $queryBuilder->andWhere('p.status IN (:status)')->setParameter('status', $search->status);
        }

       if($search->financeMode) {
           $queryBuilder
               ->leftJoin('p.participations', 'fmpa')
               ->leftJoin('fmpa.financeSousMode', 'fsm')
               ->select('DISTINCT p')
               ->andWhere('fsm.financeMode = :financeMode')
               ->setParameter(':financeMode', $search->financeMode);
        }

        if ($search->partenariat) {
            $queryBuilder
            ->andWhere("p.partenariat = :partenariat")->setParameter("partenariat", $search->partenariat);
        }

        if ($search->isActif) {
            $queryBuilder->andWhere('p.status IN (:status)')->setParameter('status', array(Participant::STATUS_ACTIF, Participant::STATUS_ACTIF_REMPLACANT, Participant::STATUS_RETRAITE_ACTIF));
        }

        $queryBuilder->addOrderBy('p.createdAt', 'DESC');

        return $queryBuilder;
    }

    public function findLead(\DateTime $start,\DateTime $end, $withoutReferent = false) {
        $queryBuilder = $this->getQueryBuilder();
        $queryBuilder->leftJoin('p.user', 'u');

        $queryBuilder->andWhere($queryBuilder->expr()->gte('p.leadCreationDate', ':start'))
            ->setParameter(':start', $start);

        $end->setTime(23, 59, 59);
        $queryBuilder->andWhere($queryBuilder->expr()->lte('p.leadCreationDate', ':end'))
            ->setParameter(':end', $end);

        if ($withoutReferent) {
            $queryBuilder->andWhere($queryBuilder->expr()->isNull('p.leadReferent'));
        }
                
        $queryBuilder->andWhere($queryBuilder->expr()->isNotNull('p.leadStatus'));

        return $queryBuilder->getQuery()->getResult();
    }

    public function createLeadSearchResultsQueryBuilder(LeadSearch $search) {
        $queryBuilder = $this->getQueryBuilder();
        $queryBuilder->leftJoin('p.user', 'u');
        $queryBuilder->leftJoin('p.leadHistories', 'lh');

        if($search->id) {
            $queryBuilder
            ->andWhere('p.id = :id')
            ->setParameter(':id', $search->id);
        }

        if ($search->lastname) {
            $queryBuilder->andWhere($queryBuilder->expr()->like('p.lastname', ':lastname'))
                ->setParameter(':lastname', '%'.$search->lastname.'%');
        }

        if ($search->firstname) {
            $queryBuilder->andWhere($queryBuilder->expr()->like('p.firstname', ':firstname'))
                ->setParameter(':firstname', '%'.$search->firstname.'%');
        }

        if ($search->start && $search->end) {
            $queryBuilder->andWhere(
                $queryBuilder->expr()->orX(
                    $queryBuilder->expr()->andX(
                        $queryBuilder->expr()->gte('p.leadCreationDate', ':start'),
                        $queryBuilder->expr()->lte('p.leadCreationDate', ':end'),
                    ),
                    $queryBuilder->expr()->andX(
                        $queryBuilder->expr()->gte('lh.leadCreationDate', ':start'),
                        $queryBuilder->expr()->lte('lh.leadCreationDate', ':end'),
                    ),
                )
            )->setParameter(':start', $search->start)->setParameter(':end', $search->end);
        } else {
            if ($search->start) {
                $queryBuilder->andWhere(
                    $queryBuilder->expr()->orX(
                        $queryBuilder->expr()->gte('p.leadCreationDate', ':start'),
                        $queryBuilder->expr()->gte('lh.leadCreationDate', ':start')
                    )
                )->setParameter(':start', $search->start);
            }
    
            if ($search->end) {
                $search->end->setTime(23, 59, 59);
                $queryBuilder->andWhere(
                    $queryBuilder->expr()->orX(
                        $queryBuilder->expr()->lte('p.leadCreationDate', ':end'),
                        $queryBuilder->expr()->lte('lh.leadCreationDate', ':end')
                    )
                )->setParameter(':end', $search->end);
            }
        }

        if($search->leadType) {
            $queryBuilder->andWhere('p.leadType = :leadType')->setParameter(':leadType', $search->leadType);
        }

        if($search->leadStatus) {
            $queryBuilder->andWhere('p.leadStatus = :leadStatus')->setParameter(':leadStatus', $search->leadStatus);
        }

        if ($search->leadState) {
            $queryBuilder->andWhere('p.leadState IN (:leadState)')->setParameter('leadState', $search->leadState);
        }

        if (!is_null($search->isProspect)) {
            $queryBuilder->andWhere('p.isProspect = :prospect')->setParameter('prospect', json_decode($search->isProspect));
        }

        if($search->speciality) {
            $queryBuilder->andWhere('p.speciality IN (:speciality)')->setParameter('speciality', $search->speciality);
        }

        if($search->category) {
            $queryBuilder->andWhere('p.category IN (:category)')->setParameter('category', $search->category);
        }

        if($search->coordinatorId) {
            $queryBuilder->andWhere('p.leadReferent = :coordinatorId')->setParameter(':coordinatorId', $search->coordinatorId);
        }

        if($search->advisor) {
            $queryBuilder->andWhere('p.advisor = :advisor')->setParameter(':advisor', $search->advisor);
        }

        if($search->webmaster) {
            $queryBuilder->leftJoin("p.leadReferent", "ldr");
            $queryBuilder->andWhere('ldr.webmaster = :webmaster')->setParameter(':webmaster', $search->webmaster);
        }

        // if ($search->coordinatorId) {
        //     $queryBuilder->andWhere(
        //         $queryBuilder->expr()->orX(
        //             $queryBuilder->expr()->eq("p.coordinator",":coordinatorId")
        //         )
        //     );
        //     $queryBuilder->setParameter("coordinatorId", $search->coordinatorId);
        // }

        if ($search->formerId) {
            $queryBuilder->join('p.participations', 'fpa')
                        ->select('DISTINCT p')
                        ->join('fpa.formation', 'ff')
                        ->join('ff.formateurs', 'ffo')
                        ->andWhere('ffo.person = :former')
                        ->andWhere('fpa.archived = false')
                        ->setParameter(':former', $search->formerId);
        }

        if ($search->exerciseMode) {

            $exerciceModes = (array) $search->exerciseMode;

            $queryBuilder
                ->join('p.participations', 'epa')
                ->join('epa.formation', 'ef')
                ->andWhere('epa.archived = false')
                ->andWhere($queryBuilder->expr()->in("epa.exerciseMode", $exerciceModes))
                ;
        }


        $queryBuilder->leftJoin('p.participations', "parti");
        $queryBuilder->leftJoin('parti.formation', "forma");
        $queryBuilder->leftJoin('forma.programme', "prog");

        $queryBuilder->andWhere($queryBuilder->expr()->isNotNull('p.leadStatus'));

        $queryBuilder->addOrderBy('p.leadCreationDate', 'DESC');

        return $queryBuilder;
    }

    public function countByCategory(ParticipantSearch $search, $year = null) {
        $start = "$year-01-01";
        $end = "$year-12-31";

        return $this->createSearchResultsQueryBuilder($search)
            ->select('SUBSTRING(f.startDate, 6, 2) AS sMonth, p.category, count(DISTINCT p.id) as total')
            ->innerJoin('p.participations', 'pa')
            ->innerJoin('pa.formation', 'f')
            ->innerJoin('f.programme', 'pr')
            ->andWhere("f.startDate between :start and :end")
            ->setParameter('start', $start)
            ->setParameter('end', $end)
            ->addOrderBy("f.startDate")
            ->groupBy("sMonth")
            ->addGroupBy("p.category")
            ->getQuery()
            ->getScalarResult();
    }

    public function countByCategoryProspect() {

        return $this->createSearchResultsQueryBuilder(new ParticipantSearch())
            ->select('p.category, p.isProspect, count(DISTINCT p.id) as total')
            ->addGroupBy("p.category")
            ->addGroupBy("p.isProspect")
            ->getQuery()
            ->getScalarResult();
    }

    public function countPartAnalysis($year)
    {
        $start = ($year + 1) . "-01-01";
        return $this->createSearchResultsQueryBuilder(new ParticipantSearch())
            ->select('p.category, p.isProspect, c.id as coordinator, count(DISTINCT p.id) as total')
            ->leftJoin("p.coordinator", "c")
            ->addGroupBy("p.category")
            ->addGroupBy("p.isProspect")
            ->addGroupBy("c.id")
            ->andWhere("p.createdAt < :start")
            ->setParameter("start", $start)
            ->getQuery()
            ->getScalarResult();
    }

    public function findIds() {
        $qb = $this->createQueryBuilder('p')->select('p.id');
        return $qb->getQuery()->getScalarResult();
    }

    public function findParticipantsIds() {
        $qb = $this->createQueryBuilder('p')->select('p.id');
        $qb->where('p.isProspect = :prospect')->setParameter('prospect', 0);
        return $qb->getQuery()->getScalarResult();
    }

    public function findForLeadsImport(array $data) {
        $qb = $this->createQueryBuilder('p');

        $qb->andWhere(
            $qb->expr()->orX(
                $qb->expr()->eq("p.rpps",":rpps"),
                $qb->expr()->andX(
                    $qb->expr()->like("p.firstname", ':firstname'),
                    $qb->expr()->like("p.lastname", ':lastname'),
                )
            )
        )->setParameter('firstname', "%" . $data[LeadsService::FIELD_FIRSTNAME] . "%")
         ->setParameter('lastname', $data[LeadsService::FIELD_LASTNAME])
         ->setParameter('rpps', $data[LeadsService::FIELD_RPPS]);

        return $qb->getQuery()->getResult();
    }
}
