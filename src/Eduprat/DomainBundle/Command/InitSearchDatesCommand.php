<?php

namespace Eduprat\DomainBundle\Command;

use Doctrine\ORM\EntityManagerInterface;
use Eduprat\DomainBundle\Entity\Formation;
use Symfony\Component\Console\Attribute\AsCommand;
use Symfony\Component\Console\Command\Command;
use Symfony\Component\Console\Input\InputInterface;
use Symfony\Component\Console\Input\InputOption;
use Symfony\Component\Console\Output\OutputInterface;
use Symfony\Component\Console\Style\SymfonyStyle;

#[AsCommand('app:search:init-dates', description: '[OneShot] Commande pour définir le searchStartDate et searchEndDate')]
class InitSearchDatesCommand extends Command
{
    private EntityManagerInterface $entityManager;

    public function __construct(EntityManagerInterface $entityManager)
    {
        parent::__construct();
        $this->entityManager = $entityManager;
    }

    protected function configure(): void
    {
        $this
            ->addOption('force', null, InputOption::VALUE_NONE, 'forcer la modification de la base de données')
        ;
    }

    protected function execute(InputInterface $input, OutputInterface $output): int
    {
        $io = new SymfonyStyle($input, $output);

        $feRepo = $this->entityManager->getRepository(Formation::class);

        $debug = [];
        $formations = $feRepo->createQueryBuilder('f')
            ->andWhere('f.archived = false')
            ->orderBy('f.id')
            ->getQuery()
            ->getResult();

        $io->progressStart(count($formations));
        /** @var Formation $formation */
        foreach ($formations as $formation) {
            $formation->updateSearchDates();

            if ($input->getOption('force')) {
                $debug[] = [
                    'id' => $formation->getId(),
                    'searchStartDate' =>  $formation->getSearchStartDate(),
                    'searchEndDate' => $formation->getSearchEndDate(),
                    'querySearchStartDate' =>  $formation->getQuerySearchStartDate(),
                    'querySearchEndDate' => $formation->getQuerySearchEndDate(),
                ];
            } else {
                if ($formation->getSearchStartDate() && $formation->getSearchEndDate()) {
                    $debug[] = [
                        'id' => $formation->getId(),
                        'searchStartDate' =>  $formation->getSearchStartDate()->format(\DateTime::ATOM),
                        'searchEndDate' => $formation->getSearchEndDate()->format(\DateTime::ATOM),
                        'querySearchStartDate' =>  $formation->getQuerySearchStartDate()->format(\DateTime::ATOM),
                        'querySearchEndDate' => $formation->getQuerySearchEndDate()->format(\DateTime::ATOM),
                    ];
                }
            }
        }


        if (!$input->getOption('force')) {
            $io->table(
                ['Id', 'searchStartDate', 'searchEndDate', 'querySearchStartDate', 'querySearchEndDate'],
                $debug
            );
            $io->note(sprintf('%s formations à mettre à jour', count($debug)));
            $io->note('Utiliser --force pour lancer la commande');
        } else {
//            $io->note('Maj de la base');
            try {
                $connection = $this->entityManager->getConnection();
                $stmt = $connection->prepare("UPDATE formation SET searchStartDate = ?, searchEndDate = ?, querySearchStartDate = ?, querySearchEndDate = ? where id = ?");
                foreach ($debug as $d) {
                    $stmt->bindValue(1, $d['searchStartDate'], "datetime");
                    $stmt->bindValue(2, $d['searchEndDate'], "datetime");
                    $stmt->bindValue(3, $d['querySearchStartDate'], "datetime");
                    $stmt->bindValue(4, $d['querySearchEndDate'], "datetime");
                    $stmt->bindValue(5, $d['id']);
                    $resultSet = $stmt->executeQuery();
                    $io->progressAdvance();
                }
                $io->progressFinish();
                $io->success('Maj de la base.');
            } catch (\Exception $e) {
                $io->error('Erreur lors de la Maj de la base. '.$e->getMessage());
            }
        }
        return 0;
    }
}
