<?php

namespace Eduprat\DomainBundle\Command;

use Doctrine\ORM\EntityManagerInterface;
use Symfony\Component\Console\Attribute\AsCommand;
use Symfony\Component\Console\Command\Command;
use Symfony\Component\Console\Helper\Table;
use Symfony\Component\Console\Helper\TableSeparator;
use Symfony\Component\Console\Input\InputInterface;
use Symfony\Component\Console\Output\OutputInterface;
use Symfony\Component\Routing\Route;
use Symfony\Component\Routing\Router;
use Symfony\Component\Routing\RouterInterface;

#[AsCommand(name: 'eduprat:test:routes', description: 'Génération de routes de tests')]
class GenerateTestRoutesCommand extends Command
{
    /**
     * @var EntityManagerInterface
     */
    private $entityManager;

    /**
     * @var RouterInterface
     */
    private $router;

    public function __construct(EntityManagerInterface $entityManager, RouterInterface $router)
    {
        parent::__construct();
        $this->entityManager = $entityManager;
        $this->router = $router;
    }

    /**
     * {@inheritdoc}
     */
    protected function configure(): void
    {
    }

    /**
     * {@inheritdoc}
     */
    protected function execute(InputInterface $input, OutputInterface $output): int
    {
        $routes = $this->router->getRouteCollection();

        $table = new Table($output);
        $rows = array();

        $summary = null;

        /** @var Route $route */
        foreach ($routes as $routeName => $route) {

            try {
                if ($routeName[0] !== "_") {
                    $compiledRoute = $route->compile();
                    $method = explode("::", $route->getDefaults()["_controller"]);
                    $controller = ltrim(str_replace('\\Controller\\', '\\', strstr($method[0], '\\')), '\\');
                    $controller = str_replace("Bundle", "", $controller);
                    $controller = str_replace("Controller", "", $controller);
                    $r = new \ReflectionMethod($method[0], $method[1]);
                    $params = $r->getParameters();
                    $params = array_values(array_filter($params, function(\ReflectionParameter $r) { return $r->getName() !== "request"; }));
                    $routeParams = array();
                    foreach ($params as $index => $param) {
                        $name = ($compiledRoute->getVariables() && isset($compiledRoute->getVariables()[$index]) && $compiledRoute->getVariables()[$index] === "id") ? "id" : $param->getName();
                        if (in_array($name, $compiledRoute->getVariables()) || ($compiledRoute->getVariables() && $compiledRoute->getVariables()[0] === "id" && $index === 0)) {
                            if ($param->getClass()) {
                                if ($param->getClass()->getName() === \DateTime::class) {
                                    $value = date("Y-m-d");
                                } else {
                                    $value = $this->getLast($param)->getId();
                                }
                            } else {
                                $value = "{" . $name . "}";
                                if (in_array($name, array("page", "auditId", "patient", "quarter", "formId"))) {
                                    $value = 1;
                                } else if ($name === "year") {
                                    $value = date("Y");
                                } else if ($route->getRequirement($name)) {
                                    $value = explode("|", $route->getRequirement($name))[0];
                                }
                            }
                            $routeParams[$name] = $value;
                        }
                    }
                    $path = $this->router->generate($routeName, $routeParams, Router::ABSOLUTE_URL);
                    if ($summary !== $controller) {
                        if ($summary !== null) {
                            $rows[] = new TableSeparator();
                        }
                        $summary = $controller;
                    }
                    $rows[] = array($controller, $routeName, join(", ",$route->getMethods()), urldecode($path));

                }

            } catch (\Exception $exception) {
            }

        }

        $table
            ->setHeaders(["Controller", 'Route', 'Methods', 'URL'])
            ->setRows($rows)
        ;
        $table->render();

        return Command::SUCCESS;
    }

    public function getLast(\ReflectionParameter $reflection) {
        return $this->entityManager->getRepository($reflection->getClass()->getName())->findOneBy(array(), array('id' => 'desc'), array('limit' => 1));
    }

}
