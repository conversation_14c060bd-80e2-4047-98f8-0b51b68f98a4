<?php

namespace Eduprat\DomainBundle\Command;

use Doctrine\ORM\EntityManagerInterface;
use Eduprat\DomainBundle\Entity\Formation;
use Eduprat\DomainBundle\Entity\Programme;
use Symfony\Component\Console\Attribute\AsCommand;
use Symfony\Component\Console\Command\Command;
use Symfony\Component\Console\Input\InputInterface;
use Symfony\Component\Console\Input\InputOption;
use Symfony\Component\Console\Output\OutputInterface;
use Symfony\Component\Console\Style\SymfonyStyle;

#[AsCommand('app:elearning:fix-start-closingdate', description:'[OneShot] Maj des startDate et endDate des formations elearning qui avaient été modifié pour la recherche')]
class ElearningFixStartClosingdateCommand extends Command
{
    private EntityManagerInterface $entityManager;

    public function __construct(EntityManagerInterface $entityManager)
    {
        parent::__construct();
        $this->entityManager = $entityManager;
    }

    protected function configure(): void
    {
        $this
            ->addOption('force', null, InputOption::VALUE_NONE, 'à utiliser pour modifier la base')
        ;
    }

    protected function execute(InputInterface $input, OutputInterface $output): int
    {
        $io = new SymfonyStyle($input, $output);

        $feRepo = $this->entityManager->getRepository(Formation::class);

        $formationElearnings = $feRepo->createQueryBuilder('f')
            ->innerJoin("f.programme", 'p')
            ->andWhere('f.archived = false')
            ->andWhere('p.presence = :presence')
            ->setParameter('presence', Programme::PRESENCE_ELEARNING)
            ->orderBy('f.id')
            ->getQuery()
            ->getResult();

        $io->progressStart(count($formationElearnings));

        $debug = [];
        foreach ($formationElearnings as $formationElearning) {
            $startDateOld = $formationElearning->getStartDate();
            $endDateOld = $formationElearning->getEndDate();

            // formation elearning 1 unité
            if ($formationElearning->has1Unities()) {
                $formationElearning->setStartDate($formationElearning->getOpeningDate());
                $formationElearning->setEndDate($formationElearning->getClosingDate());
            } else { // formation elearning multiple unité
                $unity2 = $formationElearning->getUnityByPosition(2);
                if ($unity2) {
                    if (!$unity2->getOpeningDate() || !$unity2->getClosingDate()) {
                        $unity2->synchroDate();
                    }
                    $formationElearning->setStartDate($unity2->getOpeningDate());
                    $formationElearning->setEndDate($unity2->getClosingDate());
                }
            }

            if ($startDateOld != $formationElearning->getStartDate() ||
                $endDateOld != $formationElearning->getEndDate()) {
                if (!$input->getOption('force')) {
                    $startDate = $formationElearning->getStartDate()->format(\DateTime::ATOM);
                    $endDate = $formationElearning->getEndDate()->format(\DateTime::ATOM);
                } else {
                    $startDate = $formationElearning->getStartDate();
                    $endDate =  $formationElearning->getEndDate();
                }
                $debug[] = [
                    'id'            => $formationElearning->getId(),
                    'startDate'     => $startDate,
                    'endDate'       => $endDate,
                    'oldStartDate'  => $startDateOld->format(\DateTime::ATOM),
                    'oldEndDate'    => $endDateOld->format(\DateTime::ATOM),
                ];
            }
        }

        if (!$input->getOption('force')) {
            $io->table(
                ['Id', 'StartDate', 'EndDate', 'old StartDate', 'old EndDate'],
                $debug
            );
            $io->note(sprintf('%s formations à mettre à jour', count($debug)));
            $io->note('Utiliser --force pour lancer la commande');
        } else {
            $io->note('Maj de la base');
            try {
                $connection = $this->entityManager->getConnection();
                $stmt = $connection->prepare("UPDATE formation SET startDate = ?, endDate = ? where id = ?");
                foreach ($debug as $d) {
                    $stmt->bindValue(1, $d['startDate'], "datetime");
                    $stmt->bindValue(2, $d['endDate'], "datetime");
                    $stmt->bindValue(3, $d['id']);
                    $stmt->executeQuery();
                    $io->progressAdvance();
                }
                $io->progressFinish();
                $io->success('Maj de la base réalisée avec succès.');
            } catch (\Exception $e) {
                $io->error('Erreur lors de la Maj de la base. '.$e->getMessage());
            }
        }

        return 0;
    }
}