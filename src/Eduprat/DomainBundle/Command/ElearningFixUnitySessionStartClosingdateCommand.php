<?php

namespace Eduprat\DomainBundle\Command;

use Doctrine\ORM\EntityManagerInterface;
use Eduprat\DomainBundle\Entity\UnitySession;
use Eduprat\DomainBundle\Entity\UnitySessionDate;
use Symfony\Component\Console\Attribute\AsCommand;
use Symfony\Component\Console\Command\Command;
use Symfony\Component\Console\Input\InputInterface;
use Symfony\Component\Console\Input\InputOption;
use Symfony\Component\Console\Output\OutputInterface;
use Symfony\Component\Console\Style\SymfonyStyle;

#[AsCommand(name: 'app:elearning:fix-unity-session-start-closingdate', description: 'Maj des OpeningDate et ClosingDate des unitySession sans valeur')]
class ElearningFixUnitySessionStartClosingdateCommand extends Command
{
    private EntityManagerInterface $entityManager;

    public function __construct(EntityManagerInterface $entityManager)
    {
        parent::__construct();
        $this->entityManager = $entityManager;
    }

    protected function configure(): void
    {
        $this
            ->addOption('force', null, InputOption::VALUE_NONE)
        ;
    }

    protected function execute(InputInterface $input, OutputInterface $output): int
    {
        $io = new SymfonyStyle($input, $output);

        $unitySessionToUpdate = $this->findAllUnitySessionWithoutDateButUnitySessionDate();
        $io->progressStart();

        $debug = [];
        array_map(function (UnitySession $unitySession) use ($io, &$debug) {
            $unitySession->synchroDate();
            $debug[] = [
                $unitySession->getId(),
                $unitySession->getOpeningDate()->format(\DateTime::ATOM),
                $unitySession->getClosingDate()->format(\DateTime::ATOM),
            ];
            $io->progressAdvance();
            return $unitySession;
        }, $unitySessionToUpdate);
        $io->progressFinish();

        if (!$input->getOption('force')) {
            $io->table(
                ['Id', 'OpeningDate', 'ClosingDate'],
                $debug
            );
        } else {
            $io->note('Maj de la base');
            try {
                $this->entityManager->flush();
                $io->success('Maj de la base.');
            } catch (\Exception $e) {
                $io->error('Erreur lors de la Maj de la base. '.$e->getMessage());
            }
        }

        return 0;
    }

    public function findAllUnitySessionWithoutDateButUnitySessionDate(): array
    {
        $repoUnitySession = $this->entityManager->getRepository(UnitySession::class);
        $repoUnitySessionDate = $this->entityManager->getRepository(UnitySessionDate::class);
        $qbUSD = $repoUnitySessionDate->createQueryBuilder('usd')
            ->select('DISTINCT(usd.unitySession)');
        $qb = $repoUnitySession->createQueryBuilder('u');
        return $qb
            ->select('u')
            ->where('u.openingDate is NULL')
            ->andWhere($qb->expr()->in('u.id', $qbUSD->getDQL()))
            ->getQuery()
            ->getResult()
        ;
    }
}