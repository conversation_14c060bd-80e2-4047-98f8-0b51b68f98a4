<?php

namespace Eduprat\DomainBundle\Command;

use Doctrine\ORM\EntityManagerInterface;
use Eduprat\AdminBundle\Entity\Person;
use Eduprat\DomainBundle\Entity\Participant;
use Eduprat\DomainBundle\Entity\Programme;
use Eduprat\DomainBundle\Services\SendinBlueManager;
use Symfony\Component\Console\Attribute\AsCommand;
use Symfony\Component\Console\Command\Command;
use Symfony\Component\Console\Input\InputInterface;
use Symfony\Component\Console\Output\OutputInterface;
use Symfony\Component\Console\Helper\ProgressBar;

#[AsCommand('eduprat:sib:init')]
class InitilizeSibContactsCommand extends Command
{
    /**
     * @var EntityManagerInterface
     */
    private $entityManager;

    /**
     * @var SendinBlueManager
     */
    private $sendinBlueManager;


    public function __construct(EntityManagerInterface $entityManager, SendinBlueManager $sendinBlueManager)
    {
        parent::__construct();
        $this->entityManager = $entityManager;
        $this->sendinBlueManager = $sendinBlueManager;
    }

    /**
     * {@inheritdoc}
     */
    protected function configure(): void
    {
        $this
            ->setDescription('Initialisation des contacts dans Sendinblue')
        ;
    }

    /**
     * {@inheritdoc}
     */
    protected function execute(InputInterface $input, OutputInterface $output): int
    {
        $participantRepository = $this->entityManager->getRepository(Participant::class);
        $participants = $participantRepository->findIds();

        $progressBar = new ProgressBar($output, count($participants));
        $progressBar->start();

        foreach (array_column($participants, "id") as $index => $id) {
            /** @var Programme $programme */
            $this->sendinBlueManager->updateParticipant($participantRepository->find($id));

            if ($index % 10 === 0) {
                $this->entityManager->clear();
            }

            $progressBar->advance();
        }

        $this->entityManager->clear();

        $progressBar->finish();

        $personRepository = $this->entityManager->getRepository(Person::class);
        $persons = $personRepository->findCoordinatorIds();

        $progressBar = new ProgressBar($output, count($persons));
        $progressBar->start();

        foreach (array_column($persons, "id") as $index => $id) {
            /** @var Programme $programme */
            $this->sendinBlueManager->updateCoordinateur($personRepository->find($id));

            if ($index % 10 === 0) {
                $this->entityManager->clear();
            }

            $progressBar->advance();
        }

        $this->entityManager->clear();

        $progressBar->finish();
        return Command::SUCCESS;
    }

}
