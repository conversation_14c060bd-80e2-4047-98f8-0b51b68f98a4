<?php

namespace Eduprat\DomainBundle\Command;

use Symfony\Component\Console\Attribute\AsCommand;
use Doctrine\ORM\EntityManagerInterface;
use Eduprat\AdminBundle\Entity\Person;
use Eduprat\DomainBundle\Entity\Participant;
use Eduprat\DomainBundle\Services\SendinBlueManager;
use Symfony\Component\Console\Command\Command;
use Symfony\Component\Console\Input\InputInterface;
use Symfony\Component\Console\Output\OutputInterface;

#[AsCommand(name: 'eduprat:sib:update', description: 'Met à jour des contacts dans Sendinblue')]
class UpdateSibContactsCommand extends Command
{
    /**
     * @var EntityManagerInterface
     */
    private $entityManager;

    /**
     * @var SendinBlueManager
     */
    private $sendinBlueManager;


    public function __construct(EntityManagerInterface $entityManager, SendinBlueManager $sendinBlueManager)
    {
        parent::__construct();
        $this->entityManager = $entityManager;
        $this->sendinBlueManager = $sendinBlueManager;
    }

    /**
     * {@inheritdoc}
     */
    protected function configure(): void
    {
    }

    /**
     * {@inheritdoc}
     */
    protected function execute(InputInterface $input, OutputInterface $output): int
    {
        $participants = $this->findParticipantsToUpdateInSib();
        $coordinators = $this->findCoordinatorsToUpdateInSib();

        foreach ($participants as $participant) {
            try {
                $this->sendinBlueManager->updateParticipant($participant);
                $participant->setToUpdateSib(false);
            } catch (\Exception $e) {
                $output->writeln($e->getMessage());
            }
            usleep(100*1000);
        }
        $this->entityManager->flush();

        foreach ($coordinators as $coordinator) {
            try {
                $this->sendinBlueManager->updateCoordinateur($coordinator);
                $coordinator->setToUpdateSib(false);
            } catch (\Exception $e) {
                $output->writeln($e->getMessage());
            }
            usleep(100*1000);
        }
        $this->entityManager->flush();
        return Command::SUCCESS;
    }

    public function findParticipantsToUpdateInSib(): array
    {
        $participantRepository = $this->entityManager->getRepository(Participant::class);
        $qb = $participantRepository->createQueryBuilder('p');
        return $qb
            ->select('p')
            ->where('p.toUpdateSib = true')
            ->getQuery()
            ->getResult()
        ;
    }

    public function findCoordinatorsToUpdateInSib(): array
    {
        $personRepository = $this->entityManager->getRepository(Person::class);
        $qb = $personRepository->createQueryBuilder('p');
        return $qb
            ->select('p')
            ->where('p.toUpdateSib = true')
            ->andWhere('p.roles like :coordinator')
            ->setParameter(':coordinator', '%\"ROLE_COORDINATOR\"%')
            ->getQuery()
            ->getResult()
        ;
    }

}
