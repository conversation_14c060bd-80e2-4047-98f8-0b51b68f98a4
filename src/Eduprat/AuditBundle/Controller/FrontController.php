<?php

namespace Eduprat\AuditBundle\Controller;

use Symfony\Component\HttpFoundation\Response;
use Doctrine\ORM\OptimisticLockException;
use Doctrine\Persistence\ObjectManager;
use Alienor\ApiBundle\Services\FlashMessages;
use Doctrine\ORM\EntityManagerInterface;
use Eduprat\AdminBundle\Entity\Person;
use Eduprat\AdminBundle\Form\EditPasswordType;
use Eduprat\AdminBundle\Services\UserManager;
use Eduprat\AuditBundle\Form\ProfileType;
use Eduprat\AuditBundle\Services\CourseManager;
use Eduprat\AuditBundle\Services\ParticipationAccessManager;
use Eduprat\DomainBundle\Controller\EdupratController;
use Eduprat\DomainBundle\Entity\FAQ;
use Eduprat\DomainBundle\Entity\FormationPresentielle;
use Eduprat\DomainBundle\Entity\Participant;
use Eduprat\DomainBundle\Entity\ParticipantActionSheet;
use Eduprat\DomainBundle\Entity\Participation;
use Eduprat\DomainBundle\Entity\ParticipationLog;
use Eduprat\DomainBundle\Entity\Programme;
use Eduprat\DomainBundle\Services\AttestationService;
use Eduprat\DomainBundle\Services\EmailSender;
use Eduprat\DomainBundle\Services\ParticipationLogger;
use Eduprat\DomainBundle\Services\SearchHandler;
use Symfony\Component\HttpFoundation\RedirectResponse;
use Symfony\Component\Routing\Attribute\Route;
use Symfony\Component\Form\Extension\Core\Type\FormType;
use Symfony\Component\Form\FormErrorIterator;
use Symfony\Component\Form\FormFactoryInterface;
use Symfony\Component\HttpFoundation\JsonResponse;
use Symfony\Component\HttpFoundation\Request;
use Symfony\Component\HttpKernel\Exception\AccessDeniedHttpException;
use Symfony\Component\Validator\Constraints\File;
use Vich\UploaderBundle\Form\Type\VichFileType;
use Vich\UploaderBundle\Handler\DownloadHandler;
use Symfony\Component\Form\FormBuilderInterface;
use Symfony\Component\HttpFoundation\BinaryFileResponse;

class FrontController extends EdupratController
{

    private ParticipationLogger $participationLogger;

    public function __construct(FlashMessages $flashMessages, SearchHandler $searchHandler, ParticipationLogger $participationLogger)
    {
        parent::__construct($flashMessages, $searchHandler);
        $this->participationLogger = $participationLogger;
    }

    /**
     * @param Request $request
     * @return Response
     */
    #[Route(path: '/formations/{year}', name: 'eduprat_front_formations')]
    public function index(Request $request, $year, ParticipationAccessManager $accessManager, FormFactoryInterface $formFactory, EmailSender $emailSender, ParticipationLogger $participationLogger, CourseManager $courseManager, EntityManagerInterface $entityManager)
    {
        /** @var Participant $participant */
        $participant = $this->getUser()->getParticipant();

        if (is_null($participant)) {
            return $this->redirectToRoute('admin_index');
        }

        $participations = $entityManager->getRepository(Participation::class)->findByYearForParticipant($participant, $year);

        $participantActionSheetForms = [];
        $participantActionSheetList = [];
        $formsAttestationHonneurViews = [];
        $formsAttestationHonneurN1Views = [];
        $courses = [];

        foreach($participations as $participation) {

            $participantActionSheetRepository = $entityManager->getRepository(participantActionSheet::class);
            $participantActionSheet = $participantActionSheetRepository->findOneBy(array(
                'participation' => $participation
            ));

            if(!$participantActionSheet) {
                $participantActionSheet = new ParticipantActionSheet();
                $participantActionSheet->setParticipation($participation);
            } else {
                $participantActionSheet = $participantActionSheet;
            }

            $form_participant_action_new_view = $this->drawUploadParticipantActionForm($request, $participantActionSheet, 'participantActionFile', $formFactory);
            if ($form_participant_action_new_view->isSubmitted() && $form_participant_action_new_view->isValid()
            ) {
                $filename = null;
                foreach($request->files as $file) {
                    $filename = $file['participantActionFile']['file']->getClientOriginalName();
                }

                if($filename) {
                    $participantActionSheet->setParticipantActionOriginalName($filename);
                }

                $entityManager->persist($participantActionSheet);
                $entityManager->flush();

                $participationLogger->addLog($participation, ParticipationLog::ACTION_UPLOAD_ACTION);

                $this->flashMessages->addSuccess('admin.formation.upload.success');
                return $this->redirectToRoute('eduprat_front_formations', array('year' => $year));
            }

            $participantActionSheetForms[$participation->getId()] = $form_participant_action_new_view->createView();
            $participantActionSheetList[$participation->getId()] = $participantActionSheet;
            $courses[$participation->getId()] = $courseManager->getCourseDetail($participation);

            if (!$participation->getFormation()->getProgramme()->isFormatPresentiel()) {
                $formAttestationHonneur = $this->drawUploadAttestationForm($request, 'attestationHonneurFile', $participation, $formFactory);
                $formAttestationHonneurN1 = $this->drawUploadAttestationForm($request, 'attestationHonneurFileN1', $participation, $formFactory);

                if (($formAttestationHonneur->isSubmitted() && $formAttestationHonneur->isValid())
                    || ($formAttestationHonneurN1->isSubmitted() && $formAttestationHonneurN1->isValid())) {
                    $this->onAttestationUpdatedSuccefully($participation, $emailSender, $formAttestationHonneurN1->isSubmitted() && $formAttestationHonneurN1->isValid(), $entityManager);
                }
                $formsAttestationHonneurViews[$participation->getId()] = $formAttestationHonneur->createView();
                if ($participation->getFormation()->isPluriAnnuelle()) {
                    $formsAttestationHonneurN1Views[$participation->getId()] = $formAttestationHonneurN1->createView();
                }
            }
        }
        $lastStepsCompleted = [];
        foreach ($courses as $course) {
            $lastStepsCompleted[] = $course ? last($course)['completed'] : false;
        }

        return $this->render('audit/front/formations.html.twig', array(
            'year' => $year,
            'participant' => $participant,
            'participations' => $participations,
            'participationFrom' => $participantActionSheetForms,
            'participantActionSheetList' => $participantActionSheetList,
            'accessManager' => $accessManager,
            'courses' => $courses,
            'courseManager' => $courseManager,
            "forms_attestation_honneur" => $formsAttestationHonneurViews,
            "forms_attestation_honneur_n1" => $formsAttestationHonneurN1Views,
            "lastStepsCompleted" => $lastStepsCompleted,
        ));
    }

    #[Route(path: '/formation-files/{id}', name: 'eduprat_front_formation_files')]
    public function formationFiles(Participation $participation, ParticipationAccessManager $accessManager): Response
    {
        if ($participation->getParticipant()->getUser() !== $this->getUser()) {
            throw new AccessDeniedHttpException();
        }

        return $this->render('audit/front/files.html.twig', array(
            'participation' => $participation,
            'accessManager' => $accessManager,
        ));
    }

    /**
     * @param Request $request
     * @return Response
     */
    #[Route(path: '/profile', name: 'eduprat_front_profile')]
    public function profile(Request $request, FormFactoryInterface $formFactory, UserManager $userManager, EntityManagerInterface $em)
    {
        /** @var Participant $participant */
        $participant = $this->getUser()->getParticipant();

        if (is_null($participant)) {
            return $this->redirectToRoute('admin_index');
        }

        $form = $this->createForm(ProfileType::class, $participant, array("em" => $em));

        $formPassword = $formFactory->create(EditPasswordType::class, $participant->getUser(), array(
            'validation_groups' => array('password_edit'),
        ));
        $formPassword->handleRequest($request);

        if ($formPassword->isSubmitted() && $formPassword->isValid()) {
            /** @var Person $user */
            $user = $this->getUser();
            $newPassword = $formPassword->getData()->getPlainPassword();
            $user->setPlainPassword($newPassword);
            $user->setHasCreatedPassword(true);
            $userManager->updateUser($user);
            $this->flashMessages->addSuccess('user.resetted.success');
            return $this->redirectToRoute('eduprat_front_profile');
        }

        return $this->render('audit/front/profile.html.twig', array(
            'participant' => $participant,
            'form' => $form,
            'formPassword' => $formPassword,
        ));
    }

    /**
     * @param Request $request
     * @return Response
     */
    #[Route(path: '/contact', name: 'eduprat_front_contact')]
    public function contact(EntityManagerInterface $em): Response
    {
        $faqs = $em->getRepository(FAQ::class)->findBy(array(), array('position' => 'ASC'));
        return $this->render('audit/front/contact.html.twig', array(
            "faqs" => $faqs
        ));
    }

    /**
     * @param Request $request
     * @return Response
     */
    #[Route(path: '/gdpr', name: 'eduprat_front_gdpr')]
    public function gdpr(Request $request, EntityManagerInterface $entityManager): JsonResponse
    {
        $acceptEmail = filter_var($request->request->get('email'), FILTER_VALIDATE_BOOLEAN);
        $acceptPost = filter_var($request->request->get('post'), FILTER_VALIDATE_BOOLEAN);
        $acceptCall = filter_var($request->request->get('call'), FILTER_VALIDATE_BOOLEAN);

        /** @var Participant $participant */
        $participant = $this->getUser()->getParticipant();

        if (is_null($participant)) {
            return new JsonResponse(array('status' => 'ok'));
        }

        $history = $participant->getGdprAgreementHistory();

        if (is_null($history)) {
            $history = array();
        }

        $now = (new \DateTime())->format('Y-m-d H:i:s');
        $history[] = array(
            "date" => $now,
            "email" => $acceptEmail,
            "post" => $acceptPost,
            "call" => $acceptCall,
        );

        $participant->setGdprAgreement($acceptEmail);
        $participant->setGdprAgreementPost($acceptPost);
        $participant->setGdprAgreementCall($acceptCall);
        $participant->setGdprAgreementHistory($history);
        $entityManager->persist($participant);
        $entityManager->flush();

        return new JsonResponse(array('status' => 'ok'));
    }

    /**
     * @param Request $request
     * @return Response
     */
    #[Route(path: '/notification', name: 'eduprat_front_notification')]
    public function notification(Request $request, EntityManagerInterface $entityManager): JsonResponse
    {

        $notifInscription = filter_var($request->request->get('notifInscription'), FILTER_VALIDATE_BOOLEAN);
        $notifSurveyOpen = filter_var($request->request->get('notifSurveyOpen'), FILTER_VALIDATE_BOOLEAN);
        $notifRemindSurvey = filter_var($request->request->get('notifRemindSurvey'), FILTER_VALIDATE_BOOLEAN);
        $notifRemindSession = filter_var($request->request->get('notifRemindSession'), FILTER_VALIDATE_BOOLEAN);
        $notifNewSession = filter_var($request->request->get('notifNewSession'), FILTER_VALIDATE_BOOLEAN);
        $notifSessionChange = filter_var($request->request->get('notifSessionChange'), FILTER_VALIDATE_BOOLEAN);

        /** @var Participant $participant */
        $participant = $this->getUser()->getParticipant();

        if (is_null($participant)) {
            return new JsonResponse(array('status' => 'ok'));
        }

        $participant->setNotifInscription($notifInscription);
        $participant->setNotifSurveyOpen($notifSurveyOpen);
        $participant->setNotifRemindSurvey($notifRemindSurvey);
        $participant->setNotifRemindSession($notifRemindSession);
        $participant->setNotifNewSession($notifNewSession);
        $participant->setNotifSessionChange($notifSessionChange);
        $entityManager->persist($participant);
        $entityManager->flush();

        return new JsonResponse(array('status' => 'ok'));
    }

    /**
     * @param Request $request
     * @param EntityManagerInterface $entityManager
     * @return Response
     */
    #[Route(path: '/gdpr-notification', name: 'eduprat_front_gdpr_notification')]
    public function gdprNotification(EntityManagerInterface $entityManager): JsonResponse
    {
        /** @var Participant $participant */
        $participant = $this->getUser()->getParticipant();

        if (is_null($participant)) {
            return new JsonResponse(array('status' => 'ok'));
        }

        $history = $participant->getGdprAgreementHistory();

        if (is_null($history)) {
            $history = array();
        }

        $now = (new \DateTime())->format('Y-m-d H:i:s');
        $history[] = array(
            "date" => $now,
            "notification" => true,
        );

        $participant->setGdprAgreementHistory($history);
        $entityManager->persist($participant);
        $entityManager->flush();

        return new JsonResponse(array('status' => 'ok'));
    }

    /**
     * @param Participation $participation
     * @param Request $request
     * @return Response
     * @throws OptimisticLockException
     */
    #[Route(path: '/elearning-validate/{id}', name: 'eduprat_front_elearning_validate')]
    public function elearningValidate(Participation $participation, EntityManagerInterface $em): JsonResponse
    {
        if ($participation->getParticipant()->getUser() !== $this->getUser()) {
            throw new AccessDeniedHttpException();
        }

        $participation->setElearningPhaseDone(true);
        $em->persist($participation);
        $em->flush();

        return new JsonResponse(array('status' => 'ok'));
    }

    #[Route(path: '/profile/edit', methods: ['POST'], name: 'eduprat_front_profile_edit')]
    public function edit(Request $request, EntityManagerInterface $em): JsonResponse
    {
        /** @var Participant $participant */
        $participant = $this->getUser()->getParticipant();

        $form = $this->createForm(ProfileType::class, $participant, array("em" => $em));

        foreach ($request->request->all() as $key => $value) {
//            $form->get($key)->setData($value);

//            $setter = "set" . ucfirst($key);
//            if (method_exists($participant, $setter)) {
//                call_user_func_array( array($participant, $setter), array($value));
//            }
        }

        $form->handleRequest($request);

        if ($form->isSubmitted() && $form->isValid()) {
            $em->persist($participant);
            $em->flush();
            return new JsonResponse(array("status" => "ok"));
        }

        $errors = $form->getErrors(true, false);
        if (count($errors) > 0) {
            $errorsString = array();
            /** @var FormErrorIterator $errorIterator */
            foreach ($errors as $errorIterator) {
                foreach ($errorIterator as $item) {
                    $errorsString[] = $item->getMessage();
                }
            }
            return new JsonResponse(array("status" => "ko", "errors" => $errorsString), Response::HTTP_BAD_REQUEST);
        }

        return new JsonResponse(array("status" => "ko"));
    }

    /**
     * @param Request $request
     * @return Response
     */
    #[Route(path: '/tutorial', name: 'eduprat_front_tutorial')]
    public function tutorial(ParticipationAccessManager $accessManager)
    {
        /** @var Participant $participant */
        $participant = $this->getUser()->getParticipant();

        if (is_null($participant)) {
            return $this->redirectToRoute('admin_index');
        }

        $participation1 = $this->generateParticipation($participant, array(
            "title" => "Les éruptions cutanées chez l'enfant",
            "startDate" => new \DateTime("today + 3 days"),
            "endDate" => new \DateTime("today + 3 days"),
            "reference" => "57201700144",
            "sessionNumber" => 1,
            "openingDate" => new \DateTime("today - 3 days"),
            "closingDate" => new \DateTime("today + 21 days"),
            "closed" => false,
        ));

        $participation2 = $this->generateParticipation($participant, array(
            "title" => "La Dysthyroïdie",
            "startDate" => new \DateTime("today - 3 days"),
            "endDate" => new \DateTime("today - 3 days"),
            "reference" => "57201700144",
            "sessionNumber" => 1,
            "openingDate" => new \DateTime("today - 15 days"),
            "closingDate" => new \DateTime("today + 15 days"),
            "closed" => false,
        ));

        $participation3 = $this->generateParticipation($participant, array(
            "title" => "Conduite à tenir devant une anémie",
            "startDate" => new \DateTime("today - 15 days"),
            "endDate" => new \DateTime("today - 15 days"),
            "reference" => "57201700144",
            "sessionNumber" => 1,
            "openingDate" => new \DateTime("today - 30 days"),
            "closingDate" => new \DateTime("today - 15 days"),
            "closed" => true,
        ));

        $participation4 = $this->generateParticipation($participant, array(
            "title" => "La contraception",
            "startDate" => new \DateTime("today - 15 days"),
            "endDate" => new \DateTime("today - 15 days"),
            "reference" => "57201700144",
            "sessionNumber" => 1,
            "openingDate" => new \DateTime("today - 30 days"),
            "closingDate" => new \DateTime("today - 15 days"),
            "closed" => true,
        ));

        $participationsVars = array(
            array(
                "isCompleted1" => false,
                "isCompleted2" => false,
                "canAnswer1" => true,
                "canAnswer2" => false,
                "canAnswerEvaluation" => false,
                "isEvaluationCompleted" => false
            ),
            array(
                "isCompleted1" => true,
                "isCompleted2" => false,
                "canAnswer1" => false,
                "canAnswer2" => true,
                "canAnswerEvaluation" => false,
                "isEvaluationCompleted" => false
            ),
            array(
                "isCompleted1" => true,
                "isCompleted2" => true,
                "canAnswer1" => false,
                "canAnswer2" => false,
                "canAnswerEvaluation" => true,
                "isEvaluationCompleted" => false
            ),
            array(
                "isCompleted1" => true,
                "isCompleted2" => true,
                "canAnswer1" => false,
                "canAnswer2" => false,
                "canAnswerEvaluation" => true,
                "isEvaluationCompleted" => true
            ),
        );

        $participations = array($participation1, $participation2, $participation3, $participation4);

        return $this->render('audit/front/tutorial.html.twig', array(
            'year' => (new \DateTime())->format('Y'),
            'tutorial' => true,
            'participant' => $participant,
            'participations' => $participations,
            'participationsVars' => $participationsVars,
            'accessManager' => $accessManager
        ));
    }

    /**
     * @param Request $request
     * @return Response
     */
    #[Route(path: '/tutorial-completed', name: 'eduprat_front_tutorial_completed')]
    public function tutorialCompleted(EntityManagerInterface $em): RedirectResponse
    {
        /** @var Participant $participant */
        $participant = $this->getUser()->getParticipant();
        if (is_null($participant)) {
            return $this->redirectToRoute('admin_index');
        }
        $participant->setHasDoneTutorial(true);
        $em->persist($participant);
        $em->flush();
        return $this->redirectToRoute("eduprat_front_formations", array('year' => (new \DateTime())->format('Y')));
    }

    /**
     * @param Request $request
     * @return Response
     */
    #[Route(path: '/donnees-personnelles', name: 'eduprat_front_personal_data')]
    public function personalData(): Response
    {
        return $this->render('audit/front/personal-data.html.twig');
    }

    /**
     * @param Request $request
     * @return Response
     */
    #[Route(path: '/mentions-legales', name: 'eduprat_front_legals')]
    public function legals(): Response
    {
        return $this->render('audit/front/legals.html.twig', array(
            'tutorial' => true,
        ));
    }

    /**
     * @param $participant
     * @param $parameters
     * @return Participation
     */
    protected function generateParticipation($participant, $parameters)
    {
        $programme = new Programme();
        $programme->setTitle($parameters["title"]);
        $programme->setReference($parameters["reference"]);


        $formation = new FormationPresentielle();
        $formation
            ->setSessionNumber($parameters["sessionNumber"])
            ->setOpeningDate($parameters["openingDate"])
            ->setClosingDate($parameters["closingDate"])
            ->setClosed($parameters["closed"])
            ->setStartDate($parameters["startDate"])->setEndDate($parameters["endDate"])
        ;

        $formation->setProgramme($programme);
        $participation = new Participation();
        $participation->setFormation($formation);
        $participation->setParticipant($participant);
        $participation->setFormation($formation);
        $formation->addParticipation($participation);
        $programme->addFormation($formation);
        return $participation;
    }

    public function drawUploadParticipantActionForm(Request $request, ParticipantActionSheet $participantActionSheet, $fieldName, $formFactory) {
        $genForm = $formFactory->createNamedBuilder($fieldName.'_'.$participantActionSheet->getParticipation()->getId(), FormType::class, $participantActionSheet)
            ->add($fieldName, VichFileType::class, array(
                'label' => false,
                'required' => false,
                'download_uri' => false,
                'allow_delete' => false,
                'translation_domain' => 'messages',
                'error_bubbling' => true,
                'constraints' => new File(
                    array(
                        'mimeTypes' => array(
                            'application/vnd.ms-excel',
                            'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet',
                            'application/vnd.oasis.opendocument.spreadsheet',
                            'application/xml'
                        ),
                        'mimeTypesMessage' => 'participationActionSheet.mimeTypes'
                    )
                )
            ));
            $form = $genForm->getForm();
            $form->handleRequest($request);

        return $form;
    }

    public function drawUploadAttestationForm(Request $request, $fieldName, Participation $participation, $formFactory) {
        $genForm = $formFactory->createNamedBuilder($fieldName.'_'.$participation->getId(), FormType::class, $participation);
        $this->drawUploadFormField($genForm, $fieldName, false, "12M");
        $form = $genForm->getForm();
        $form->handleRequest($request);
        return $form;
    }

    public function drawUploadFormField(FormBuilderInterface $builder, $fieldName, $allow_delete = true, $maxSize="128M") {
        $builder->add($fieldName, VichFileType::class, array(
            'label' => false,
            'required' => false,
            'download_uri' => false,
            'allow_delete' => $allow_delete,
            'error_bubbling' => true,
            'constraints' => new File(
                array(
                    'mimeTypes' => array(
                        'image/png', 'image/jpeg', 'application/pdf', 'application/x-pdf'
                    ),
                    'mimeTypesMessage' => 'formation.mimeTypesAttestation',
                    'maxSize' => $maxSize
                )
            )
        ));
    }

    #[Route(path: '/{id}/attestation-file/{n1}', name: 'front_attestation_file', defaults: ['n1' => false])]
    public function attestationFileDownload(Participation $participation, $n1 = false): BinaryFileResponse
    {
        if($participation->getParticipant()->getUser() == $this->getUser()) {
            if ($n1) {
                return new BinaryFileResponse($participation->getAttestationHonneurFileN1()->getRealPath());
            }
            return new BinaryFileResponse($participation->getAttestationHonneurFile()->getRealPath());
        }
        throw new AccessDeniedHttpException();
    }

    #[Route(path: '/{id}/attestation-deletefile/{n1}', name: 'front_attestation_delete_file', defaults: ['n1' => false])]
    public function attestationDeleteFileDownload(Request $request, Participation $participation, EntityManagerInterface $entityManager, AttestationService $attestationService, $n1 = false): RedirectResponse
    {
        if($participation->getParticipant()->getUser() == $this->getUser()) {
            $attestationService->deleteAttestation($n1, $participation);

            $entityManager->persist($participation);
            $entityManager->flush();

            $this->flashMessages->addSuccess('admin.participation.attestation_honneur.deleteSucess');
            return $this->redirect($request->headers->get('referer'));
        }
        throw new AccessDeniedHttpException();
    }


    #[Route(path: '/{id}/participantFile/{fileField}', name: 'participant_action_sheet')]
    public function participantActionSheetDownload(ParticipantActionSheet $participantActionSheet, $fileField, DownloadHandler $downloadHandler)
    {
        $getter = "getParticipantActionSheet";
        $filename = call_user_func(array($participantActionSheet, $getter));

        return $downloadHandler->downloadObject($participantActionSheet, $fileField, null, $filename, false);
    }

    #[Route(path: '/participant_end_action', name: 'participant_end_action')]
    public function participantEndCurrent(ParticipationLogger $participationLogger): JsonResponse
    {
        $participationLogger->endCurrentLog();
        return new JsonResponse(array("status" => "OK"));
    }

    #[Route(path: '/participant-logout', name: 'participant_end_action_logout')]
    public function participantLogout(ParticipationLogger $participationLogger): RedirectResponse
    {
        $participationLogger->endCurrentLog();
        return $this->redirectToRoute("eduprat_audit_logout");
    }

    /**
     * @param $participation
     * @param EmailSender $emailSender
     * @return ObjectManager
     */
    public function onAttestationUpdatedSuccefully(Participation $participation, EmailSender $emailSender, bool $n1, EntityManagerInterface $entityManager): void
    {
        $entityManager->persist($participation);
        $entityManager->flush();
        if ($participation->getCoordinator()) {
            $entityManager->refresh($participation);
            $emailSender->sendAttestationHonneurUploadedEmail($participation);
        }

        $this->participationLogger->addLog($participation, ParticipationLog::ACTION_UPLOAD_ATTESTATION_HONNEUR);

        $res = $n1 ? $participation->getAttestationHonneurN1() : $participation->getAttestationHonneur();
        if ($res) {
            $this->flashMessages->addSuccess('admin.participation.attestation_honneur.importSucess');
        } else {
            $this->flashMessages->addError('admin.participation.attestation_honneur.importError');
        }
    }
}
