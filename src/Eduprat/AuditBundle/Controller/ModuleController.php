<?php

namespace Eduprat\AuditBundle\Controller;

use Symfony\Component\HttpFoundation\Response;
use Symfony\Component\Form\FormBuilderInterface;
use Alienor\ApiBundle\Services\FlashMessages;
use DateTime;
use Doctrine\Common\Collections\ArrayCollection;
use Doctrine\ORM\EntityManagerInterface;
use Eduprat\AdminBundle\Http\CsvFileResponse;
use Eduprat\AuditBundle\Form\EtutoratType;
use Eduprat\AuditBundle\Form\FicheActionCollectionType;
use Eduprat\AuditBundle\Form\FicheProgressionCollectionType;
use Eduprat\AuditBundle\Services\CourseManager;
use Eduprat\AuditBundle\Services\ParticipationAccessManager;
use Eduprat\DomainBundle\Controller\EdupratController;
use Eduprat\DomainBundle\Entity\Etutorat;
use Eduprat\DomainBundle\Entity\EtutoratCasClinique;
use Eduprat\DomainBundle\Entity\FicheAction;
use Eduprat\DomainBundle\Entity\FicheProgression;
use Eduprat\DomainBundle\Entity\FicheSynthese;
use Eduprat\DomainBundle\Entity\FormationAudit;
use Eduprat\DomainBundle\Entity\Participation;
use Eduprat\DomainBundle\Entity\ParticipationLog;
use Eduprat\DomainBundle\Form\EtutoratCasCliniqueType;
use Eduprat\DomainBundle\Services\EmailSender;
use Eduprat\DomainBundle\Services\ParticipationLogger;
use Eduprat\DomainBundle\Services\RestitutionCalculator;
use Eduprat\DomainBundle\Services\SearchHandler;
use Symfony\Component\Form\Extension\Core\Type\FormType;
use Symfony\Component\Form\Extension\Core\Type\TextareaType;
use Symfony\Component\Form\FormFactoryInterface;
use Symfony\Component\HttpFoundation\JsonResponse;
use Symfony\Component\HttpFoundation\RedirectResponse;
use Symfony\Component\HttpKernel\Exception\NotFoundHttpException;
use Symfony\Component\Routing\Attribute\Route;
use Symfony\Component\HttpFoundation\Request;
use Symfony\Component\HttpKernel\Exception\AccessDeniedHttpException;
use Symfony\Bundle\SecurityBundle\Security;
use Symfony\Component\Validator\Constraints\File;
use Vich\UploaderBundle\Form\Type\VichFileType;

class ModuleController extends EdupratController
{

    private CourseManager $courseManager;
    private ParticipationLogger $participationLogger;
    private Security $security;

    public function __construct(FlashMessages $flashMessages, SearchHandler $searchHandler, CourseManager $courseManager, ParticipationLogger $participationLogger, Security $security)
    {
        parent::__construct($flashMessages, $searchHandler);
        $this->courseManager = $courseManager;
        $this->participationLogger = $participationLogger;
        $this->security = $security;
    }

    #[Route(path: '/formation-video-1/{id}', name: 'eduprat_front_module_video_1')]
    public function video1(Participation $participation, ParticipationAccessManager $accessManager): Response
    {
        if (!$this->canAccessToModules($participation)) {
            throw new AccessDeniedHttpException();
        }

        $module = CourseManager::STEP1_VIDEO_LABEL;

        $course = $this->courseManager->getCourseDetail($participation);
        $step = $course[CourseManager::STEP1];

        $this->participationLogger->startLog($participation, $module, null, $participation->isStepCompleted($module) ? 100 : 0);

        return $this->render("audit/module/video_1.html.twig", array(
            'participation' => $participation,
            'formation' => $participation->getFormation(),
            'accessManager' => $accessManager,
            'courseManager' => $this->courseManager,
            'course' => $course,
            'step' => $step,
            "current_module" => $module,
            'completed' => $participation->isStepCompleted($module)
        ));
    }

    #[Route(path: '/formation-video-1-validate/{id}', name: 'eduprat_front_module_video_1_validate')]
    public function video1Validate(Participation $participation, Request $request, EntityManagerInterface $entityManager, ParticipationAccessManager $accessManager): RedirectResponse
    {
        if (!$this->canAccessToModules($participation)) {
            throw new AccessDeniedHttpException();
        }

        $currentModule = CourseManager::STEP1_VIDEO_LABEL;
        if (!$participation->isStepCompleted($currentModule)) {
            if (!$participation->minModuleTimeReached($currentModule)) {
                $this->flashMessages->addError("front.error.minTime");
                return $this->redirectToRoute('eduprat_front_module_video_1', array("id" => $participation->getId()));
            }
            $this->courseManager->completeCourseStep($participation, $currentModule);
            $entityManager->persist($participation);
            $entityManager->flush();
        }
        $this->participationLogger->endLog($participation, $currentModule, null, 100);

        return $this->redirectToNextModule($participation, $currentModule, $request);
    }

    #[Route(path: '/formation-etutorat-1/{id}', name: 'eduprat_front_module_etutorat_1')]
    public function etutorat1(Participation $participation, Request $request, ParticipationAccessManager $accessManager, EntityManagerInterface $entityManager)
    {
        if (!$this->canAccessToModules($participation)) {
            throw new AccessDeniedHttpException();
        }

        $module = CourseManager::STEP1_ETUTORAT_LABEL;

        $course = $this->courseManager->getCourseDetail($participation);
        $step = $course[CourseManager::STEP1];

        $form = $this->createForm(EtutoratType::class, null, array(
            "participation" => $participation
        ));

        $programme = $participation->getFormation()->getProgramme();
        $hasEtutorat = $programme->hasEtutorat();

        // Si déjà répondu, on redirige directement vers l'étape cas clinique
        if ($hasEtutorat && $participation->getEtutorats()->count() > 0) {
            return $this->redirectToRoute('eduprat_front_module_etutorat_cas_clinique', array("id" => $participation->getId()));
        }

        $isPredefined = $participation->getFormation()->isPredefined();
        $progression = $participation->isStepCompleted($module) ? 100 : 0;
        $stringProgression = null;
        if ($hasEtutorat) {
            $stringProgression = $participation->isStepCompleted($module) ? "2/2" : "0/2";
        }

        $this->participationLogger->startLog($participation, $module, null, $participation->isStepCompleted($module) ? $progression : 0, $stringProgression);
        $isWithoutEtutoratCasClinique = $participation->getFormation()->isElearning() && !$participation->getFormation()->containPresentiel();
        $form->handleRequest($request);

        if ($form->isSubmitted() && $form->isValid()) {
            $progression = $hasEtutorat ? 50 : 100;
            $stringProgression = $hasEtutorat ? "1/2" : null;
            $timeCompleted = true;
            if ($isWithoutEtutoratCasClinique) {
                $timeCompleted = $participation->minModuleTimeReached($module);
            }
            if (!$timeCompleted) {
                $this->flashMessages->addError("front.error.minTime");
            } else {

                foreach ($form->all() as $item) {
                    if ($item->getData() instanceof Etutorat) {
                        $entityManager->persist($item->getData());
                    }
                }

                $participation->setEtutoratAttentes($form->get("message")->getData());
                $participation->setFirstPageEtutoCompleted(true);
                $entityManager->flush();

                $entityManager->persist($participation);
                $entityManager->flush();
                if ($participation->getFormation()->isElearning() && !$participation->getFormation()->containPresentiel()) {
                    $this->courseManager->completeCourseStep($participation, $module);
                    $this->participationLogger->endLog($participation, $module, null, 100);
                    $entityManager->persist($participation);
                    $entityManager->flush();
                    return $this->redirectToNextModule($participation, $module, $request);
                }
                $this->participationLogger->endLog($participation, $module, null, $participation->isStepCompleted($module) ? 100 : $progression, $stringProgression);

                return $this->redirectToRoute('eduprat_front_module_etutorat_cas_clinique', array("id" => $participation->getId()));
            }
        }

        return $this->render("audit/module/etutorat_1.html.twig", array(
            'form' => $form,
            'hasEtutorat' => $hasEtutorat,
            'participation' => $participation,
            'formation' => $participation->getFormation(),
            'accessManager' => $accessManager,
            'courseManager' => $this->courseManager,
            'course' => $course,
            'step' => $step,
            "current_module" => $module,
            'completed' => $participation->isStepCompleted($module),
            'isWithoutEtutoratCasClinique' => $isWithoutEtutoratCasClinique,
        ));
    }

    #[Route(path: '/formation-etutorat-{stepId}/{id}', name: 'eduprat_front_module_etutorat', requirements: ['stepId' => '2|3'])]
    public function etutorat2(Participation $participation, Request $request, ParticipationAccessManager $accessManager, EntityManagerInterface $entityManager, EmailSender $emailSender, int $stepId = 2)
    {
        $stepId = (int) $stepId;

        if (!$this->canAccessToModules($participation)) {
            throw new AccessDeniedHttpException();
        }

        $module = $stepId === 2 ? CourseManager::STEP3_ETUTORAT_LABEL : CourseManager::STEP4_ETUTORAT_LABEL;

        $course = $this->courseManager->getCourseDetail($participation);
        $step = $this->courseManager->findStepModule($course, $module);

        $form = $this->createFormBuilder()
            ->add('message', TextareaType::class, array(
                "required" => false,
                "attr" => array("rows" => 6, "placeholder" => "Saisissez votre texte ici"),
            ))
            ->getForm();

        $form->handleRequest($request);

        $this->participationLogger->startLog($participation, $module);

        if ($form->isSubmitted() && $form->isValid()) {
            if (!$participation->minModuleTimeReached($module)) {
                $this->flashMessages->addError("front.error.minTime");
            } else {
                if ($message = $form->get("message")->getData()) {
                    if ($stepId == "2") {
                        $participation->setEtutorat2Message($message);
                    } else {
                        $participation->setEtutorat3Message($message);
                    }
                    $emailSender->sendEtutoratQuestionsFormateurs($stepId + 1, $participation, $message);
                }
    
                $this->courseManager->completeCourseStep($participation, $module);
                $entityManager->persist($participation);
                $entityManager->flush();
    
                $this->participationLogger->endLog($participation, $module, null, 100);
    
                return $this->redirectToNextModule($participation, $module, $request);
            }
        }

        return $this->render("audit/module/etutorat_question.html.twig", array(
            'form' => $form,
            'participation' => $participation,
            'formation' => $participation->getFormation(),
            'accessManager' => $accessManager,
            'courseManager' => $this->courseManager,
            'course' => $course,
            'step' => $step,
            "current_module" => $module,
            'completed' => $participation->isStepCompleted($module),
        ));
    }

    #[Route(path: '/formation-etutorat-cas-clinique/{id}', name: 'eduprat_front_module_etutorat_cas_clinique')]
    public function etutoratCasClinique(Participation $participation, Request $request, ParticipationAccessManager $accessManager, EntityManagerInterface $entityManager)
    {
        if (!$this->canAccessToModules($participation)) {
            throw new AccessDeniedHttpException();
        }

        $module = CourseManager::STEP1_ETUTORAT_LABEL;

        $course = $this->courseManager->getCourseDetail($participation);
        $step = $this->courseManager->findStepModule($course, $module);

        $casClinique = $participation->getEtutoratCasClinique() ?? new EtutoratCasClinique($participation);
        $form = $this->createForm(EtutoratCasCliniqueType::class, $casClinique);

        $form->handleRequest($request);
        $hasEtutorat = $participation->getFormation()->getProgramme()->hasEtutorat();
        $moduleComplete = $participation->isStepCompleted($module);
        $progression = $moduleComplete ? 100 : 50;
        $stringProgression = null;
        if ($hasEtutorat) {
            $stringProgression = $moduleComplete ? "2/2" : "1/2";
        }
        $this->participationLogger->startLog($participation, $module, null, $progression, $stringProgression);

        if ($form->isSubmitted() && $form->isValid()) {
            $timeCompleted = $participation->minModuleTimeReached($module);
            if (!$timeCompleted) {
                $this->flashMessages->addError("front.error.minTime");
                $form = $this->createForm(EtutoratCasCliniqueType::class, $casClinique);
            } else {
                $entityManager->persist($casClinique);
                $entityManager->flush();

                $this->courseManager->completeCourseStep($participation, $module);
                $entityManager->persist($participation);
                $entityManager->flush();

                $this->participationLogger->endLog($participation, $module, null, 100, "2/2");

                if (!$casClinique->isEmpty()) {
                    $this->flashMessages->addSuccess("front.success.etutorat_casclinique");
                }
                if (!$participation->getFormation()->isElearning() && new DateTime() < $participation->getFormation()->getStartDate()) {
                    $this->flashMessages->addSuccess("front.success.step1_end");
                }

                return $this->redirectToNextModule($participation, $module, $request);
            }
        }

        return $this->render("audit/module/etutorat_cas_clinique.html.twig", array(
            'form' => $form,
            'participation' => $participation,
            'formation' => $participation->getFormation(),
            'accessManager' => $accessManager,
            'courseManager' => $this->courseManager,
            'course' => $course,
            'step' => $step,
            "current_module" => $module,
            'completed' => $participation->isStepCompleted($module),
            'etututo_step_1_completed' => true
        ));
    }

    #[Route(path: '/formation-fiche-action-{stepId}/{id}', name: 'eduprat_front_module_fiche_action', requirements: ['stepId' => '1|2'])]
    public function fiche(Participation $participation, Request $request, ParticipationAccessManager $accessManager, EntityManagerInterface $entityManager, int $stepId = 1)
    {
        $stepId = (int) $stepId;

        if (!$this->canAccessToModules($participation)) {
            throw new AccessDeniedHttpException();
        }

        $module = $stepId === 1 ? CourseManager::STEP2_FICHE_ACTION_LABEL : CourseManager::STEP4_FICHE_ACTION_LABEL;

        // $canEdit = true;

        // if ($stepId === 1) {
        //     $canEdit = !$accessManager->isFormCompleted($participation, 2);
        // } else {
        //     $canEdit = !$accessManager->hasPassedClosingDate($participation);
        // }
        $canEdit = (new \DateTime())->setTime(0, 0) <= $participation->getFormation()->getClosingDate();
        if (!is_null($participation->getFormation()->getFormClosingDate())) {
            $canEdit = (new \DateTime())->setTime(0, 0) <= $participation->getFormation()->getFormClosingDate();
        }

        if ($participation->getFormation()->isVfc() ||$participation->getFormation()->isTcs()) {
            $canEdit = true;
        }

        $course = $this->courseManager->getCourseDetail($participation);
        $step = $this->courseManager->findStepModule($course, $module);

        $this->participationLogger->startLog($participation, $module, null, $participation->isStepCompleted($module) ? 100 : 0);

        if ($canEdit) {
            if ($stepId === 1 && count($participation->getFicheActions()) == 0) {
                $emptyFicheAction = new FicheAction();
                $emptyFicheAction->setAction("");
                $emptyFicheAction->setProblematique("");
                $participation->addFicheAction($emptyFicheAction);
            }
            $form = $this->createForm(FicheActionCollectionType::class, $participation, array(
                "step" => $stepId,
                "action" => $this->generateUrl('eduprat_front_module_fiche_action', array(
                    "id" => $participation->getId(),
                    "stepId" => $stepId
                ))
            ));

            $form->handleRequest($request);

            if ($form->isSubmitted() && $form->isValid()) {
                if (!$participation->minModuleTimeReached($module)) {
                    $this->flashMessages->addError("front.error.minTime");
                } else {
                    $this->courseManager->completeCourseStep($participation, $module);
                    $entityManager->persist($participation);
                    foreach($participation->getFicheActions() as $ficheAction) {
                        if($ficheAction->getProblematique() == "" && $ficheAction->getAction() == "") {
                            $participation->removeFicheAction($ficheAction);
                        }
                    }
                    $entityManager->flush();
    
                    $this->participationLogger->endLog($participation, $module, null, 100);
    
                    if (is_null($form->get('continueFormation')->getData())) {
                        return $this->redirectToNextModule($participation, $module, $request);
                    }
    
                    return $this->redirectToFormationsList($participation);
                }
            }
        }

        return $this->render(sprintf("audit/module/fiche_action_%s.html.twig", $stepId), array(
            'form' => $canEdit ? $form : null,
            'participation' => $participation,
            'formation' => $participation->getFormation(),
            'accessManager' => $accessManager,
            'courseManager' => $this->courseManager,
            'course' => $course,
            'step' => $step,
            "current_module" => $module,
            'completed' => $participation->isStepCompleted($module),
            'canEdit' => $canEdit
        ));
    }

    #[Route(path: '/formation-fiche-action-download/{id}', name: 'eduprat_front_module_fiche_action_download')]
    public function ficheActionDownload(Participation $participation, $stepId = 1): CsvFileResponse
    {
        if (!$this->canAccessToModules($participation)) {
            throw new AccessDeniedHttpException();
        }

        $data = $participation->getFicheActions()->map(function(FicheAction $ficheAction) {
            return array(
                $ficheAction->getProblematique() ?? "",
                $ficheAction->getAction() ?? "",
                $ficheAction->getEvaluation() ?? "",
                $ficheAction->getNewAction() ?? "",
            );
        })->toArray();

        $participant = $participation->getParticipant();

        $participantRppsOrAdeli = $participant->getRpps() != null ? $participant->getRpps() : $participant->getAdeli();
        $participantDatas = $participant->getLastName() . " " . $participant->getFirstname() . " " . $participantRppsOrAdeli;

        array_unshift($data, [0 => $participantDatas]);

        array_unshift($data, array(
            "Problématique identifiée",
            "Action d'amélioration entreprise",
            "Évaluation de l'impact de l'action entreprise",
            "Nouvelles actions d'amélioration à mettre en oeuvre / poursuivre",
        ));

        return new CsvFileResponse($data, sprintf("Fiche Action %s %s.csv", $participation->getFormation()->getProgramme()->getReference(), $participation->getParticipant()->getInvertedFullname()));
    }

    #[Route(path: '/formation-video-2/{id}', name: 'eduprat_front_module_video_2')]
    public function video2(Participation $participation, ParticipationAccessManager $accessManager, EntityManagerInterface $entityManager): Response
    {
        if (!$this->canAccessToModules($participation)) {
            throw new AccessDeniedHttpException();
        }

        $module = CourseManager::STEP3_VIDEO_LABEL;

        $course = $this->courseManager->getCourseDetail($participation);
        $step = $this->courseManager->findStepModule($course, $module);

        $this->participationLogger->startLog($participation, $module, null, $participation->isStepCompleted($module) ? 100 : 0);

        return $this->render("audit/module/video_2.html.twig", array(
            'participation' => $participation,
            'formation' => $participation->getFormation(),
            'accessManager' => $accessManager,
            'courseManager' => $this->courseManager,
            'course' => $course,
            'step' => $step,
            "current_module" => $module,
            'completed' => $participation->isStepCompleted($module)
        ));
    }

    #[Route(path: '/formation-video-2-validate/{id}', name: 'eduprat_front_module_video_2_validate')]
    public function video2Validate(Participation $participation, Request $request, EntityManagerInterface $entityManager, ParticipationAccessManager $accessManager): RedirectResponse
    {
        if (!$this->canAccessToModules($participation)) {
            throw new AccessDeniedHttpException();
        }

        $currentModule = CourseManager::STEP3_VIDEO_LABEL;
        if (!$participation->isStepCompleted($currentModule)) {
            if (!$participation->minModuleTimeReached($currentModule)) {
                $this->flashMessages->addError("front.error.minTime");
                return $this->redirectToRoute('eduprat_front_module_video_2', array("id" => $participation->getId()));
            }
            $this->courseManager->completeCourseStep($participation, $currentModule);
            $entityManager->persist($participation);
            $entityManager->flush();
        }
        $this->participationLogger->endLog($participation, $currentModule, null, 100);

        return $this->redirectToNextModule($participation, $currentModule, $request);
    }

    #[Route(path: '/formation-docs-pedagogiques/{id}', name: 'eduprat_front_module_doc_pedagogique_1')]
    public function docsPedagogiques(Participation $participation): RedirectResponse
    {
        if (!$this->canAccessToModules($participation)) {
            throw new AccessDeniedHttpException();
        }
        $filesCount = (count($participation->getFormation()->getProgramme()->getToolProgrammeFiles()));
        $lastAccessibleFile = $participation->getLastToolFile() && $participation->getLastToolFile() + 1 <= $filesCount ?$participation->getLastToolFile() + 1 : 1;
        return $this->redirectToRoute("eduprat_front_module_doc_pedagogique_1_file", array("file" => $lastAccessibleFile, "id" => $participation->getId()));
    }

    #[Route(path: '/formation-docs-pedagogiques-{file}/{id}', name: 'eduprat_front_module_doc_pedagogique_1_file')]
    public function docsPedagogiquesFile(Participation $participation, ParticipationAccessManager $accessManager, $file = 1): Response
    {
        if (!$this->canAccessToModules($participation)) {
            throw new AccessDeniedHttpException();
        }

        $toolFile = $participation->getFormation()->getProgramme()->getDocumentsPedagogiquesFiles()->toArray()[$file-1];
        $module = CourseManager::STEP1_DOC_PEDAGOGIQUE_LABEL;

        $lastCompletedFile = $participation->getLastDocumentsPedagogiquesFile() ? $participation->getLastDocumentsPedagogiquesFile() : 0;
        $filesCount = count($participation->getFormation()->getProgramme()->getDocumentsPedagogiquesFiles());
        $progression = $lastCompletedFile > 0 ? ($lastCompletedFile * 100) / $filesCount : 0;
        $stringProgression = $lastCompletedFile . "/" . $filesCount;

        $this->participationLogger->endLog($participation, $module, null, $participation->isStepCompleted($module) ? 100 : $progression, $stringProgression);

        $course = $this->courseManager->getCourseDetail($participation);
        $step = $this->courseManager->findStepModule($course, $module);

        $this->participationLogger->startLog($participation, $module, null, $participation->isStepCompleted($module) ? 100 : $progression, $stringProgression);

        $fileCount = count($participation->getFormation()->getProgramme()->getDocumentsPedagogiquesFiles());
        return $this->render('audit/module/doc_pedagogique.html.twig', array(
            'participation' => $participation,
            'formation' => $participation->getFormation(),
            'accessManager' => $accessManager,
            'courseManager' => $this->courseManager,
            'course' => $course,
            'step' => $step,
            'current_module' => $module,
            'completed' => $participation->isStepCompleted($module),
            'documentsPedagogiquesFile' => $toolFile,
            'toolFiles' => $participation->getFormation()->getProgramme()->getDocumentsPedagogiquesFiles()->toArray(),
            'fileNumber' => $file,
            'fileCount' => $fileCount,
            'isLastFile' => $file == $fileCount
        ));
    }

    #[Route(path: '/formation-tool-box/{id}', name: 'eduprat_front_module_tool_box')]
    public function toolBox(Participation $participation): RedirectResponse
    {
        if (!$this->canAccessToModules($participation)) {
            throw new AccessDeniedHttpException();
        }
        $filesCount = (count($participation->getFormation()->getProgramme()->getToolProgrammeFiles()));
        $lastAccessibleFile = $participation->getLastToolFile() && $participation->getLastToolFile() + 1 <= $filesCount ?$participation->getLastToolFile() + 1 : 1;
        return $this->redirectToRoute("eduprat_front_module_tool_box_file", array("file" => $lastAccessibleFile, "id" => $participation->getId()));
    }

    #[Route(path: '/formation-tool-box-{file}/{id}', name: 'eduprat_front_module_tool_box_file')]
    public function toolBoxFile(Participation $participation, ParticipationAccessManager $accessManager, $file = 1): Response
    {
        if (!$this->canAccessToModules($participation)) {
            throw new AccessDeniedHttpException();
        }

        $toolFile = $participation->getFormation()->getProgramme()->getToolProgrammeFiles()->toArray()[$file-1];
        $module = CourseManager::STEP3_TOOL_BOX_LABEL;

        $lastCompletedFile = $participation->getLastToolFile() ? $participation->getLastToolFile() : 0;
        $filesCount = count($participation->getFormation()->getProgramme()->getToolProgrammeFiles());
        $progression = $lastCompletedFile > 0 ? ($lastCompletedFile * 100) / $filesCount : 0;
        $stringProgression = $lastCompletedFile . "/" . $filesCount;


        $this->participationLogger->endLog($participation, $module, null, $participation->isStepCompleted($module) ? 100 : $progression, $stringProgression);

        $course = $this->courseManager->getCourseDetail($participation);
        $step = $this->courseManager->findStepModule($course, $module);

        $this->participationLogger->startLog($participation, $module, null, $participation->isStepCompleted($module) ? 100 : $progression, $stringProgression);

        $fileCount = count($participation->getFormation()->getProgramme()->getToolProgrammeFiles());
        return $this->render('audit/module/tool_box.html.twig', array(
            'participation' => $participation,
            'formation' => $participation->getFormation(),
            'accessManager' => $accessManager,
            'courseManager' => $this->courseManager,
            'course' => $course,
            'step' => $step,
            'current_module' => $module,
            'completed' => $participation->isStepCompleted($module),
            'toolFile' => $toolFile,
            'toolFiles' => $participation->getFormation()->getProgramme()->getToolProgrammeFiles()->toArray(),
            'fileNumber' => $file,
            'fileCount' => count($participation->getFormation()->getProgramme()->getToolProgrammeFiles()),
            'isLastFile' => $file == $fileCount
        ));
    }

    #[Route(path: '/formation-validate-tool-box-{file}/{id}', name: 'eduprat_front_module_tool_box_validate')]
    public function toolBoxValidate(Participation $participation, Request $request, EntityManagerInterface $entityManager, $file = 1)
    {
        if (!$this->canAccessToModules($participation)) {
            throw new AccessDeniedHttpException();
        }

        $currentModule = CourseManager::STEP3_TOOL_BOX_LABEL;

        if ($participation->getLastToolFile() < $file) {
            $participation->setLastToolFile($file);
            $entityManager->persist($participation);
            $entityManager->flush();
        }

        $lastCompletedFile = $participation->getLastToolFile() ? $participation->getLastToolFile() : 0;
        $filesCount = count($participation->getFormation()->getProgramme()->getToolProgrammeFiles());
        $progression = $lastCompletedFile > 0 ? ($lastCompletedFile * 100) / $filesCount : 0;
        $stringProgression = $lastCompletedFile . "/" . $filesCount;

        if (count($participation->getFormation()->getProgramme()->getToolProgrammeFiles()) > $file) {
            $this->participationLogger->endLog($participation, $currentModule, null, $participation->isStepCompleted($currentModule) ? 100 : $progression, $stringProgression);
            return $this->redirectToRoute("eduprat_front_module_tool_box_file", array("file" => $file+1, "id" => $participation->getId()));
        } else {
            if (!$participation->isStepCompleted($currentModule)) {
                if (!$participation->minModuleTimeReached($currentModule)) {
                    $this->flashMessages->addError("front.error.minTime");
                    return $this->redirectToRoute("eduprat_front_module_tool_box_file", array("file" => $file, "id" => $participation->getId()));
                }
                $this->courseManager->completeCourseStep($participation, $currentModule);
                $entityManager->persist($participation);
                $entityManager->flush();
            }
        }

        $this->participationLogger->endLog($participation, $currentModule, null, $participation->isStepCompleted($currentModule) ? 100 : $progression, $stringProgression);

        if (($request->query->has("redirect") && $request->query->get("redirect") === "admin") || ($request->request->has("redirect") && $request->request->get("redirect") === "admin")) {
            return $this->redirectToFormationsDetail($participation);
        }

        return $this->redirectToNextModule($participation, $currentModule, $request);
    }

    #[Route(path: '/formation-validate-documents-pedagogique-{file}/{id}', name: 'eduprat_front_module_documents_pedagogiques_validate')]
    public function documentsPedagogiquesValidate(Participation $participation, Request $request, EntityManagerInterface $entityManager, $file = 1)
    {
        if (!$this->canAccessToModules($participation)) {
            throw new AccessDeniedHttpException();
        }

        $currentModule = CourseManager::STEP1_DOC_PEDAGOGIQUE_LABEL;

        if ($participation->getLastDocumentsPedagogiquesFile() < $file) {
            $participation->setLastDocumentsPedagogiquesFile($file);
            $entityManager->persist($participation);
            $entityManager->flush();
        }

        $lastCompletedFile = $participation->getLastDocumentsPedagogiquesFile() ? $participation->getLastDocumentsPedagogiquesFile() : 0;
        $toolFilesCount = count($participation->getFormation()->getProgramme()->getDocumentsPedagogiquesFiles());
        $progression = $lastCompletedFile > 0 ? ($lastCompletedFile * 100) / $toolFilesCount : 0;
        $stringProgression = $lastCompletedFile . "/" . $toolFilesCount;

        if (count($participation->getFormation()->getProgramme()->getDocumentsPedagogiquesFiles()) > $file) {
            $this->participationLogger->endLog($participation, $currentModule, null, $participation->isStepCompleted($currentModule) ? 100 : $progression, $stringProgression);
            return $this->redirectToRoute("eduprat_front_module_doc_pedagogique_1_file", array("file" => $file+1, "id" => $participation->getId()));
        } else {
            if (!$participation->isStepCompleted($currentModule)) {
                if (!$participation->minModuleTimeReached($currentModule)) {
                    $this->flashMessages->addError("front.error.minTime");
                    return $this->redirectToRoute("eduprat_front_module_doc_pedagogique_1_file", array("file" => $file, "id" => $participation->getId()));
                }
                $this->courseManager->completeCourseStep($participation, $currentModule);
                $entityManager->persist($participation);
                $entityManager->flush();
            }
        }

        $this->participationLogger->endLog($participation, $currentModule, null, $participation->isStepCompleted($currentModule) ? 100 : $progression, $stringProgression);

        if (($request->query->has("redirect") && $request->query->get("redirect") === "admin") || ($request->request->has("redirect") && $request->request->get("redirect") === "admin")) {
            return $this->redirectToFormationsDetail($participation);
        }

        return $this->redirectToNextModule($participation, $currentModule, $request);
    }

    #[Route(path: '/formation-topos/{id}', name: 'eduprat_front_module_topos')]
    public function topos(Participation $participation, ParticipationAccessManager $accessManager): Response
    {
        if (!$this->canAccessToModules($participation)) {
            throw new AccessDeniedHttpException();
        }

        $module = CourseManager::STEP2_TOPOS_LABEL;

        $course = $this->courseManager->getCourseDetail($participation);
        $step = $this->courseManager->findStepModule($course, $module);

        $this->participationLogger->startLog($participation, $module, null, $participation->isStepCompleted($module) ? 100 : 0);

        return $this->render('audit/module/topos.html.twig', array(
            'participation' => $participation,
            'formation' => $participation->getFormation(),
            'accessManager' => $accessManager,
            'courseManager' => $this->courseManager,
            'course' => $course,
            'step' => $step,
            "current_module" => $module,
            'completed' => $participation->isStepCompleted($module),
            'tool_box_enabled' => $this->courseManager->isBeforeVideoAndToolBoxUpdate($participation),
        ));
    }

    #[Route(path: '/formation-topos-validate/{id}', name: 'eduprat_front_module_topos_validate')]
    public function toposValidate(Participation $participation, Request $request, EntityManagerInterface $entityManager): RedirectResponse
    {
        if (!$this->canAccessToModules($participation)) {
            throw new AccessDeniedHttpException();
        }

        $currentModule = CourseManager::STEP2_TOPOS_LABEL;
        if (!$participation->isStepCompleted($currentModule)) {
            if (!$participation->minModuleTimeReached($currentModule)) {
                $this->flashMessages->addError("front.error.minTime");
                return $this->redirectToRoute('eduprat_front_module_topos', array("id" => $participation->getId()));
            }
            $this->courseManager->completeCourseStep($participation, $currentModule);
            $entityManager->persist($participation);
            $entityManager->flush();
        }

        $this->participationLogger->endLog($participation, $currentModule, null, 100);

        return $this->redirectToNextModule($participation, $currentModule, $request);
    }

    #[Route(path: '/formation-prerestitution-validate/{id}', name: 'eduprat_front_formation_prerestitution_validate')]
    public function formationPrerestitutionValidate(Participation $participation, Request $request, EntityManagerInterface $entityManager): RedirectResponse
    {
        if (!$this->canAccessToModules($participation)) {
            throw new AccessDeniedHttpException();
        }
        $currentModule = CourseManager::STEP1_PRERESTITUTION_LABEL;
        $this->participationLogger->endLog($participation, $currentModule, null, 100);
        if (!$participation->isStepCompleted($currentModule)) {
            if (!$participation->minModuleTimeReached($currentModule)) {
                $this->flashMessages->addError("front.error.minTime");
                return $this->redirectToRoute("eduprat_front_formation_module", array("module" => $currentModule, "id" => $participation->getId()));
            }
            $this->courseManager->completeCourseStep($participation, $currentModule);
            $entityManager->persist($participation);
            $entityManager->flush();
        }

        return $this->redirectToNextModule($participation, $currentModule, $request);
    }

    #[Route(path: '/formation-restitution-validate/{id}', name: 'eduprat_front_formation_restitution_validate')]
    public function formationRestitutionValidate(Participation $participation, Request $request, EntityManagerInterface $entityManager): RedirectResponse
    {
        if (!$this->canAccessToModules($participation)) {
            throw new AccessDeniedHttpException();
        }

        $currentModule = CourseManager::STEP4_RESTITUTION_LABEL;
        $course = $this->courseManager->getCourseDetail($participation);
        $step = $this->courseManager->findStepModule($course, $currentModule);

        if (!$participation->isStepCompleted($currentModule)) {
            if (!$participation->minModuleTimeReached($currentModule)) {
                $this->flashMessages->addError("front.error.minTime");
                return $this->redirectToRoute("eduprat_front_formation_module", array("module" => $currentModule, "id" => $participation->getId()));
            }
            $this->courseManager->completeCourseStep($participation, $currentModule);
            $entityManager->persist($participation);
            $entityManager->flush();
        }

        $this->participationLogger->endLog($participation, $currentModule, null, 100);

        return $this->redirectToNextModule($participation, $currentModule, $request);
        
    }

    #[Route(path: '/formation-fiche-synthese/{id}', name: 'eduprat_front_module_synthese')]
    public function synthese(Participation $participation, Request $request, ParticipationAccessManager $accessManager, EntityManagerInterface $entityManager)
    {
        if (!$this->canAccessToModules($participation)) {
            throw new AccessDeniedHttpException();
        }

        $module = CourseManager::STEP4_SYNTHESE_LABEL;

        $course = $this->courseManager->getCourseDetail($participation);
        $step = $this->courseManager->findStepModule($course, $module);

        $canEdit = !$participation->isStepCompleted($module);

        $form = $this->createForm(EtutoratType::class, null, array(
            "participation" => $participation,
            "synthese" => true,
            "disabled" => !$canEdit
        ));

        if (!$participation->getFormation()->getProgramme()->hasEtutorat()) {
            return $this->redirectToNextModule($participation, $module, $request);
        }

        $form->handleRequest($request);

        $this->participationLogger->startLog($participation, $module, null, $participation->isStepCompleted($module) ? 100 : 0);

        if ($form->isSubmitted() && $form->isValid()) {
            if (!$participation->minModuleTimeReached($module)) {
                $this->flashMessages->addError("front.error.minTime");
            } else {

                foreach ($form->all() as $item) {
                    if ($item->getData() instanceof FicheSynthese) {
                        $entityManager->persist($item->getData());
                    }
                }

                $entityManager->flush();

                $this->courseManager->completeCourseStep($participation, $module);
                $entityManager->persist($participation);
                $entityManager->flush();

                $this->participationLogger->endLog($participation, $module, null, 100);

                return $this->redirectToNextModule($participation, $module, $request);
            }
        }

        return $this->render("audit/module/synthese.html.twig", array(
            'form' => $form,
            'canEdit' => $canEdit,
            'participation' => $participation,
            'formation' => $participation->getFormation(),
            'accessManager' => $accessManager,
            'courseManager' => $this->courseManager,
            'course' => $course,
            'step' => $step,
            "current_module" => $module,
            'completed' => $participation->isStepCompleted($module),
        ));
    }

    #[Route(path: '/formation-fiche-progression/{id}', name: 'eduprat_front_module_progression')]
    public function progression(Participation $participation, Request $request, ParticipationAccessManager $accessManager, EntityManagerInterface $entityManager)
    {
        if (!$this->canAccessToModules($participation)) {
            throw new AccessDeniedHttpException();
        }

        if (!$participation->getFormation()->isPredefined()) {
            throw new NotFoundHttpException();
        }

        $module = CourseManager::STEP4_PROGRESSION_LABEL;

        $course = $this->courseManager->getCourseDetail($participation);
        $step = $this->courseManager->findStepModule($course, $module);

        $editable = (new \DateTime("tomorrow")) < $participation->getFormation()->getFormClosingDate();

        /** @var FormationAudit $formation */
        $formation = $participation->getFormation();

        if ($participation->getFicheProgressions()->count() === 0 ) {
            foreach ($formation->getAudit()->getPatientDescriptions() as $patient) {
                $sampleProgression = new FicheProgression();
                $sampleProgression->setPatient($patient);
                $participation->addFicheProgression($sampleProgression);
            }
        }

        $form = $this->createForm(FicheProgressionCollectionType::class, $participation, array(
            "editable" => $editable
        ));

        $form->handleRequest($request);

        $this->participationLogger->startLog($participation, $module, null, $participation->isStepCompleted($module) ? 100 : 0);

        if ($form->isSubmitted() && $form->isValid()) {
            $timeCompleted = $participation->minModuleTimeReached($module);
            if (!$timeCompleted) {
                $this->flashMessages->addError("front.error.minTime");
            } else {
                $this->courseManager->completeCourseStep($participation, $module);
                $entityManager->persist($participation);
                $entityManager->flush();

                $this->participationLogger->endLog($participation, $module, null, 100);

                return $this->redirectToNextModule($participation, $module, $request);
            }
        }

        return $this->render("audit/module/fiche_progression.html.twig", array(
            'form' => $form,
            'editable' => $editable,
            'participation' => $participation,
            'formation' => $participation->getFormation(),
            'accessManager' => $accessManager,
            'courseManager' => $this->courseManager,
            'course' => $course,
            'step' => $step,
            "current_module" => $module,
            'completed' => $participation->isStepCompleted($module),
        ));
    }

    #[Route(path: '/formation-finalisation/{id}', name: 'eduprat_front_module_end')]
    public function end(Participation $participation, Request $request, ParticipationAccessManager $accessManager, EntityManagerInterface $entityManager, FormFactoryInterface $formFactory, EmailSender $emailSender): Response
    {
        if (!$this->canAccessToModules($participation)) {
            throw new AccessDeniedHttpException();
        }

        $module = CourseManager::STEP4_END_LABEL;

        $course = $this->courseManager->getCourseDetail($participation);
        $step = $this->courseManager->findStepModule($course, $module);

        $formBuilder = $formFactory->createNamedBuilder("attestationHonneurFile", FormType::class, $participation);
        if ($participation->getFormation()->shouldDisplayAttestationN1()) {
            $this->addField($formBuilder, "attestationHonneurFileN1");
        } else {
            $this->addField($formBuilder, 'attestationHonneurFile');
        }
        $form = $formBuilder->getForm();

        $form->handleRequest($request);

        $this->participationLogger->startLog($participation, $module, null, $participation->isStepCompleted($module) ? 100 : 0);

        if ($form->isSubmitted() && $form->isValid()) {

            $entityManager->persist($participation);
            $entityManager->flush();

            if ($participation->getCoordinator()) {
                $entityManager->refresh($participation);
                $emailSender->sendAttestationHonneurUploadedEmail($participation);
            }

            $this->participationLogger->addLog($participation, ParticipationLog::ACTION_UPLOAD_ATTESTATION_HONNEUR);

            $this->flashMessages->addSuccess('front.success.attestationHonneur');
        }

        return $this->render("audit/module/end.html.twig", array(
            'form' => $form,
            'participation' => $participation,
            'formation' => $participation->getFormation(),
            'accessManager' => $accessManager,
            'courseManager' => $this->courseManager,
            'course' => $course,
            'step' => $step,
            "current_module" => $module,
            'completed' => $participation->isStepCompleted($module),
        ));
    }

    #[Route(path: '/formation-finalisation-validate/{id}', name: 'eduprat_front_module_end_validate')]
    public function endValidate(Participation $participation, Request $request, EntityManagerInterface $entityManager, ParticipationAccessManager $accessManager)
    {
        if (!$this->canAccessToModules($participation)) {
            throw new AccessDeniedHttpException();
        }

        $currentModule = CourseManager::STEP4_END_LABEL;
        if (!$participation->isStepCompleted($currentModule)) {
            if (!$participation->minModuleTimeReached($currentModule)) {
                $this->flashMessages->addError("front.error.minTime");
                return $this->redirectToRoute("eduprat_front_module_end", array("id" => $participation->getId()));
            }
            $this->courseManager->completeCourseStep($participation, $currentModule);
            $entityManager->persist($participation);
            $entityManager->flush();
        }

        $this->participationLogger->endLog($participation, $currentModule, null, 100);

        if ($request->query->has("redirect") && $request->query->get("redirect") === "admin") {
            return $this->redirectToNextModule($participation, $currentModule, $request);
        }

        if ($accessManager->canEvaluateFormation($participation)) {
            return $this->redirectToRoute("eduprat_evaluation_redirect", array("participation" => $participation->getId()));
        }

        return $this->redirectToFormationsList($participation);
    }

    #[Route(path: '/formation-{module}/{id}', name: 'eduprat_front_formation_module')]
    public function formationPrerestitution(Participation $participation, $module, ParticipationAccessManager $accessManager, CourseManager $courseManager, RestitutionCalculator $restitutionCalculator): Response
    {
        if (!$this->canAccessToModules($participation)) {
            throw new AccessDeniedHttpException();
        }

        $course = $courseManager->getCourseDetail($participation);
        $step = $this->courseManager->findStepModule($course, $module);

        $this->participationLogger->startLog($participation, $module, null, $participation->isStepCompleted($module) ? 100 : 0);

        $restitutionDatas = $restitutionCalculator->getRestitution($participation, array(
            "axis_audit" => $module === CourseManager::STEP1_PRERESTITUTION_LABEL ? 1 : 2,
            "auditId" => $module === CourseManager::STEP1_PRERESTITUTION_LABEL ? 1 : 2,
        ));

        $moduleTemplate = $module == CourseManager::STEP4_RESTITUTION_LABEL && $participation->getFormation()->isFormVignetteAudit() ? "postrestitution" : $module;

        return $this->render(sprintf("audit/module/%s.html.twig", $moduleTemplate), array_merge(array(
            'participation' => $participation,
            'formation' => $participation->getFormation(),
            'accessManager' => $accessManager,
            'courseManager' => $courseManager,
            'course' => $course,
            'step' => $step,
            "current_module" => $module,
            'completed' => $participation->isStepCompleted($module),
            "moduleId" => $module === CourseManager::STEP1_PRERESTITUTION_LABEL ? 1 : 2,
        ), $restitutionDatas));
    }

    #[Route(path: '/participation-next-module/{participation}/{module}', name: 'eduprat_front_next_module')]
    public function nextModule(Participation $participation, $module, Request $request, EntityManagerInterface $entityManager)
    {
        return $this->redirectToNextModule($participation, $module, $request);
    }

    #[Route(path: '/participation-clear-module/{module}/{id}', name: 'eduprat_front_clear_module')]
    public function clearModule(Participation $participation, $module, EntityManagerInterface $entityManager)
    {
        if (!$this->canAccessToModules($participation)) {
            throw new AccessDeniedHttpException();
        }
        if ($module === CourseManager::SATISFACTION_LABEL) {
            return $this->redirectToFormationsList($participation);
        }
        $participation->removeCourseStep($module);

        switch ($module) {
            case CourseManager::STEP1_ETUTORAT_LABEL:
                $participation->getEtutorats()->clear();
                $participation->setEtutoratCasClinique(null);
                break;
            case CourseManager::STEP2_FICHE_ACTION_LABEL:
            case CourseManager::STEP4_FICHE_ACTION_LABEL:
                $participation->getFicheActions()->clear();
                break;
            case CourseManager::STEP4_SYNTHESE_LABEL:
                $participation->getFicheSyntheses()->clear();
                break;
            case CourseManager::STEP4_PROGRESSION_LABEL:
                $participation->getFicheProgressions()->clear();
                break;
            case CourseManager::STEP1_FORM_PRE_LABEL:
                $participation->setPatientsDescriptions(new ArrayCollection());
                $participation->setAuditAnswers(new ArrayCollection());
                $participation->setSurveyAnswers(new ArrayCollection());
                $participation->setCompletedForm1(false);
                $participation->setCompletedForms(false);
            case CourseManager::STEP3_FORM_POST_LABEL:
                $participation->setPatientsDescriptions(new ArrayCollection());
                $participation->setAuditAnswers(new ArrayCollection());
                $participation->setSurveyAnswers(new ArrayCollection());
                $participation->setCompletedForm2(false);
                $participation->setCompletedForms(false);
                break;
            default:
                break;
        }

        $entityManager->persist($participation);
        $entityManager->flush();

        return $this->redirectToFormationsList($participation);
    }

    /**
     * @param Participation $participation
     * @param $currentModule
     * @return RedirectResponse
     */
    public function redirectToNextModule(Participation $participation, $currentModule, Request $request)
    {
        if (($request->query->has("redirect") && $request->query->get("redirect") === "admin") || ($request->request->has("redirect") && $request->request->get("redirect") === "admin")) {
            return $this->redirectToFormationsDetail($participation);
        }
        $course = $this->courseManager->getCourseDetail($participation);
        $nextModule = $this->courseManager->getNextModule($course, $currentModule);
        if ($nextModule && $nextModule["url"] && $nextModule["available"]) {
            return $this->redirect($nextModule["url"]);
        }

        // Si l'utilisateur est un user backoffice on le redirige vers les détail session
        if ($this->security->getUser()->getRole() !== null) {
            return $this->redirectToFormationsDetail($participation);
        }

        return $this->redirectToFormationsList($participation);
    }

    public function redirectToFormationsList(Participation $participation) {
        return $this->redirectToRoute('eduprat_front_formations', array(
            'year' => $participation->getFormation()->getStartDate()->format("Y"),
            '_fragment' => "participation-" . $participation->getId()
        ));
    }

    public function redirectToFormationsDetail(Participation $participation) {
        return $this->redirectToRoute("admin_formation_show", array(
            "id" => $participation->getFormation()->getId(),
            "_fragment" => "parcours-" . $participation->getId()
        ));
    }

    public function canAccessToModules($participation) {
        if ($participation->getParticipant()->getUser() !== $this->getUser()) {
            return $this->courseManager->canAccessToModules($participation, $this->getUser());
        }
        return true;
    }

    #[Route(path: '/participation-refresh-time/{id}/{stepId}', name: 'eduprat_front_refresh_time')]
    public function refreshTime(Participation $participation, $stepId): JsonResponse {
        return new JsonResponse(array("status" => "ok", "currentTime" =>  intval($participation->getTotalTimeLogsByStep($stepId, true, false, false, true))));
    }

    #[Route(path: '/participation-refresh-module-time/{id}/{moduleId}', name: 'eduprat_front_refresh_module_time')]
    public function refreshModuleTime(Participation $participation, $moduleId) {
        return new JsonResponse(array("status" => "ok", "currentModuleTime" =>  intval($participation->getTimeLogedByModule($moduleId))));
    }

    /**
     * @param FormBuilderInterface $formBuilder
     * @param string $field
     * @return void
     */
    public function addField(FormBuilderInterface $formBuilder, string $field): void
    {
        $formBuilder
            ->add($field, VichFileType::class, array(
                'label' => false,
                'required' => false,
                'download_uri' => false,
                'allow_delete' => false,
                'error_bubbling' => true,
                'constraints' => new File(
                    array(
                        'mimeTypes' => array(
                            'image/png', 'image/jpeg', 'application/pdf', 'application/x-pdf'
                        ),
                        'mimeTypesMessage' => 'formation.mimeTypesAttestation'
                    )
                )
            ));
    }

}
