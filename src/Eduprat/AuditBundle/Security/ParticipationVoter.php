<?php

namespace Eduprat\AuditBundle\Security;

use Ed<PERSON>rat\AdminBundle\Entity\Person;
use Ed<PERSON>rat\DomainBundle\Entity\Participation;
use Symfony\Component\Security\Core\Authentication\Token\TokenInterface;
use Symfony\Component\Security\Core\Authorization\AccessDecisionManagerInterface;
use Symfony\Component\Security\Core\Authorization\Voter\Voter;

class ParticipationVoter extends Voter
{
    const ANSWER = 'answer';
    const DOWNLOAD_PDF_CERTIFICATE_PARTICIPATION = 'download_pdf_certificate_participation';

    private $decisionManager;

    public function __construct(AccessDecisionManagerInterface $decisionManager)
    {
        $this->decisionManager = $decisionManager;
    }

    protected function supports($attribute, $subject): bool
    {
        if (!in_array($attribute, array(self::ANSWER, self::DOWNLOAD_PDF_CERTIFICATE_PARTICIPATION))) {
            return false;
        }

        if (!$subject instanceof Participation) {
            return false;
        }

        return true;
    }

    protected function voteOnAttribute($attribute, $subject, TokenInterface $token): bool
    {
        $user = $token->getUser();

        if (!$user instanceof Person) {
            // the user must be logged in; if not, deny access
            return false;
        }

        /** @var Participation $participation */
        $participation = $subject;

        switch ($attribute) {
            case self::DOWNLOAD_PDF_CERTIFICATE_PARTICIPATION:
                return $this->canDownloadCertificateParticipation($participation, $user, $token);
        }

        // ROLE_SUPER_ADMIN can do anything! The power!
        if ($this->decisionManager->decide($token, array('ROLE_SUPER_ADMIN'))) {
            return true;
        }


        switch ($attribute) {
            case self::ANSWER:
                return $this->canAnswer($participation, $user, $token);
        }

        throw new \LogicException('This code should not be reached!');
    }

    private function canAnswer(Participation $participation, Person $user, TokenInterface $token): bool
    {
        return $this->decisionManager->decide($token, array('ROLE_COORDINATOR'))
        || $user->getParticipant() === $participation->getParticipant()
        || ($user->isSupervisor() && $user === $participation->getFormation()->getProgramme()->getCoordinator()->getSupervisor());
    }

    /**
     * En place sur CRM :
     * webmaster ou closed et DPC
     */
    private function canDownloadCertificateParticipation(Participation $participation, Person $user, TokenInterface $token)
    {
        return ($this->decisionManager->decide($token, array('ROLE_WEBMASTER')) || $participation->getFormation()->getClosed())
            && $participation->hasDpcFinanceSousMode();
    }
}