<?php

namespace Eduprat\AuditBundle\Command;

use Doctrine\ORM\EntityManagerInterface;
use Ed<PERSON>rat\AuditBundle\Services\AuditManager;
use Eduprat\AuditBundle\Services\SurveyManager;
use Ed<PERSON>rat\DomainBundle\Entity\Audit;
use Eduprat\DomainBundle\Entity\AuditAnswer;
use Eduprat\DomainBundle\Entity\Formation;
use Eduprat\DomainBundle\Entity\Survey;
use Eduprat\DomainBundle\Entity\SurveyAnswer;
use Eduprat\DomainBundle\Entity\SurveyQuestion;
use Symfony\Component\Console\Attribute\AsCommand;
use Symfony\Component\Console\Command\Command;
use Symfony\Component\Console\Input\InputArgument;
use Symfony\Component\Console\Input\InputInterface;
use Symfony\Component\Console\Output\OutputInterface;
use Symfony\Component\Form\Exception\LogicException;

#[AsCommand(name: 'eduprat:generate:answers', description: 'Commande permettant de générer des réponses à une formation')]
class GenerateAnswersCommand extends Command
{
    /**
     * @var EntityManagerInterface
     */
    private $entityManager;

    public function __construct(EntityManagerInterface $entityManager)
    {
        parent::__construct();
        $this->entityManager = $entityManager;
    }

    /**
     * {@inheritdoc}
     */
    protected function configure(): void
    {
        $this
            ->addArgument('formation', InputArgument::REQUIRED, 'ID de la formation')
        ;
    }

    /**
     * {@inheritdoc}
     */
    protected function execute(InputInterface $input, OutputInterface $output): int
    {
        $formation = $this->entityManager->getRepository(Formation::class)->find($input->getArgument('formation'));

        if ($formation->isAudit()) {
            /** @var FormationAudit $formation */
            /** @var Audit $audit */
            $audit = $formation->getAudit();

            $questions = $audit->getQuestions();
            $nbPatients = $audit->getNbPatients();
            $auditsIds = [AuditManager::FIRST_AUDIT, AuditManager::SECOND_AUDIT];

            $participations = $formation->getParticipations();
            foreach ($participations as $participation) {
                foreach ($questions as $question) {
                    foreach ($auditsIds as $auditId) {
                        for ($patient = 1; $patient <= $nbPatients; $patient++) {
                            $randomAnswer = $this->getAuditRandomAnswer();
                            if ($randomAnswer !== null) {
                                $currentAnswer = $this->entityManager->getRepository(AuditAnswer::class)->findOneBy(array(
                                    'participation'=> $participation,
                                    'question' => $question,
                                    'patient' => $patient,
                                    'auditId' => $auditId
                                ));
                                if (is_null($currentAnswer)) {
                                    $currentAnswer = (new AuditAnswer())
                                        ->setParticipation($participation)
                                        ->setQuestion($question)
                                        ->setAuditId($auditId)
                                        ->setPatient($patient);
                                }
                                $currentAnswer->setAnswer($randomAnswer);
                                $this->entityManager->persist($currentAnswer);
                            }
                        }
                    }
                }
            }
        } else if ($formation->isPresentielle()) {
            /** @var FormationPresentielle $formation */
            /** @var Survey $audit */
            $audit = $formation->getQuestionnaire();
            $questions = $audit->getQuestions();
            $surveyIds = [SurveyManager::FIRST_SURVEY, SurveyManager::SECOND_SURVEY];

            $participations = $formation->getParticipations();
            foreach ($participations as $participation) {
                foreach ($questions as $question) {
                    foreach ($surveyIds as $surveyId) {
                        $randomAnswer = $this->getSurveyRandomAnswer($question);
                        if ($randomAnswer !== null) {
                            $currentAnswer = $this->entityManager->getRepository(SurveyAnswer::class)->findOneBy(array(
                                'participation'=> $participation,
                                'question' => $question,
                                'surveyId' => $surveyId
                            ));
                            if (is_null($currentAnswer)) {
                                $currentAnswer = (new SurveyAnswer())
                                    ->setParticipation($participation)
                                    ->setQuestion($question)
                                    ->setSurveyId($surveyId);
                            }
                            $currentAnswer->setAnswer($randomAnswer);
                            $this->entityManager->persist($currentAnswer);
                        }
                    }
                }
            }
        } else {
            throw new LogicException('Formation invalide');
        }

        $this->entityManager->flush();
        return Command::SUCCESS;
    }

    public function getAuditRandomAnswer() {
        $rand = mt_rand(0, 100);
        if ($rand < 3) {
            return null;
        } else if ($rand < 60) {
            return 1;
        } else  if ($rand < 90) {
            return 0;
        } else {
            return 2;
        }
    }

    public function getSurveyRandomAnswer(SurveyQuestion $question) {
        $rand = mt_rand(0, 100);
        if ($rand < 95) {
            if ($question->getType() === SurveyQuestion::TYPE_TEXT) {
                return $this->generateRandomString(mt_rand(10, 30));
            } else {
                if ($question->getType() === SurveyQuestion::TYPE_CHOICE) {
                    return $question->getChoices()[mt_rand(0, $question->getChoices()->count() - 1)]->getLabel();
                }
            }
        }
        return null;
    }

    public function generateRandomString($length = 10) {
        $characters = '0123456789abcdefghijklmnopqrstuvwxyzABCDEFGHIJKLMNOPQRSTUVWXYZ';
        $charactersLength = strlen($characters);
        $randomString = '';
        for ($i = 0; $i < $length; $i++) {
            $randomString .= $characters[rand(0, $charactersLength - 1)];
        }
        return $randomString;
    }

}
