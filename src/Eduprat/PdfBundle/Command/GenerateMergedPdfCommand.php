<?php

namespace Eduprat\PdfBundle\Command;

use Doctrine\Common\Collections\ArrayCollection;
use Doctrine\ORM\EntityManagerInterface;
use Eduprat\DomainBundle\Entity\FinanceSousMode;
use Eduprat\DomainBundle\Entity\Formation;
use Eduprat\DomainBundle\Entity\Participation;
use Symfony\Component\Console\Attribute\AsCommand;
use Symfony\Component\Console\Command\Command;
use Symfony\Component\Console\Input\InputArgument;
use Symfony\Component\Console\Input\InputInterface;
use Symfony\Component\Console\Output\OutputInterface;
use Symfony\Component\Process\Process;
use Symfony\Component\Routing\RouterInterface;

#[AsCommand(name: 'eduprat:pdf_merged', description: "Génère le fichier PDF global d'une formation")]
class GenerateMergedPdfCommand extends Command
{
    const TYPE_PARTICIPATION = "participation";
    const TYPE_PARTICIPATION_HORARY = "participation_horary";
    const TYPE_AUDIT1 = "audit1";
    const TYPE_AUDIT2 = "audit2";
    const TYPE_SURVEY1 = "survey1";
    const TYPE_SURVEY2 = "survey2";
    const TYPE_RESTITUTION_AUDIT = "restitution_audit";
    const TYPE_PRERESTITUTION_AUDIT = "prerestitution_audit";

    /**
     * @var RouterInterface
     */
    private $router;
    /**
     * @var EntityManagerInterface
     */
    private $entityManager;

    /** @var string */
    private $projectDir;

    public function __construct(string $projectDir, RouterInterface $router, EntityManagerInterface $entityManager)
    {
        parent::__construct();
        $this->router = $router;
        $this->entityManager = $entityManager;
        $this->projectDir = $projectDir;
    }

    /**
     * {@inheritdoc}
     */
    protected function configure(): void
    {
        $this
            ->addArgument('id', InputArgument::REQUIRED, 'ID de la formation')
            ->addArgument('financeSousMode', InputArgument::OPTIONAL, 'ID du sous mode de financement')
        ;
    }

    /**
     * {@inheritdoc}
     */
    protected function execute(InputInterface $input, OutputInterface $output): int
    {
        $formation = $this->entityManager->getRepository(Formation::class)->find($input->getArgument('id'));
        $type = "fusion";
        if ($input->hasArgument('financeSousMode')) {
            $financeSousMode = $this->entityManager->getRepository(FinanceSousMode::class)->find($input->getArgument('financeSousMode'));
        } else {
            $financeSousMode = null;
        }
        try {
            if (!is_dir($formation->getGeneratedFileTmpBasePath($type, $financeSousMode))) {
                mkdir($formation->getGeneratedFileTmpBasePath($type, $financeSousMode), 0777, true);
            }

            if ($formation->hasGeneratedFile($type, $financeSousMode)) {
                unlink($formation->getGeneratedFilePath($type, $financeSousMode));
            }
            if ($formation->hasGeneratedFileError($type, $financeSousMode)) {
                unlink($formation->getGeneratedFileErrorPath($type, $financeSousMode));
            }
            touch($formation->getGeneratedFilePath($type, $financeSousMode));

            $urls = array(
                $this->router->generate('pdf_traceability_document_pdf', array('id' => $formation->getId(), 'financeSousMode' => $financeSousMode->getId(), 'token' => $formation->getToken()), RouterInterface::ABSOLUTE_URL),
                $this->router->generate('pdf_fusion_header_pdf', array('id' => $formation->getId(), 'token' => $formation->getToken()), RouterInterface::ABSOLUTE_URL),
                $this->router->generate('pdf_fusion_methodologie_pdf', array('id' => $formation->getId(), 'actalians' => $financeSousMode->isActalians()), RouterInterface::ABSOLUTE_URL),
                $this->router->generate('pdf_restitution_audit_groupe_pdf', array(
                    'id' => $formation->getId(),
                    'former' => $formation->getFormateurs()->first()->getId(),
                    'financeSousMode' => $financeSousMode->getId(),
                    'token' => $formation->getToken()
                ), RouterInterface::ABSOLUTE_URL),
                $this->router->generate('pdf_restitution_audit_groupe_2_pdf', array(
                    'id' => $formation->getId(),
                    'former' => $formation->getFormateurs()->first()->getId(),
                    'financeSousMode' => $financeSousMode->getId(),
                    'token' => $formation->getToken()
                ), RouterInterface::ABSOLUTE_URL),
            );

            $participations = $formation->getParticipationsPerMode($financeSousMode);
            $iterator = $participations->getIterator();
            $iterator->uasort(function (Participation $a, Participation $b) {
                return (ucfirst(strtolower($a->getParticipant()->getLastname())) < ucfirst(strtolower($b->getParticipant()->getLastname()))) ? -1 : 1;
            });
            $participations = array_values((new ArrayCollection(iterator_to_array($iterator)))->toArray());

            /** @var Participation $participation */
            foreach ($participations as $a => $participation) {

                $urls[] = $this->router->generate('pdf_audit_restitution_groupe_individuelle_pdf', array(
                    'id' => $participation->getId(),
                    'token' => $participation->getToken()
                ), RouterInterface::ABSOLUTE_URL);
                $urls[] = $this->router->generate('pdf_restitution_audit_pdf', array(
                    'id' => $participation->getId(),
                    'token' => $participation->getToken()
                ), RouterInterface::ABSOLUTE_URL);

                if ($formation->isAudit()) {
                    if ($formation->getAudit()->getNbPatients() <= 5) {
                        $urls[] = $this->router->generate('pdf_audit_pdf', array(
                            'id' => $participation->getId(),
                            'auditId' => 1,
                            'token' => $participation->getToken()
                        ), RouterInterface::ABSOLUTE_URL);
                        $urls[] = $this->router->generate('pdf_audit_pdf', array(
                            'id' => $participation->getId(),
                            'auditId' => 2,
                            'token' => $participation->getToken()
                        ), RouterInterface::ABSOLUTE_URL);
                    } else {
                        foreach ([1, 2] as $auditId) {
                            for ($i = 1; $i <= $formation->getAudit()->getNbPatients(); $i++) {
                                $urls[] = $this->router->generate('pdf_audit_pdf_patient', array(
                                    'id' => $participation->getId(),
                                    'auditId' => $auditId,
                                    'token' => $participation->getToken(),
                                    'patient' => $i,
                                ), RouterInterface::ABSOLUTE_URL);
                            }
                        }
                    }
                } else if ($formation->isPresentielle()) {
                    $urls[] = $this->router->generate('pdf_survey_pdf', array(
                        'id' => $participation->getId(),
                        'surveyId' => 1,
                        'token' => $participation->getToken()
                    ), RouterInterface::ABSOLUTE_URL);
                    $urls[] = $this->router->generate('pdf_survey_pdf', array(
                        'id' => $participation->getId(),
                        'surveyId' => 2,
                        'token' => $participation->getToken()
                    ), RouterInterface::ABSOLUTE_URL);
                }
            }

            $files = [];

            $folder = $this->projectDir . "/uploads/formations/fusion/";
            $tmpFolder = sprintf("%s%s%s/", $folder, $formation->getId(), $financeSousMode ? ("-" . $financeSousMode->getId()) : "");

            if (!is_dir($folder)) {
                mkdir($folder, 0777, true);
            }

            if (!is_dir($tmpFolder)) {
                mkdir($tmpFolder, 0777, true);
            }

            $pagesOffset = 0;
            $pageCount = 0;
            $startPage = 0;
            $summaryPages = ceil((count($participations) + 2) / 20);
            $summary = array();
            $nbPages = 0;
            $nbPagesbeforeSummary = 0;

            $sslContext = array(
                "ssl"=>array(
                    "verify_peer" => false,
                    "verify_peer_name" => false,
                ),
            );

            foreach ($urls as $i => $url) {
                if ($i === 2) {
                    $pagesOffset += $summaryPages;
                }
                $filename = sprintf("%s%s.pdf", $tmpFolder, $i);
                $files[] = $filename;
                $output->writeln("Génération de " . $url . "?offset=" . $pagesOffset);
                $fileSize = 0;
                $try = 0;
                while ($fileSize === 0 && $try < 3) {
                    $pdf = file_get_contents($url . "?offset=" . $pagesOffset, false, stream_context_create($sslContext));
                    file_put_contents($filename, $pdf);
                    $fileSize = filesize($filename);
                    $try++;
                }
                if($i >= 1) {
                    $nbPages = $this->getPDFPages($filename);
                }
                else {
                    $nbPagesbeforeSummary = $this->getPDFPages($filename);
                }
                if($i === 1) {
                    $summary = array(
                        array("title" => "Méthodologie", "pages" => [1 + $summaryPages + $nbPages, 1 + $summaryPages + $nbPages])
                    );
                }
                if ($i >= 3) {
                    $id = $i - 5;
                    if ($i === 3) {
                        $title = "Pré-restitution groupe";
                        $startPage = $pagesOffset + 1;
                        $pageCount = $nbPages;
                    } else if ($i === 4) {
                        $title = "Restitution groupe";
                        $startPage = $pagesOffset + 1;
                        $pageCount = $nbPages;
                    } else {
                        $moduloCpt = 4;
                        if ($formation->isAudit() && $formation->getAudit()->getNbPatients() > 5) {
                            $moduloCpt = ($formation->getAudit()->getNbPatients() * 2) + 2;
                        }
                        /** @var Participation $p */
                        if ($id % $moduloCpt === 0) {
                            $startPage = $pagesOffset + 1;
                            $cid = $id / $moduloCpt;
                            $p = $participations[$cid];
                            $title = sprintf("Dossier participant %s : %s", $cid + 1, sprintf("%s %s", $p->getParticipant()->getCivility(), $p->getParticipant()->getFullname()));
                        }
                        $pageCount += $nbPages;
                    }
                    if ($i === 3 || $i === 4 || $id % $moduloCpt === ($moduloCpt-1)) {
                        $summary[] = array("title" => $title, "pages" => [$startPage, $startPage + $pageCount - 1]);
                        $pageCount = 0;
                    }
                }
                $pagesOffset += $nbPages;
            }

            $output->writeln("Génération du sommaire");
            $summaryUrl = $this->router->generate('pdf_fusion_summary_pdf', array(), RouterInterface::ABSOLUTE_URL);

            $postdata = http_build_query(
                array(
                    'summary' => json_encode($summary)
                )
            );
            $opts = array(
                'http' =>
                    array(
                        'method'  => 'POST',
                        'header'  => 'Content-type: application/x-www-form-urlencoded',
                        'content' => $postdata
                    ),
                "ssl" => array(
                    "verify_peer" => false,
                    "verify_peer_name" => false,
                ),
            );
            $context  = stream_context_create($opts);
            $filename = sprintf("%s%s%s.pdf", $tmpFolder, "summary", $financeSousMode ? ("-" . $financeSousMode->getId()) : "");
            array_splice( $files, $nbPagesbeforeSummary, 0, [$filename] );
            $pdf = file_get_contents($summaryUrl, false, $context);
            file_put_contents($filename, $pdf);

            $outputFileTemp = sprintf("%s%s_temp.pdf", $folder, $formation->getId());
            $outputFile = sprintf("%s%s%s.pdf", $folder, $formation->getId(), $financeSousMode ? ("-" . $financeSousMode->getId()) : "");
            $process = new Process([
                'pdftk',
                ...$files,
                "cat",
                "output",
                $outputFileTemp
            ]);
            $process->run();
            rename($outputFileTemp, $outputFile);
            foreach ($files as $file) {
                unlink($file);
            }
            rmdir($tmpFolder);
        } catch (\Exception $e) {
            $output->writeln($e->getMessage());
            file_put_contents($formation->getGeneratedFileErrorPath($type), $e->getMessage());
            if ($formation->hasGeneratedFile($type)) {
                unlink($formation->getGeneratedFilePath($type));
            }
        }
        return Command::SUCCESS;
    }

    static public function getPDFPages($document)
    {
        $process = new Process([
            "pdfinfo",
            $document
        ]);

        $process->run();

        $output = array_filter(explode("\n", $process->getOutput()), 'strlen');

        $pagecount = 0;
        foreach($output as $op)
        {
            if(preg_match("/Pages:\s*(\d+)/i", $op, $matches) === 1)
            {
                $pagecount = intval($matches[1]);
                break;
            }
        }
        return $pagecount;
    }
}
