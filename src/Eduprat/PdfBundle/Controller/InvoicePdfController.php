<?php

namespace Eduprat\PdfBundle\Controller;

use Eduprat\DomainBundle\Entity\Formation;
use Eduprat\DomainBundle\Entity\Participation;
use Eduprat\DomainBundle\Entity\FinanceSousMode;
use Eduprat\PdfBundle\Services\InvoicePDF;
use Eduprat\DomainBundle\Services\InvoiceManager;
use Symfony\Component\HttpFoundation\Request;
use Symfony\Component\HttpFoundation\Response;
use Symfony\Component\Routing\Attribute\Route;
use Symfony\Contracts\Service\Attribute\Required;

class InvoicePdfController extends AbstractPdfController
{
    private InvoicePDF $invoicePDF;
    private InvoiceManager $invoiceManager;

    #[Required]
    public function withInvoicePDF(InvoicePDF $invoicePDF, InvoiceManager $invoiceManager): void
    {
        $this->invoicePDF = $invoicePDF;
        $this->invoiceManager = $invoiceManager;
    }

    #[Route(path: '/pdf/invoice_proforma/{id}/{financeSousMode}/{n1}/pdf/{token}', name: 'pdf_invoice_proforma_pdf', defaults: ['n1' => '0'], methods: ['GET'])]
    public function invoiceProforma(Formation $formation, FinanceSousMode $financeSousMode, Request $request, $n1 = null, $token = null): Response
    {
        $pageOffset = $request->query->get('offset');

        $this->denyAccessIfInvalidToken($formation, $token);

        $year = $n1 ? $formation->getOpeningDate()->format('Y')+1 : $formation->getOpeningDate()->format('Y') ;
        $invoice = $this->invoiceManager->getInvoiceProforma($formation, $financeSousMode, $year);

        $filename = "facture-".$invoice->getNumber().".pdf";

        $response =  $this->invoicePDF->generate(
            ['formation' => $formation, 'financeSousMode' => $financeSousMode, 'n1' => $n1, 'proforma' => true], null, ['pageOffset' => $pageOffset]
        );

        return new Response(
            $response->getBody(),
            Response::HTTP_OK,
            array(
                'Content-Type' => 'application/pdf',
                'Content-Disposition' => sprintf("inline; filename=\"%s\"", $filename)
            )
        );
    }

    #[Route(path: 'pdf/invoice/{id}/{financeSousMode}/{n1}/pdf/{token}', name: 'pdf_invoice_pdf', defaults: ['n1' => '0'], methods: ['GET'])]
    public function invoice(Formation $formation, FinanceSousMode $financeSousMode, Request $request, InvoiceManager $invoiceManager, $n1 = null, $token = null)
    {
        $pageOffset = $request->query->get('offset');

        $this->denyAccessIfInvalidToken($formation, $token);

        $year = $n1 ? ((int)$formation->getOpeningDate()->format('Y')) + 1 : $formation->getOpeningDate()->format('Y') ;
        $invoice = $invoiceManager->getInvoice($formation, $financeSousMode, $year);

        $filename = "facture-".$invoice->getNumber().".pdf";

        $response =  $this->invoicePDF->generate(
            ['formation' => $formation, 'financeSousMode' => $financeSousMode, 'n1' => $n1, 'proforma' => false], null, ['pageOffset' => $pageOffset]
        );

        return new Response(
            $response->getBody(),
            Response::HTTP_OK,
            array(
                'Content-Type' => 'application/pdf',
                'Content-Disposition' => sprintf("inline; filename=\"%s\"", $filename)
            )
        );
    }
}