<?php

declare(strict_types=1);

namespace Eduprat\PdfBundle\Controller;

use Symfony\Component\Routing\Attribute\Route;
use Eduprat\DomainBundle\Entity\Formation;
use Eduprat\PdfBundle\Services\FicheActionEmptyPDF;
use Symfony\Component\HttpFoundation\Request;
use Symfony\Component\HttpFoundation\Response;
use Symfony\Contracts\Service\Attribute\Required;

class FicheActionEmptyController extends AbstractPdfController
{

    private FicheActionEmptyPDF $ficheActionEmptyPDF;

    #[Required]
    public function withFicheActionEmptyPDF(FicheActionEmptyPDF $ficheActionEmptyPDF): void
    {
        $this->ficheActionEmptyPDF = $ficheActionEmptyPDF;
    }

    #[Route(path: '/pdf/fiche-action/empty/{id}/pdf/{token}', name: 'pdf_action_empty_pdf', methods: ['GET'])]
    public function ficheActionEmpty(
        Request $request,
        Formation $formation,
        string $token,
    ): Response
    {
//        $this->snappy->setOption('margin-top', 36);
//        $this->snappy->setOption('margin-bottom', 20);
//        $this->snappy->setOption('javascript-delay', 300);
//        $this->snappy->setOption('header-html', $this->generateUrl('pdf_header_evaluation_global', array("id" => $formation->getId()), UrlGeneratorInterface::ABSOLUTE_URL));
//        $this->snappy->setOption('footer-html', $this->generateUrl('pdf_footer_evaluation_global', array('offset' => $pageOffset), UrlGeneratorInterface::ABSOLUTE_URL));


        $this->denyAccessIfInvalidToken($formation, $token);

        $response =  $this->ficheActionEmptyPDF->generate(
            ['formation' => $formation, 'request' => $request], null, null
        );

        $filename = "action-vierge-".$formation->getProgramme()->getReference().".pdf";

        return new Response(
            $response->getBody()->getContents(),
            Response::HTTP_OK,
            [
                'Content-Type' => 'application/pdf',
                'Content-Disposition' => sprintf("inline; filename=\"%s\"", $filename)
            ]
        );
    }
}
