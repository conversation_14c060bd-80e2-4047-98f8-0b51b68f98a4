<?php

namespace Eduprat\PdfBundle\Controller;

use Doctrine\Common\Collections\ArrayCollection;
use Doctrine\ORM\EntityManagerInterface;
use Eduprat\AdminBundle\Entity\Person;
use Eduprat\AdminBundle\Services\CoordinatorHonorary;
use Eduprat\AdminBundle\Services\EvaluationReporting;
use Eduprat\AuditBundle\Form\EtutoratType;
use Eduprat\AuditBundle\Form\FicheProgressionCollectionType;
use Eduprat\AuditBundle\Form\SurveyType;
use Eduprat\AuditBundle\Services\AuditManager;
use Eduprat\AuditBundle\Services\FormManagerFactory;
use Eduprat\DomainBundle\Entity\FicheProgression;
use Eduprat\DomainBundle\Entity\FinanceSousMode;
use Eduprat\DomainBundle\Entity\Formateur;
use Eduprat\DomainBundle\Entity\Formation;
use Eduprat\DomainBundle\Entity\FormationAudit;
use Eduprat\DomainBundle\Entity\FormationPresentielle;
use Eduprat\DomainBundle\Entity\Participation;
use Eduprat\DomainBundle\Entity\Programme;
use Eduprat\DomainBundle\Services\InvoiceManager;
use Eduprat\DomainBundle\Services\RestitutionCalculator;
use Symfony\Bundle\FrameworkBundle\Controller\AbstractController;
use Symfony\Component\Routing\Attribute\Route;
use Symfony\Component\HttpKernel\Profiler\Profiler;
use Symfony\Component\Form\FormFactoryInterface;
use Symfony\Component\HttpFoundation\Request;
use Symfony\Component\HttpFoundation\Response;
use Eduprat\AdminBundle\Services\EvaluationCoordinatorByCoordinatorManager;
use Eduprat\DomainBundle\Entity\EvaluationCoordinator;
use Eduprat\DomainBundle\Entity\EvaluationProgramme;
use Eduprat\AdminBundle\Services\EvaluationProgrammeManager;
use Eduprat\AuditBundle\Services\CourseManager;
use Eduprat\DomainBundle\Entity\Coordinator;
use Eduprat\DomainBundle\Entity\Participant;
use Eduprat\DomainBundle\Services\PlaquetteManager;
use Symfony\Contracts\Translation\TranslatorInterface;

#[Route(path: '/pdf')]
class HtmlController extends AbstractController
{

    private ?Profiler $profiler;
    private CourseManager $courseManager;
    private PlaquetteManager $plaquetteManager;

    public function __construct(CourseManager $courseManager, PlaquetteManager $plaquetteManager, Profiler $profiler = null)
    {
        $this->profiler = $profiler;
        $this->courseManager = $courseManager;
        $this->plaquetteManager = $plaquetteManager;
    }

    public function disableToolbar(): void
    {
        $this->profiler?->disable();
    }

    /*** HEADERS ***/
    /**
     * @param Participation|null $participation
     * @return Response
     */
    #[Route(path: '/header-participation/{id}', name: 'pdf_header_participation', defaults: ['id' => null], methods: ['GET'])]
    public function headerParticipation(Participation $participation = null): Response
    {
        $this->disableToolbar();
        return $this->render('pdf/headers/participation.html.twig', array(
            "participation" => $participation
        ));
    }

    #[Route(path: '/header-dpc-attendance', name: 'pdf_header_dpc_attendance', methods: ['GET'])]
    public function headerDpcAttendance(): Response
    {
        $this->disableToolbar();
        return $this->render('pdf/headers/dpc.html.twig', array(
            'title' => "Attestation de suivi des étapes non présentielles d'un programme / d'une action de DPC"
        ));
    }

    #[Route(path: '/header-coordinator_honorary', name: 'pdf_header_coordinator_honorary', methods: ['GET'])]
    public function headerCoordinatorHonorary(TranslatorInterface $translator): Response
    {
        $this->disableToolbar();
        return $this->render('pdf/headers/budget-coordinator.html.twig', array(
            'title' => $translator->trans('compensation.detailsCrTitle')
        ));
    }

    #[Route(path: '/header-admin_budget_cr_total', name: 'pdf_header_admin_budget_cr_total', methods: ['GET'])]
    public function headerAdminBudgetCrTotal(TranslatorInterface $translator): Response
    {
        $this->disableToolbar();
        return $this->render('pdf/headers/budget-coordinator.html.twig', array(
            'title' => $translator->trans('compensation.crTitle')
        ));
    }

    #[Route(path: '/footer-budget-coordinator', name: 'pdf_footer_budget_coordinator', methods: ['GET'])]
    public function footerBudgetCoordinator(): Response
    {
        $this->disableToolbar();
        return $this->render('pdf/footers/budget-coordinator.html.twig');
    }

    #[Route(path: '/footer-evaluation', name: 'pdf_footer_evaluation', methods: ['GET'])]
    public function footerEvaluation(): Response
    {
        $this->disableToolbar();
        return $this->render('pdf/footers/evaluation.html.twig');
    }

    /**
     * @param Formation|null $formation
     * @return Response
     */
    #[Route(path: '/header-evaluation-global/{id}', name: 'pdf_header_evaluation_global', defaults: ['id' => null], methods: ['GET'])]
    public function headerEvaluationGlobal(Formation $formation = null): Response
    {
        $this->disableToolbar();
        return $this->render('pdf/headers/evaluation-global.html.twig', array(
            "formation" => $formation
        ));
    }


    #[Route(path: '/footer-evaluation-global', name: 'pdf_footer_evaluation_global', methods: ['GET'])]
    public function footerEvaluationGlobal(Request $request): Response
    {
        $this->disableToolbar();
	// Si la requête est trop rapide, wkhtmltopdf affiche une erreur ??? :)
	sleep(2);
        $pageOffset = $request->query->get('offset');
        return $this->render('pdf/footers/evaluation-global.html.twig', array(
            "offset" => $pageOffset
        ));
    }

    #[Route(path: '/footer-coordinateur/{coordinateur}/{hasDPC}', name: 'pdf_footer_coordinateur', methods: ['GET'])]
    public function footerCoordinateur(Request $request, Person $coordinateur, $hasDPC): Response
    {
        $this->disableToolbar();
        // Si la requête est trop rapide, wkhtmltopdf affiche une erreur ??? :)
        $pageOffset = $request->query->get('offset');
        return $this->render('pdf/footers/coordinateur.html.twig', array(
            "offset" => $pageOffset,
            "coordinateur" => $coordinateur,
            "hasDPC" => $hasDPC,
        ));
    }

    /*** FOOTERS ***/
    #[Route(path: '/footer-dpc', name: 'pdf_footer_dpc', methods: ['GET'])]
    public function footerDpc(): Response
    {
        $this->disableToolbar();
        return $this->render('pdf/footers/dpc.html.twig');
    }

    #[Route(path: '/certificate-attendance/{id}/html', name: 'pdf_certificate_attendance_html', methods: ['GET'])]
    public function certificateAttendance(Formation $formation): Response
    {
        // On ordonne les participants dans l'ordre alphabétique des lastnames
        $participations = $formation->getParticipations();
        $iterator = $participations->getIterator();
        $iterator->uasort(function ($a, $b) {
            return (ucfirst(strtolower($a->getParticipant()->getLastname())) < ucfirst(strtolower($b->getParticipant()->getLastname()))) ? -1 : 1;
        });
        $participations = new ArrayCollection(iterator_to_array($iterator));
        return $this->render('pdf/certificate_attendance.html.twig', array(
            'formation' => $formation,
            'participations' => $participations
        ));
    }

    #[Route(path: '/coordinator_honorary/{formation}/{coordinator}/{n1}/html', name: 'pdf_coordinator_honorary_html', methods: ['GET'])]
    public function coordinatorHonorary(Formation $formation, Coordinator $coordinator, CoordinatorHonorary $coordinatorHonoraryService, $n1 = null): Response
    {
        $openingDate = $formation->getOpeningDate();
        $closingDate = $formation->getClosingDate();
        $year = $n1 ? ((int) $openingDate->format("Y")) + 1 : $openingDate->format("Y");

        if ($formation->isPluriAnnuelle()) {
            $openingDate = $formation->getFirstUnityDateOfYear($year);
            $closingDate = $formation->getLastUnityDateOfYear($year);
        }

        $compensations = $coordinatorHonoraryService->calculHonorarySession($formation, $coordinator, $n1);

        $totalHonorary = $coordinatorHonoraryService->calculTotalHonorary($formation, $coordinator, true, false, true, $n1);

        return $this->render('pdf/coordinator_honorary.html.twig', array(
            'compensations' => $compensations,
            'formation' => $formation,
            'coordinator' => $coordinator,
            'newHonorary' => $formation->getStartDate() >= new \DateTime($this->getParameter('honoraires.migration_date')),
            'totalHonorary' => $totalHonorary,
            'openingDate' => $openingDate,
            'closingDate' => $closingDate,
            'year' => $year,
            'n1' => $n1,
        ));
    }

    /**
     * @param Coordinator $coordinator
     * @param Programme $programme
     * @return Response
     */
    #[Route(path: '/evaluation_coordinator_by_coordinator/{coordinator}/programme/{programme}/html/', name: 'pdf_evaluation_coordinator_by_coordinator_html', methods: ['GET'])]
    public function evaluationCoordinatorByCoordinator(Coordinator $coordinator, Programme $programme, EntityManagerInterface $entityManager): Response
    {

        /** @var EvaluationProgramme[] $evaluations */
        $evaluationsP = $entityManager->getRepository(EvaluationProgramme::class)->findByProgrammeCoordinator($programme, $coordinator);

        $repartitionsP = [];

        for ($i = 1; $i <= EvaluationProgrammeManager::NB_QUESTION; $i++) {
            $repartitionsP[$i] = array(
                "count" => 0,
                "total" => 0,
                "avg" => 0
            );
        }

        foreach ($evaluationsP as $evaluation) {
            $repartitionsP[$evaluation->getQuestion()]["count"]++;
            $repartitionsP[$evaluation->getQuestion()]["total"] += $evaluation->getAnswer();
        }

        foreach ($repartitionsP as &$repartition) {
            if ($repartition["count"] > 0) {
                $repartition["avg"] = $repartition["total"] / $repartition["count"];
            }
        }

        /** @var EvaluationProgramme[] $evaluations */
        $evaluationsC = $entityManager->getRepository(EvaluationCoordinator::class)->findByProgrammeCoordinator($programme, $coordinator);

        $repartitionsC = [];

        for ($i = 1; $i <= EvaluationCoordinatorByCoordinatorManager::NB_QUESTION-1; $i++) {
            $repartitionsC[$i] = array(
                "count" => 0,
                "total" => 0,
                "avg" => 0
            );
        }

        foreach ($evaluationsC as $evaluation) {
            if($evaluation->getQuestion() != 5) {
                $repartitionsC[$evaluation->getQuestion()]["count"]++;
                $repartitionsC[$evaluation->getQuestion()]["total"] += $evaluation->getAnswer();
            }
            else {
                $commentaire = $evaluation->getAnswer();
            }
        }

        foreach ($repartitionsC as &$repartition) {
            if ($repartition["count"] > 0) {
                $repartition["avg"] = $repartition["total"] / $repartition["count"];
            }
        }

        $participants = array();
        foreach($programme->getFormations() as $formation) {
            $participants[$formation->getId()] = $formation->getParticipants()->count();
        }

        return $this->render('pdf/evaluation_coordinator_by_coordinator.html.twig', array(
            'evaluationP' => $repartitionsP,
            'evaluationC' => $repartitionsC,
            'coordinator' => $coordinator,
            'programme' => $programme,
            'commentaire' => $commentaire,
            'participants' => $participants
        ));
    }

    /**
     * @param Participation $participation
     * @return Response
     */
    #[Route(path: '/certificate-participation-horary/{id}/html', name: 'pdf_certificate_participation_horary_html', methods: ['GET'])]
    public function certificateParticipationHonorary(Participation $participation): Response
    {
        return $this->render('pdf/certificate_participation_horary.html.twig', array(
            'participation' => $participation,
            'participant' => $participation->getParticipant()
        ));
    }

    /**
     * @param Formation $formation
     * @param FinanceSousMode $financeSousMode
     * @param $start
     * @param $batch
     * @return Response
     */
    #[Route(path: '/certificate-participation-horary-formation/{id}/{financeSousMode}/html/{start}/{batch}', name: 'pdf_certificate_participation_horary_formation_html', methods: ['GET'])]
    public function certificateParticipationHoraryFormation(EntityManagerInterface $entityManager, Formation $formation, FinanceSousMode $financeSousMode, $start, $batch): Response
    {
        $participationRepo = $entityManager->getRepository(Participation::class);
        $participations = $participationRepo->findByBatch($formation, $financeSousMode, $start, $batch);

        $this->disableToolbar();
        return $this->render('pdf/certificate_participation_horary_all.html.twig', array(
            'formation' => $formation,
            'participations' => $participations
        ));
    }

    /**
     * @param Formation $formation
     * @param FinanceSousMode $financeSousMode
     * @return Response
     */
    #[Route(path: '/certificate-participation-horary-formation-opti/{id}/{financeSousMode}/html', name: 'pdf_certificate_participation_horary_formation_opti_html', methods: ['GET'])]
    public function certificateParticipationHoraryOptiFormation(EntityManagerInterface $entityManager, Formation $formation, FinanceSousMode $financeSousMode): Response
    {

        $participations = [];

        foreach($formation->getParticipations() as $participation) {
            if($participation->getFinanceSousMode() == $financeSousMode) {
                $participations[] = $participation;
            }
        }

        $this->disableToolbar();
        return $this->render('pdf/certificate_participation_horary_all.html.twig', array(
            'formation' => $formation,
            'participations' => $participations
        ));
    }

    /**
     * @param Formation $formation
     * @param FinanceSousMode $financeSousMode
     * @return Response
     */
    #[Route(path: '/tcs-answers/{id}/{financeSousMode}/html', name: 'pdf_tcs_answers_html', methods: ['GET'])]
    public function tcsAnswers(EntityManagerInterface $entityManager, Formation $formation, FinanceSousMode $financeSousMode): Response
    {
        $participations = [];
        $questionnaireTcs = $formation->getAudit();

        foreach($formation->getParticipations() as $participation) {
            if ($participation->getFinanceSousMode() == $financeSousMode && $participation->isStepCompleted("form_presession")) {
                $participations[] = $participation;
            }
        }

        $this->disableToolbar();
        return $this->render('pdf/tcs_answers.html.twig', array(
            'formation' => $formation,
            'questionnaireTcs' => $questionnaireTcs,
            'participations' => $participations
        ));
    }

    /**
     * @param Formateur $former
     * @param Formation $formation
     * @return Response
     */
    #[Route(path: '/contract-former/{id}/{formation}/html', name: 'pdf_contract_former_html', methods: ['GET'])]
    public function formerContract(Formateur $former, Formation $formation): Response
    {
        $this->disableToolbar();
        return $this->render('pdf/former_contract.html.twig', array(
            'former' => $former,
            'formation' => $formation
        ));
    }

    /**
     * @param Formation $formation
     * @param FinanceSousMode $financeSousMode
     * @return Response
     */
    #[Route(path: '/convention-pharmacie/{id}/{financeSousMode}/html', name: 'pdf_convention_pharmacie_html', methods: ['GET'])]
    public function conventionPharmacie(Formation $formation, FinanceSousMode $financeSousMode): Response
    {
        $this->disableToolbar();
        return $this->render("pdf/convention_pharmacie.html.twig", array(
            'formation' => $formation,
            'financeSousMode' => $financeSousMode,
        ));
    }

    /**
     * @param Formation $formation
     * @param FinanceSousMode $financeSousMode
     * @return Response
     */
    #[Route(path: '/convention/{id}/{financeSousMode}/html', name: 'pdf_convention_html', methods: ['GET'])]
    #[Route(path: '/convention/{id}/html', name: 'pdf_convention_empty_html', methods: ['GET'])]
    public function convention(Formation $formation, FinanceSousMode $financeSousMode = null): Response
    {
        $this->disableToolbar();
        return $this->render("pdf/convention.html.twig", array(
            'formation' => $formation,
            'financeSousMode' => $financeSousMode,
        ));
    }

    #[Route(path: '/emargement/{id}/{financeSousMode}/html', name: 'pdf_emargement_html', methods: ['GET'])]
    #[Route(path: '/emargement/{id}/{financeSousMode}/{unityPosition}/html', name: 'pdf_emargement_unity_html', methods: ['GET'])]
    public function emargement(Formation $formation, FinanceSousMode $financeSousMode, Request $request, $unityPosition = null): Response
    {
        // On ordonne les participants dans l'ordre alphabétique des lastnames
        $participations = $formation->getParticipationsPerMode($financeSousMode);
        $iterator = $participations->getIterator();
        $iterator->uasort(function ($a, $b) {
            return (ucfirst(strtolower($a->getParticipant()->getLastname())) < ucfirst(strtolower($b->getParticipant()->getLastname()))) ? -1 : 1;
        });
        $participations = new ArrayCollection(iterator_to_array($iterator));
        $this->disableToolbar();

        $formateurs = $formation->getFormateurs();

        $template = $financeSousMode->isHorsDPC() || $financeSousMode->isFif() || $financeSousMode->isActalians() ? 'emargement_hors_dpc' : 'emargement';

        return $this->render('pdf/' . $template . '.html.twig', array(
            'participations' => $participations,
            'formation' => $formation,
            'formateurs' => $formateurs,
            'date' => $request->query->get('date'),
            'unityPosition' => $unityPosition,
        ));
    }

    /**
     * @param Participation $participation
     * @return Response
     */
    #[Route(path: '/restitution-audit/{id}/html', name: 'pdf_audit_restitution')]
    public function restitutionAudit(Participation $participation, RestitutionCalculator $restitutionCalculator): Response
    {
        $this->disableToolbar();

        $restitutionDatas = $restitutionCalculator->getRestitution($participation, array(
            "axis_audit" => 2,
            "auditId" => 2,
        ));

        return $this->render('pdf/restitution_audit.html.twig', array_merge(array(
            'participation' => $participation,
            'oneCoordinator' => true,
            'formation' => $participation->getFormation(),
        ), $restitutionDatas));
    }

    /**
     * @param Formation $formation
     * @param RestitutionCalculator $restitutionCalculator
     * @return Response
     * @internal param \Eduprat\DomainBundle\Entity\Participation $participation
     */
    #[Route(path: '/restitution-audit-groupe/{id}/html', name: 'pdf_audit_restitution_groupe')]
    public function restitutionAuditGroupe(Formation $formation, RestitutionCalculator $restitutionCalculator): Response
    {
        $this->disableToolbar();

        if ($formation->isTcs()) {
            $restitutionDatas = $restitutionCalculator->getTcsRestitutionFormation($formation, array(
                "audit" => 1
            ));
        } else {
            $restitutionDatas = $restitutionCalculator->getRestitutionFormation($formation, array(
                "audit" => 1
            ));
        }

        $template = $formation->isTcs() ?  "pdf/restitution_tcs_groupe.html.twig" : "pdf/restitution_audit_groupe.html.twig";
        return $this->render($template, array_merge(array(
            "auditId" => 1,
        ), $restitutionDatas));
    }

    /**
     * @param Formation $formation
     * @param RestitutionCalculator $restitutionCalculator
     * @return Response
     * @internal param \Eduprat\DomainBundle\Entity\Participation $participation
     */
    #[Route(path: '/restitution-audit-groupe-2/{id}/html', name: 'pdf_audit_restitution_groupe_2')]
    public function restitutionAuditGroupe2(Formation $formation, RestitutionCalculator $restitutionCalculator): Response
    {
        $this->disableToolbar();

        $restitutionDatas = $restitutionCalculator->getRestitutionFormation($formation, array(
            "audit" => 2
        ));

        return $this->render('pdf/restitution_audit_groupe.html.twig', array_merge(array(
            "auditId" => 2
        ), $restitutionDatas));
    }

    /**
     * @param Formation $formation
     * @return Response
     * @internal param \Eduprat\DomainBundle\Entity\Participation $participation
     */
    #[Route(path: '/attestation-honneur/{id}/{participant}/{person}/{n1}/html', name: 'pdf_attestation_honneur')]
    public function attestationHonneur(Formation $formation, Participant $participant = null, Person $person = null, $n1 = null): Response
    {
        $this->disableToolbar();

        if ($participant) {
            $participants = [$participant];
        } else {
            $participants = [];
            foreach($formation->getParticipations() as $participation) {
                if($person) {
                    if($participation->getCoordinator() && $participation->getCoordinator()->getPerson() == $person) {
                        $participants[] = $participation->getParticipant();
                    }
                } else {
                    $participants[] = $participation->getParticipant();
                }
            }

        }

        $unities = $formation->getProgramme()->getUnities()->toArray();
        $unity1 = $unities[0] ?? null;
        $unity3 = $unities[2] ?? null;

        $openingDate = $formation->getOpeningDate();
        $closingDate = $formation->getClosingDate();
        $year = $n1 ? ((int)$openingDate->format("Y")) + 1 : $openingDate->format("Y");

        if ($formation->isPluriAnnuelle()) {
            $openingDate = $formation->getFirstUnityDateOfYear($year);
            $closingDate = $formation->getLastUnityDateOfYear($year);
            $unity1 = $n1 ? null : $unity1; // L'unité 1 si existante est forcément en année n+0 donc inclue uniquement pour l'attestation n+0
            $unity3 = $n1 ? $unity3 : null; // L'unité 3 si existante est forcément en année n+1 donc inclue uniquement pour l'attestation n+1
        }

        usort($participants, function($a, $b) {return strcmp($a->getLastName(), $b->getLastName());});
        $template = $year >= 2023 ? "pdf/attestation_honneur_2023.html.twig" : "pdf/attestation_honneur.html.twig";
        return $this->render($template, ['formation' => $formation, 'participants' => $participants, "unity1" => $unity1, "unity3" => $unity3, "openingDate" => $openingDate, "closingDate" => $closingDate, "year" => $year]);
    }

    /**
     * @param Formation $formation
     * @return Response
     */
    #[Route(path: '/auto-evaluations/{id}/html', name: 'pdf_auto_evaluations')]
    public function autoEvaluations(Formation $formation, RestitutionCalculator $restitutionCalculator): Response
    {
        $this->disableToolbar();
        return $this->render('pdf/auto_evaluations.html.twig', ['formation' => $formation, 'datas' => $restitutionCalculator->getAutoEvalsDatas($formation)]);
    }

    /**
     * @param FormationAudit $formation
     * @param Formateur $former
     * @return Response
     * @internal param \Eduprat\DomainBundle\Entity\Participation $participation
     */
    #[Route(path: '/restitution-audit-groupe-individuelle/{participation}/html', name: 'pdf_audit_restitution_groupe_individuelle')]
    public function restitutionAuditGroupeIndividuelle(Participation $participation, RestitutionCalculator $restitutionCalculator): Response
    {
        $this->disableToolbar();

        $restitutionDatas = $restitutionCalculator->getRestitution($participation, array(
            "axis_audit" => 1,
            "auditId" => 1,
        ));

        return $this->render('pdf/restitution_audit_groupe_individuelle.html.twig', array_merge(array(
            'participation' => $participation,
            'oneCoordinator' => true,
            'formation' => $participation->getFormation(),
        ), $restitutionDatas));
    }

    #[Route(path: '/header-restitution', name: 'pdf_header_restitution')]
    public function headerRestitution(): Response
    {
        $this->disableToolbar();
        return $this->render('pdf/headers/restitution.html.twig');
    }

    /**
     * @param Request $request
     * @return Response
     */
    #[Route(path: '/footer-restitution', name: 'pdf_footer_restitution', methods: ['GET'])]
    public function footerRestitution(Request $request): Response
    {
        $this->disableToolbar();
        $pageOffset = $request->query->get('offset');
        $padding = $request->query->get('padding');
        return $this->render('pdf/footers/restitution.html.twig', array(
            'title' => $request->query->get('title'),
            'offset' => $pageOffset,
            'padding' => $padding
        ));
    }

    /**
     * @param Request $request
     * @return Response
     */
    #[Route(path: '/footer-contract', name: 'pdf_footer_contract', methods: ['GET'])]
    public function footerContract(): Response
    {
        $this->disableToolbar();
        return $this->render('pdf/footers/contract.html.twig');
    }

    /**
     * @param Request $request
     * @return Response
     */
    #[Route(path: '/footer-audit', name: 'pdf_footer_audit', methods: ['GET'])]
    public function footerAudit(Request $request): Response
    {
        $this->disableToolbar();
        $pageOffset = $request->query->get('offset');
        $padding = $request->query->get('padding');
        return $this->render('pdf/footers/audit.html.twig', array('offset' => $pageOffset, 'padding' => $padding));
    }

    /**
     * @param Formation $formation
     * @param $auditId
     * @param int $patient
     * @param Request $request
     * @param FormManagerFactory $formManagerFactory
     * @return Response
     * @internal param \Eduprat\DomainBundle\Entity\Participation $participation
     * @internal param int $auditId
     */
    #[Route(path: '/audit/empty/{id}/{auditId}/{patient}/html', name: 'pdf_audit_empty_html')]
    public function auditEmpty(Formation $formation, $auditId, $patient, Request $request, FormManagerFactory $formManagerFactory): Response
    {
        $patient = (int) $patient;
        $auditId = (int) $auditId;

        $questions = $auditId === AuditManager::SECOND_AUDIT && $formation->isVignette() ? $formation->getAudit2()->getQuestions() : $formation->getAudit()->getQuestions();
        $questions->forAll(function($key, $question) {
            $question->setIndex($key);
            return true;
        });

        $participation = new Participation();
        $participation->setParticipant(new Participant());
        $participation->setFormation($formation);

        $auditManager = $formManagerFactory->getAuditManager($participation, $auditId, $patient);

        $audit = $auditManager->getAudit();
        $form = $auditManager->getForm();

        $form->handleRequest($request);

        $this->disableToolbar();
        return $this->render('pdf/audit.html.twig', array(
            'patient' => $patient,
            "participation" => $participation,
            'form' => $form,
            'auditLabel' => $formation->getAuditLabel($auditId),
            'auditId' => $auditId,
            'audit' => $audit
        ));
    }

    /**
     * @param int                                       $patient
     * @param Request $request
     * @return Response
     * @internal param \Eduprat\DomainBundle\Entity\Participation $participation
     * @internal param int $surveyId
     */
    #[Route(path: '/etutorat/empty/{id}/html', name: 'pdf_etutorat_empty_html')]
    public function etutoratEmpty(Formation $formation, Request $request): Response
    {
        $participation = new Participation();
        $participation->setFormation($formation);

        $form = $this->createForm(EtutoratType::class, null, array(
            "participation" => $participation
        ));

        $programme = $participation->getFormation()->getProgramme();
        $hasEtutorat = $programme->hasEtutorat();
        $form->handleRequest($request);

        $this->disableToolbar();
        return $this->render('pdf/empty/etutorat.html.twig', array(
            "participation" => $participation,
            "formation" => $participation->getFormation(),
            "hasEtutorat" => $hasEtutorat,
            'form' => $form,
            'forPdf' => true,
        ));
    }

    /**
     * @param int                                       $patient
     * @param Request $request
     * @return Response
     * @internal param \Eduprat\DomainBundle\Entity\Participation $participation
     * @internal param int $surveyId
     */
    #[Route(path: '/fiche-progression/empty/{id}/html', name: 'pdf_progression_empty_html')]
    public function progressionEmpty(Formation $formation, Request $request): Response
    {
        $participation = new Participation();
        $participation->setFormation($formation);

        /** @var FormationAudit $formation */
        $formation = $participation->getFormation();

        foreach ($formation->getAudit()->getPatientDescriptions() as $patient) {
            $sampleProgression = new FicheProgression();
            $sampleProgression->setPatient($patient);
            $participation->addFicheProgression($sampleProgression);
        }

        $form = $this->createForm(FicheProgressionCollectionType::class, $participation, array(
            "editable" => true
        ));

        $programme = $participation->getFormation()->getProgramme();
        $hasEtutorat = $programme->hasEtutorat();
        $form->handleRequest($request);

        $this->disableToolbar();
        return $this->render('pdf/empty/progression.html.twig', array(
            "participation" => $participation,
            "formation" => $participation->getFormation(),
            "hasEtutorat" => $hasEtutorat,
            'form' => $form,
            "editable" => true
        ));
    }

    /**
     * @param int                                       $patient
     * @param Request $request
     * @return Response
     * @internal param \Eduprat\DomainBundle\Entity\Participation $participation
     * @internal param int $surveyId
     */
    #[Route(path: '/fiche-synthese/empty/{id}/{participation}/html', name: 'pdf_synthese_empty_html')]
    public function syntheseEmpty(EntityManagerInterface $entityManager, Formation $formation, $participation, Request $request): Response
    {
        $participant = null;
        if ($participation != "null") {
            $participation = $entityManager->getRepository(Participation::class)->findOneBy(array('id' => $participation));
            $participant = $participation->getParticipant();
        }

        $participation = new Participation();
        $participation->setFormation($formation);

        $form = $this->createForm(EtutoratType::class, null, array(
            "participation" => $participation,
            "synthese" => true,
            "disabled" => false
        ));

        $programme = $participation->getFormation()->getProgramme();
        $hasEtutorat = $programme->hasEtutorat();
        $form->handleRequest($request);

        $this->disableToolbar();
        return $this->render('pdf/empty/synthese.html.twig', array(
            "participant" => $participant,
            "participation" => $participation,
            "formation" => $participation->getFormation(),
            "hasEtutorat" => $hasEtutorat,
            'form' => $form,
        ));
    }

    /**
     * @param int                                       $patient
     * @param Request $request
     * @return Response
     * @internal param \Eduprat\DomainBundle\Entity\Participation $participation
     * @internal param int $surveyId
     */
    #[Route(path: '/fiche-synthese/full/{id}/{participation}/html', name: 'pdf_synthese_full_html')]
    public function syntheseFull(EntityManagerInterface $entityManager, $participation, Request $request): Response
    {
        $participant = null;
        if ($participation != "null") {
            $participation = $entityManager->getRepository(Participation::class)->findOneBy(array('id' => $participation));
            $participant = $participation->getParticipant();
        }

        // $participation = new Participation();
        // $participation->setFormation($formation);

        $form = $this->createForm(EtutoratType::class, null, array(
            "participation" => $participation,
            "synthese" => true,
            "disabled" => false
        ));

        $programme = $participation->getFormation()->getProgramme();
        $hasEtutorat = $programme->hasEtutorat();
        $form->handleRequest($request);

        $this->disableToolbar();
        return $this->render('pdf/empty/synthese.html.twig', array(
            "participant" => $participant,
            "participation" => $participation,
            "formation" => $participation->getFormation(),
            "hasEtutorat" => $hasEtutorat,
            'form' => $form,
        ));
    }

    /**
     * @param Participation $participation
     * @param Request $request
     * @return Response
     */
    #[Route(path: '/tcs/{id}/html', name: 'pdf_tcs_html')]
    public function tcs(EntityManagerInterface $entityManager, $id, Request $request): Response
    {
        $participation = $entityManager->getRepository(Participation::class)->findWithJoins($id);
        $formation = $participation->getFormation();
        $questionnaireTcs = $formation->getAudit();

        $this->disableToolbar();
        return $this->render('pdf/tcs.html.twig', array(
            'participation' => $participation,
            'formation' => $formation,
            'questionnaireTcs' => $questionnaireTcs
        ));
    }

    /**
     * @param Formation $formation
     * @param Request $request
     * @return Response
     */
    #[Route(path: '/tcs/empty/{id}/html', name: 'pdf_tcs_empty_html')]
    public function tcsEmpty(EntityManagerInterface $entityManager, $id, Request $request): Response
    {
        $formation = $entityManager->getRepository(Formation::class)->findOneById($id);
        $questionnaireTcs = $formation->getAudit();

        $this->disableToolbar();
        return $this->render('pdf/tcs_empty.html.twig', array(
            'formation' => $formation,
            'questionnaireTcs' => $questionnaireTcs
        ));
    }

    /**
     * @param Participation $participation
     * @param int                                       $surveyId
     * @param Request $request
     * @return Response
     */
    #[Route(path: '/survey/{id}/{surveyId}/html', name: 'pdf_survey_html')]
    public function survey(Participation $participation, $surveyId, Request $request, FormManagerFactory $formManagerFactory): Response
    {
        $surveyId = (int) $surveyId;

        $surveyManager = $formManagerFactory->getSurveyManager($participation, $surveyId);

        $form = $surveyManager->getForm();

        $form->handleRequest($request);

        $this->disableToolbar();
        return $this->render('pdf/survey.html.twig', array(
            'participation' => $participation,
            'surveyId' => $surveyId,
            'form' => $form,
            'surveyLabel' => $participation->getFormation()->getQuestionnaire()->getLabel()
        ));
    }

    /**
     * @param Formation $formation
     * @param int                                   $auditId
     * @return Response
     */
    #[Route(path: '/audit-answers/{id}/{financeSousMode}/{auditId}/{start}/{batch}/html', name: 'pdf_audit_answers_html')]
    public function auditAnswers(EntityManagerInterface $entityManager, Formation $formation, FinanceSousMode $financeSousMode, $auditId, $start, $batch): Response
    {
        $nbPatients = $formation->getAudit()->getNbPatients();

        $participationRepo = $entityManager->getRepository(Participation::class);
        $participations = $participationRepo->findByBatch($formation, $financeSousMode, $start, $batch);

        $this->disableToolbar();

        return $this->render('pdf/audit_answers.html.twig', array(
            'formation' => $formation,
            'participations' => $participations,
            'nbPatients' => $nbPatients,
            'auditId' => (int) $auditId
        ));
    }

    /**
     * @param Formation $formation
     * @param int                                       $surveyId
     * @return Response
     */
    #[Route(path: '/survey-answers/{id}/{financeSousMode}/{surveyId}/{start}/{batch}/html', name: 'pdf_survey_answers_html')]
    public function surveyAnswers(EntityManagerInterface $entityManager, Formation $formation, FinanceSousMode $financeSousMode, $surveyId, $start, $batch): Response
    {
        $participationRepo = $entityManager->getRepository(Participation::class);
        $participations = $participationRepo->findByBatch($formation, $financeSousMode, $start, $batch);

        $this->disableToolbar();

        return $this->render('pdf/survey_answers.html.twig', array(
            'formation' => $formation,
            'participations' => $participations,
            'surveyId' => (int) $surveyId
        ));
    }

    /**
     * @param Formation $formation
     * @return Response
     */
    #[Route(path: '/fusion-header/{id}/html', name: 'pdf_fusion_header_html')]
    public function fusionHeader(Formation $formation): Response
    {
        $this->disableToolbar();
        return $this->render('pdf/fusion/header.html.twig', array(
            'formation' => $formation,
        ));
    }

    /**
     * @param Formation $formation
     * @param Request $request
     * @return Response
     */
    #[Route(path: '/fusion-methodologie/{id}/html', name: 'pdf_fusion_methodologie_html')]
    public function fusionMethodologie(Formation $formation, Request $request): Response
    {
        $this->disableToolbar();
        $isActalians = json_decode($request->query->get('actalians'), true);
        $template = $isActalians ? 'pdf/fusion/methodologie_actalians.html.twig' : 'pdf/fusion/methodologie.html.twig';
        return $this->render($template, array("formation" => $formation));
    }

    /**
     * @return Response
     */
    #[Route(path: '/fusion-summary/html', name: 'pdf_fusion_summary_html')]
    public function fusionSummary(Request $request): Response
    {
        $this->disableToolbar();
        $summary = json_decode($request->request->get('summary'), true);
        return $this->render('pdf/fusion/summary.html.twig', array(
            'summary' => $summary,
        ));
    }


    /**
     * @param Request $request
     * @param Programme $programme
     * @return Response
     */
    #[Route(path: '/programme/{programme}/{role}/html', name: 'pdf_evaluation_programme_global_role', methods: ['GET'])]
    public function pdfEvaluationGlobalProgramme(Programme $programme, $role, EvaluationReporting $evaluationReporting, TranslatorInterface $translator): Response
    {
        $this->disableToolbar();
        $role = Person::ROLE_MAPPING[$role];
        $reporting = $evaluationReporting->getProgrammeReport($programme, $role, true);
        return $this->render('pdf/base-report.html.twig', array(
            'programme' => $programme,
            'role' => $role,
            'reporting' => $reporting,
            'evaluation_title' => sprintf("Bilan pédagogique de la formation «&nbsp;%s&nbsp;»", $programme->getFormations()->first()->getProgramme()->getReference()),
            'evaluation_subtitle' => $translator->trans('evaluation.subtitle.' . $role)
        ));
    }

    /**
     * @param Request $request
     * @param Formation $formation
     * @param $role
     * @param EvaluationReporting $evaluationReporting
     * @param TranslatorInterface $translator
     */
    #[Route(path: '/formation/{formation}/{role}/html', name: 'pdf_evaluation_formation_global_role', methods: ['GET'])]
    public function pdfEvaluationGlobalFormation(Formation $formation, $role, EvaluationReporting $evaluationReporting, TranslatorInterface $translator, RestitutionCalculator $restitutionCalculator): Response
    {
        $this->disableToolbar();
        $syntheseDatas = $role == "participant" ? $restitutionCalculator->getFicheSyntheseDatas($formation) : null;
        $role = Person::ROLE_MAPPING[$role];
        $reporting = $evaluationReporting->getFormationReport($formation, $role, true);
        return $this->render('pdf/base-report.html.twig', array(
            'formation' => $formation,
            'role' => $role,
            'reporting' => $reporting,
            'evaluation_title' => sprintf("Bilan pédagogique de la session %s n°%s", $formation->getProgramme()->getReference(), $formation->getSessionNumber()),
            'evaluation_subtitle' => $translator->trans('evaluation.subtitle.session.' . $role),
            'syntheseDatas' => $syntheseDatas
        ));
    }

    /**
     * @param Request $request
     * @param Formation $formation
     * @param Formateur $former
     * @return Response
     */
    #[Route(path: '/formation/{formation}/former/{former}/html', name: 'pdf_evaluation_formation_global_former', methods: ['GET'])]
    public function pdfEvaluationGlobalFormer(Formation $formation, Formateur $former, EvaluationReporting $evaluationReporting, TranslatorInterface $translator): Response
    {
        $this->disableToolbar();
        $reporting = $evaluationReporting->getFormerReport($former, true);
        return $this->render('pdf/base-report.html.twig', array(
            'formation' => $formation,
            'former' => $former,
            'reporting' => $reporting,
            'evaluation_title' => $translator->trans('evaluationGlobal.titles.former_programme', array(
                "%civility%" => $former->getPerson()->getCivility(),
                "%fullname%" => $former->getPerson()->getFullname(),
            )),
            'evaluation_subtitle' => $translator->trans('evaluationGlobal.titles.former_programme_2', array(
                "%reference%" => $formation->getProgramme()->getReference(),
                "%session%" => $formation->getSessionNumber()
            ))
        ));
    }

    /**
     * @param Request $request
     * @param Person $person
     * @param $year
     */
    #[Route(path: '/former/{person}/{year}/html', name: 'pdf_evaluation_global_former_year', methods: ['GET'])]
    public function pdfEvaluationGlobalFormerYear(Person $person, $year, EvaluationReporting $evaluationReporting, TranslatorInterface $translator): Response
    {
        $this->disableToolbar();
        $reporting = $evaluationReporting->getFormerReportYear($person, $year, true);
        return $this->render('pdf/base-report.html.twig', array(
            'year' => $year,
            'person' => $person,
            'reporting' => $reporting,
            'evaluation_title' => $translator->trans('evaluationGlobal.titles.former_annual', array(
                "%civility%" => $person->getCivility(),
                "%fullname%" => $person->getFullname(),
                "%year%" => $year,
            )),
            'evaluation_subtitle' => $translator->trans('evaluationGlobal.titles.former_annual_2')
        ));
    }

    /**
     * @param Request $request
     * @param Person $person
     * @param $year
     * @return Response
     */
    #[Route(path: '/coordinator/{person}/{year}/html', name: 'pdf_evaluation_global_coordinator_year', methods: ['GET'])]
    public function pdfEvaluationGlobalCoordinatorYear(Person $person, $year, EvaluationReporting $evaluationReporting, TranslatorInterface $translator): Response
    {
        $this->disableToolbar();
        $reporting = $evaluationReporting->getCoordinatorReportYear($person, $year, true);
        return $this->render('pdf/evaluation_reporting_coordinator_year.html.twig', array(
            'year' => $year,
            'person' => $person,
            'reporting' => $reporting,
            'evaluation_title' => $translator->trans('evaluationGlobal.titles.coordinator_annual', array(
                "%civility%" => $person->getCivility(),
                "%fullname%" => $person->getFullname(),
                "%year%" => $year,
            )),
        ));
    }

    /**
     * @param Request $request
     * @param $title
     * @param $role
     * @param $year
     * @return Response
     */
    #[Route(path: '/topo/{year}/{role}/{title}/html', name: 'pdf_evaluation_global_topo', requirements: ['title' => '.+'], methods: ['GET'])]
    public function pdfEvaluationGlobalTopo(string $title, $role, $year, EvaluationReporting $evaluationReporting, TranslatorInterface $translator): Response
    {
        $this->disableToolbar();
        $role = Person::ROLE_MAPPING[$role];
        $reporting = $evaluationReporting->getTopoByYearAndRole($title, $year, $role, true);
        return $this->render('pdf/base-report.html.twig', array(
            'title' => $title,
            'role' => $role,
            'year' => $year,
            'reporting' => $reporting,
            'evaluation_title' => $translator->trans('evaluationGlobal.titles.topo_' . ($role === Person::ROLE_PARTICIPANT ? 'participant' : 'former'), array(
                "%title%" => $title,
                "%year%" => $year
            )),
            'evaluation_subtitle' => $translator->trans('evaluation.subtitle.' . $role)
        ));
    }

    /**
     * @param Formation $formation
     * @param FinanceSousMode $financeSousMode
     * @param $start
     * @param $batch
     * @return Response
     */
    #[Route(path: '/document_realisation/{id}/{financeSousMode}/html/{start}/{batch}', name: 'pdf_document_realisation_formation_html', methods: ['GET'])]
    public function documentRealisationFormation(EntityManagerInterface $entityManager, Formation $formation, FinanceSousMode $financeSousMode, $start, $batch): Response
    {
        $participationRepo = $entityManager->getRepository(Participation::class);
        $participations = $participationRepo->findByBatch($formation, $financeSousMode, $start, $batch);

        $this->disableToolbar();
        return $this->render('pdf/document_realisation.html.twig', array(
            'formation' => $formation,
            'participations' => $participations
        ));
    }

    /**
     * @return Response
     */
    #[Route(path: '/header-realisation/{id}', name: 'pdf_header_realisation', defaults: ['id' => null], methods: ['GET'])]
    public function headerRealisation(): Response
    {
        $this->disableToolbar();
        return $this->render('pdf/headers/realisation.html.twig');
    }

    /**
     * @param Formation $formation
     * @param FinanceSousMode $financeSousMode
     * @return Response
     */
    #[Route(path: '/attestation-presence/{id}/{financeSousMode}/html/{start}/{batch}', name: 'pdf_attestation-presence_html', methods: ['GET'])]
    public function attestationPresenceDocument(EntityManagerInterface $entityManager, Formation $formation, FinanceSousMode $financeSousMode, $start, $batch): Response
    {

        $participationRepo = $entityManager->getRepository(Participation::class);
        $participations = $participationRepo->findByBatch($formation, $financeSousMode, $start, $batch);
        $this->disableToolbar();

        return $this->render('pdf/attestation_presence.html.twig', array(
            'formation' => $formation,
            'participations' => $participations
        ));
    }

    #[Route(path: '/header-attestation-presence-document', name: 'pdf_header_attestation_presence_document', methods: ['GET'])]
    public function headerAttestationPresenceDocument(): Response
    {
        $this->disableToolbar();
        return $this->render('pdf/headers/presence.html.twig');
    }

     #[Route(path: '/footer-plaquette-programme', name: 'pdf_footer_plaquette_programme', methods: ['GET'])]
    public function footerPlaquette(Request $request): Response
     {
        $this->disableToolbar();
        // Si la requête est trop rapide, wkhtmltopdf affiche une erreur ??? :)
        $pageOffset = $request->query->get('offset');
        return $this->render('pdf/footers/plaquette-programme.html.twig', array(
            "offset" => $pageOffset
        ));
    }
}
