<?php

declare(strict_types=1);

namespace Eduprat\PdfBundle\Controller;

use Symfony\Component\Routing\Attribute\Route;
use Eduprat\DomainBundle\Entity\Coordinator;
use Eduprat\DomainBundle\Entity\Formation;
use Eduprat\PdfBundle\Services\BudgetCrTotalPDF;
use Symfony\Component\HttpFoundation\Response;
use Symfony\Contracts\Service\Attribute\Required;

class BudgetCrTotalController extends AbstractPdfController
{

    private BudgetCrTotalPDF $budgetCrTotalPDF;

    #[Required]
    public function withBudgetCrTotalPDF(BudgetCrTotalPDF $budgetCrTotalPDF): void
    {
        $this->budgetCrTotalPDF = $budgetCrTotalPDF;
    }

    #[Route(path: '/pdf/admin_budget_cr_total/{formation}/{coordinator}/{n1}/{token}/{tokenFormation}/pdf', name: 'pdf_budget_cr_total_pdf', defaults: ['n1' => '0'], methods: ['GET'])]
    public function budgetCrTotal(
        Formation $formation,
        Coordinator $coordinator,
        string $token,
        string $tokenFormation,
        ?bool $n1 = false,
    ): Response
    {
//        $this->snappy->setOption('margin-top', 36);
//        $this->snappy->setOption('margin-bottom', 20);

        $this->denyAccessIfInvalidToken($coordinator->getPerson(), $token);
        $this->denyAccessIfInvalidToken($formation, $tokenFormation);

        $response =  $this->budgetCrTotalPDF->generate(
            ['formation' => $formation, 'coordinator' => $coordinator, 'n1' => $n1], null, null
        );

        $coordinateur = $coordinator->getPerson()->getLastname().' '.$coordinator->getPerson()->getFirstname();
        $filename = "budget-total-coordinateur-".$coordinateur.".pdf";

        return new Response(
            $response->getBody()->getContents(),
            Response::HTTP_OK,
            [
                'Content-Type' => 'application/pdf',
                'Content-Disposition' => sprintf("inline; filename=\"%s\"", $filename)
            ]
        );
    }
}
