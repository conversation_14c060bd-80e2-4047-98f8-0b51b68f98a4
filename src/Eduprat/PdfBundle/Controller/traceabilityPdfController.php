<?php

namespace Eduprat\PdfBundle\Controller;

use Eduprat\DomainBundle\Entity\Formation;
use Eduprat\DomainBundle\Entity\FinanceSousMode;
use Eduprat\PdfBundle\Services\TraceabilityUnityPDF;
use Eduprat\PdfBundle\Services\TraceabilityPDF;
use Symfony\Component\HttpFoundation\Request;
use Symfony\Component\HttpFoundation\Response;
use Symfony\Component\Routing\Attribute\Route;
use Symfony\Contracts\Service\Attribute\Required;

class traceabilityPdfController extends AbstractPdfController
{
    private TraceabilityUnityPDF $traceabilityUnityPDF;
    private TraceabilityPDF $traceabilityPDF;

    #[Required]
    public function withTraceabilitiesPDF(TraceabilityUnityPDF $traceabilityUnityPDF, TraceabilityPDF $traceabilityPDF): void
    {
        $this->traceabilityUnityPDF = $traceabilityUnityPDF;
        $this->traceabilityPDF = $traceabilityPDF;
    }

    #[Route(path: 'pdf/traceability-document-unity/{id}/{financeSousMode}/{unity}/pdf/{token}', name: 'pdf_traceability_document_unity_pdf', methods: ['GET'])]
    public function traceabilityDocumentUnity(Formation $formation, FinanceSousMode $financeSousMode, $unity, $token = null)
    {
        $this->denyAccessIfInvalidToken($formation, $token);

        $formRef = $formation->getProgramme()->getReference();
        $filename = "synthese-suivi-activites-non-presentielles-".$formRef."-".$unity.".pdf";

        $title = "SYNTHESE DE SUIVI DES ACTIVITES NON PRESENTIELLES";
        $img = $financeSousMode->isActalians() ? "actalians" : "dpc";

        $response =  $this->traceabilityUnityPDF->generate(
            ['formation' => $formation, 'financeSousMode' => $financeSousMode, 'unity' => $unity], ['title' => $title, 'img' => $img]
        );

        return new Response(
            $response->getBody(),
            Response::HTTP_OK,
            array(
                'Content-Type' => 'application/pdf',
                'Content-Disposition' => sprintf("inline; filename=\"%s\"", $filename)
            )
        );
    }

    #[Route(path: 'pdf/traceability-document/{id}/{financeSousMode}/pdf/{token}', name: 'pdf_traceability_document_pdf', methods: ['GET'])]
    public function traceabilityDocument(Request $request, Formation $formation, FinanceSousMode $financeSousMode, $token = null)
    {
        $this->denyAccessIfInvalidToken($formation, $token);

        $isZoom = $request->get("zoom") ?? false;
        $isEmpty = $request->get("empty") ?? false;

        $formRef = $formation->getProgramme()->getReference();
        if($isEmpty) {
            $filename = "doc-de-tracabilite-vierge-".$formRef.".pdf";
        } elseif($isZoom) {
            $filename = "doc-de-tracabilite-temps-connexion-".$formRef.".pdf";
        } else {
            $filename = "doc-de-tracabilite-".$formRef.".pdf";
        }

        $title = "SYNTHESE DE SUIVI DES ACTIVITES NON PRESENTIELLES";
        $img = $financeSousMode->isActalians() ? "actalians" : "dpc";

        $response =  $this->traceabilityPDF->generate(
            ['formation' => $formation, 'financeSousMode' => $financeSousMode, 'zoom' => $isZoom, 'empty' => $isEmpty], ['title' => $title, 'img' => $img]
        );

        return new Response(
            $response->getBody(),
            Response::HTTP_OK,
            array(
                'Content-Type' => 'application/pdf',
                'Content-Disposition' => sprintf("inline; filename=\"%s\"", $filename)
            )
        );         
    }
}