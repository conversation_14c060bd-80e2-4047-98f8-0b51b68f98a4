<?php

namespace Eduprat\PdfBundle\Controller;

use Doctrine\ORM\EntityManagerInterface;
use Eduprat\AdminBundle\Entity\Person;
use Eduprat\AdminBundle\Form\DownloadedPlaquetteFileType;
use Eduprat\AuditBundle\Services\AuditManager;
use Eduprat\DomainBundle\Entity\DownloadedPlaquetteFile;
use Eduprat\DomainBundle\Entity\FinanceSousMode;
use Eduprat\DomainBundle\Entity\Formateur;
use Eduprat\DomainBundle\Entity\Formation;
use Eduprat\DomainBundle\Entity\FormationActalians;
use Eduprat\DomainBundle\Entity\Participation;
use Eduprat\DomainBundle\Entity\Programme;
use Eduprat\DomainBundle\Services\DownloadedPlaquetteFileManager;
use Eduprat\DomainBundle\Services\InvoiceManager;
use Eduprat\PdfBundle\Services\AuditPDF;
use Eduprat\PdfBundle\Services\CertificateParticipationPDF;
use Eduprat\PdfBundle\Services\InvoicePDF;
use Gotenberg\Exceptions\GotenbergApiErrored;
use Gotenberg\Gotenberg;
use Gotenberg\Stream;
use Knp\Snappy\Pdf;
use Psr\Log\LoggerInterface;
use Symfony\Bundle\FrameworkBundle\Controller\AbstractController;
use Symfony\Component\DependencyInjection\ParameterBag\ParameterBagInterface;
use Symfony\Component\HttpFoundation\RedirectResponse;
use Symfony\Component\Routing\Attribute\Route;
use Symfony\Component\HttpKernel\Exception\HttpException;
use Symfony\Component\HttpFoundation\BinaryFileResponse;
use Symfony\Component\HttpFoundation\JsonResponse;
use Symfony\Component\HttpFoundation\Request;
use Symfony\Component\HttpFoundation\Response;
use Symfony\Component\HttpFoundation\ResponseHeaderBag;
use Symfony\Component\HttpKernel\Exception\AccessDeniedHttpException;
use Symfony\Component\HttpKernel\Exception\NotFoundHttpException;
use Symfony\Component\Process\Process;
use Eduprat\DomainBundle\Entity\Coordinator;
use Eduprat\DomainBundle\Entity\Participant;
use Symfony\Component\Routing\Generator\UrlGeneratorInterface;
use Symfony\Component\Security\Core\Authorization\AuthorizationCheckerInterface;

#[Route(path: '/pdf')]
class DefaultController extends AbstractController
{

    /**
     * @var AuthorizationCheckerInterface
     */
    private $authorizationChecker;

    /**
     * @var Pdf
     */
    private $snappy;

    private LoggerInterface $pdfLogger;
    private ParameterBagInterface $parameterBag;
    private CertificateParticipationPDF $certificateParticipationPDF;
    private InvoicePDF $invoicePDF;

    public function __construct(AuthorizationCheckerInterface $authorizationChecker, Pdf $snappy, LoggerInterface $pdfLogger,
                                ParameterBagInterface         $parameterBag,
                                CertificateParticipationPDF   $certificateParticipationPDF,
                                InvoicePDF   $invoicePDF,
    )
    {
        $this->authorizationChecker = $authorizationChecker;
        $this->snappy = $snappy;
        $this->pdfLogger = $pdfLogger;
        $this->parameterBag = $parameterBag;
        $this->certificateParticipationPDF = $certificateParticipationPDF;
        $this->invoicePDF = $invoicePDF;
    }

    public function denyAccessIfInvalidToken($object, $token, $message = 'Access Denied.')
    {
        if (!($this->authorizationChecker->isGranted('IS_AUTHENTICATED_FULLY') && $this->isGranted('ROLE_SUPER_ADMIN')) && method_exists($object, 'getToken') && $object->getToken() !== $token) {
            throw new AccessDeniedHttpException();
        }
    }

    #[Route(path: '/certificate-attendance/{id}/pdf/{token}', name: 'pdf_certificate_attendance_pdf', methods: ['GET'])]
    public function certificateAttendance(Formation $formation, $token = null): Response
    {
        $this->denyAccessIfInvalidToken($formation, $token);

        $pageUrl = $this->generateUrl('pdf_certificate_attendance_html', array('id' => $formation->getId()), UrlGeneratorInterface::ABSOLUTE_URL);
        $this->snappy->setOption('disable-javascript', true);
        $this->snappy->setOption('orientation', "Portrait");
        $this->snappy->setOption('header-html', $this->generateUrl('pdf_header_dpc_attendance', array(), UrlGeneratorInterface::ABSOLUTE_URL));
        $this->snappy->setOption('footer-html', $this->generateUrl('pdf_footer_dpc', array(), UrlGeneratorInterface::ABSOLUTE_URL));
        $headerH = 58;
        $headerS = 10;
        $this->snappy->setOption('margin-top', $headerH + $headerS);
        $this->snappy->setOption('header-spacing', $headerS);
        $this->snappy->setOption('margin-bottom', 24.2);
        $this->snappy->setOption('margin-left', 0);
        $this->snappy->setOption('margin-right', 0);

        $filename = "attestations-participation-".$formation->getProgramme()->getReference().".pdf";

        return $this->getPdfResponse($filename, $pageUrl);
    }

    /**
     * @param Formation $formation
     * @param Coordinator $coordinator
     * @param null $token
     * @param Request $request
     * @return Response
     */
    #[Route(path: '/coordinator_honorary/{formation}/{coordinator}/{n1}/pdf/{token}', name: 'pdf_coordinator_honorary_pdf', defaults: ['n1' => '0'], methods: ['GET'])]
    public function coordinatorHonorary(Request $request, Formation $formation, Coordinator $coordinator, $n1 = null, $token = null)
    {
        $this->denyAccessIfInvalidToken($formation, $token);

        $pageOffset = $request->query->get('offset');

        $pageUrl = $this->generateUrl('pdf_coordinator_honorary_html', array('formation' => $formation->getId(), 'coordinator' => $coordinator->getId(), 'n1' => $n1), UrlGeneratorInterface::ABSOLUTE_URL);
        $this->snappy->setOption('disable-javascript', true);
        $this->snappy->setOption('orientation', "Portrait");
        $this->snappy->setOption('header-html', $this->generateUrl('pdf_header_evaluation_global', array(), UrlGeneratorInterface::ABSOLUTE_URL));
        $this->snappy->setOption('footer-html', $this->generateUrl('pdf_footer_evaluation_global', array("offset" => $pageOffset), UrlGeneratorInterface::ABSOLUTE_URL));
        $this->snappy->setOption('margin-top', 36);
        $this->snappy->setOption('margin-left', 0);
        $this->snappy->setOption('margin-right', 0);
        $this->snappy->setOption('margin-bottom', 20);

        $coordinateur = $coordinator->getPerson()->getLastname().' '.$coordinator->getPerson()->getFirstname();

        $filename = "honoraire-coordinateur-".$coordinateur.".pdf";

        return $this->getPdfResponse($filename, $pageUrl);
    }

    /**
      * @param Programme $programme
      * @param Person $person
      * @param null                                   $token
      * @return Response
      */
     #[Route(path: '/evaluation_coordinator_by_coordinator/{coordinator}/programme/{programme}/pdf/{token}', name: 'pdf_evaluation_coordinator_by_coordinator_pdf', methods: ['GET'])]
     public function evaluationCoordinatorByCoordinator(Coordinator $coordinator, Programme $programme, $token = null)
     {
         $this->denyAccessIfInvalidToken($programme, $token);

         $pageUrl = $this->generateUrl('pdf_evaluation_coordinator_by_coordinator_html', array('coordinator' => $coordinator->getId(), 'programme' => $programme->getId()), UrlGeneratorInterface::ABSOLUTE_URL);
         $this->snappy->setOption('disable-javascript', true);
         $this->snappy->setOption('orientation', "Portrait");
         $this->snappy->setOption('header-html', $this->generateUrl('pdf_header_coordinator_honorary', array(), UrlGeneratorInterface::ABSOLUTE_URL));
         $this->snappy->setOption('footer-html', $this->generateUrl('pdf_footer_evaluation', array(), UrlGeneratorInterface::ABSOLUTE_URL));
         $headerH = 40;
         $headerS = 15;
         $this->snappy->setOption('margin-top', $headerH + $headerS);
         $this->snappy->setOption('margin-left', 5);
         $this->snappy->setOption('margin-right', 5);
         $this->snappy->setOption('header-spacing', $headerS);
         $this->snappy->setOption('footer-spacing', 16);
         $this->snappy->setOption('margin-bottom', 46);

         $coordinateur = $coordinator->getPerson()->getInvertedFullname();

         $filename = "evaluation-coordinateur-".$coordinateur.".pdf";

         return $this->getPdfResponse($filename, $pageUrl);
     }

    /**
     * @param Participation $participation
     * @param null $token
     * @param Request $request
     * @return Response
     */
    #[Route(path: '/certificate-participation/{id}/pdf/{token}', name: 'pdf_certificate_participation_pdf', methods: ['GET'])]
    public function certificateParticipation(Request $request, Participation $participation, $token = null): Response
    {
        $this->denyAccessIfInvalidToken($participation, $token);

        $pageOffset = $request->query->get('offset');

        $filename = $participation->getFormation()->getProgramme()->getReference() . "-attestation-participation-" . $participation->getParticipant()->getLastname() . ".pdf";

        $response =  $this->certificateParticipationPDF->generate(
            ['participation' => $participation], null, ['pageOffset' => $pageOffset]
        );

        return new Response(
            $response->getBody(),
            Response::HTTP_OK,
            array(
                'Content-Type' => 'application/pdf',
                'Content-Disposition' => sprintf("inline; filename=\"%s\"", $filename)
            )
        );
    }

    /**
     * @param Participation $participation
     * @param null $token
     * @param Request $request
     * @return Response
     */
    #[Route(path: '/certificate-participation-horary/{id}/pdf/{token}', name: 'pdf_certificate_participation_horary_pdf', methods: ['GET'])]
    public function certificateParticipationHorary(Request $request, Participation $participation, $token = null)
    {
        $this->denyAccessIfInvalidToken($participation, $token);

        $pageOffset = $request->query->get('offset');

        $pageUrl = $this->generateUrl('pdf_certificate_participation_horary_html', array('id' => $participation->getId()), UrlGeneratorInterface::ABSOLUTE_URL);
        $this->snappy->setOption('disable-javascript', true);
        $this->snappy->setOption('orientation', "Portrait");
        $this->snappy->setOption('header-html', $this->generateUrl('pdf_header_evaluation_global', array(), UrlGeneratorInterface::ABSOLUTE_URL));
        $this->snappy->setOption('footer-html', $this->generateUrl('pdf_footer_evaluation_global', array("offset" => $pageOffset), UrlGeneratorInterface::ABSOLUTE_URL));
        $this->snappy->setOption('margin-top', 36);
        $this->snappy->setOption('margin-left', 0);
        $this->snappy->setOption('margin-right', 0);
        $this->snappy->setOption('margin-bottom', 20);

        $filename = $participation->getFormation()->getProgramme()->getReference()."-attestation-participation-".$participation->getParticipant()->getLastname().".pdf";

        return $this->getPdfResponse($filename, $pageUrl);
    }

    /**
     * @param Formation $formation
     * @param FinanceSousMode|null $financeSousMode
     * @param null $token
     * @param                                           $type
     * @param Request $request
     */
    #[Route(path: '/formation-generate-file/{type}/{id}/{financeSousMode}/generate/{token}', name: 'pdf_formation_generate_file', methods: ['GET'], defaults: ['financeSousMode' => null])]
    public function formationGenerateFile(Formation $formation, $type, FinanceSousMode $financeSousMode = null, $token = null): JsonResponse
    {
        $this->denyAccessIfInvalidToken($formation, $token);
        if ($type === "fusion") {
            $command = sprintf("eduprat:pdf_merged %s %s", $formation->getId(), $financeSousMode->getId());
        } else {
            $command = sprintf("eduprat:pdf %s %s %s", $formation->getId(), $type, $financeSousMode->getId());
        }

        $projectDir = $this->getParameter('kernel.project_dir');
        $cmd = sprintf('php %s/bin/console %s', $projectDir, $command);
        $cmd = sprintf("timeout -k 5s 43200s %s --env=%s >/dev/null 2>&1 &", $cmd, $this->getParameter('kernel.environment'));

        $process = Process::fromShellCommandline($cmd);
        $process->run();
        if (!$process->isSuccessful()) {
            throw new \RuntimeException($process->getErrorOutput());
        }

        $pid = $process->getOutput();

        return new JsonResponse(["status" => "ok", "pid" => $pid]);
    }

    /**
     * @param Formation $formation
     * @param null                                      $token
     * @param                                           $type
     * @param Request $request
     * @return Response
     */
    #[Route(path: '/formation-generate-file-get/{type}/{id}/generate/{token}/{financeSousMode}', name: 'pdf_formation_generate_file_get', methods: ['GET'])]
    public function formationGenerateFileGet(Formation $formation, $type, FinanceSousMode $financeSousMode = null, $token = null): BinaryFileResponse
    {
        $this->denyAccessIfInvalidToken($formation, $token);

        $filename = "";

        if($type == 'fusion') {
            $filename = "tracabilite-%s-%s.pdf";
        }
        else if ($type == 'restitution_audit') {
            $scoringOrPr = $formation->isVignette() ? "scoring" : 'restitution';
            $filename = $scoringOrPr."-%s-%s.pdf";
        }
        else if ($type == 'audit1') {
            $filename = "audit1-reponses-%s-%s.pdf";
        }
        else if ($type == 'audit2') {
            $filename = "audit2-reponses-%s-%s.pdf";
        }
        else if ($type == 'participation') {
            $filename = "attestations-participation-%s-%s.pdf";
        }
        else if ($type == 'attestation_honneur') {
            $filename = "attestations-honneurs-%s.pdf";
        }
        else if ($type == 'attestation_honneur_n1') {
            $filename = "attestations-honneurs-n1-%s.pdf";
        }
        else if ($type === 'prerestitution_audit') {
            $scoringOrPr = $formation->isVignette() ? "scoring" : 'pre-restitution';
            $filename = $scoringOrPr."-%s-%s.pdf";
        }

        $filename = sprintf($filename, urlencode($formation->getProgramme()->getReference()), $financeSousMode ? $financeSousMode->getId() : "");

        if ($formation->hasGeneratedFile($type, $financeSousMode) && $formation->generateFileIsFinished($type, $financeSousMode)) {
            $response = new BinaryFileResponse($formation->getGeneratedFilePath($type, $financeSousMode));

            if($filename) {
                $response->setContentDisposition(
                    ResponseHeaderBag::DISPOSITION_ATTACHMENT,
                    $filename);
            }
            return $response;
        }

        throw new NotFoundHttpException();
    }

    /**
     * @param Participation $participation
     * @param null                                       $token
     * @return Response
     */
    #[Route(path: '/restitution-audit/{id}/pdf/{token}', name: 'pdf_restitution_audit_pdf', methods: ['GET'])]
    public function restitutionFormation(Request $request, Participation $participation, $token = null)
    {
        $this->denyAccessIfInvalidToken($participation, $token);

        $pageOffset = $request->query->get('offset');

        $pageUrl = $this->generateUrl('pdf_audit_restitution', array('id' => $participation->getId()), UrlGeneratorInterface::ABSOLUTE_URL);
        $this->snappy->setOption('orientation', "Portrait");
        $this->snappy->setOption('javascript-delay', 1000);
        $this->snappy->setOption('header-html', $this->generateUrl('pdf_header_evaluation_global', array(), UrlGeneratorInterface::ABSOLUTE_URL));
        $this->snappy->setOption('footer-html', $this->generateUrl('pdf_footer_evaluation_global', array("offset" => $pageOffset), UrlGeneratorInterface::ABSOLUTE_URL));
        $this->snappy->setOption('margin-top', 36);
        $this->snappy->setOption('margin-left', 0);
        $this->snappy->setOption('margin-right', 0);
//        $this->>snappy->setOption('margin-bottom', 20);

        $filename = $participation->getFormation()->getProgramme()->getReference()."-restitution-".$participation->getParticipant()->getLastname().".pdf";

        return $this->getPdfResponse($filename, $pageUrl);
    }

    /**
     * @param Participation $participation
     * @param null                                       $token
     * @return Response
     */
    #[Route(path: '/restitution-audit-groupe-individuelle/{id}/pdf/{token}', name: 'pdf_audit_restitution_groupe_individuelle_pdf', methods: ['GET'])]
    public function restitutionAuditGroupeIndividuelle(Request $request, Participation $participation, $token = null)
    {
        $this->denyAccessIfInvalidToken($participation, $token);

        $pageOffset = $request->query->get('offset');

        $pageUrl = $this->generateUrl('pdf_audit_restitution_groupe_individuelle', array('participation' => $participation->getId()), UrlGeneratorInterface::ABSOLUTE_URL);
        $this->snappy->setOption('orientation', "Portrait");
        $this->snappy->setOption('javascript-delay', 1000);
        $this->snappy->setOption('header-html', $this->generateUrl('pdf_header_evaluation_global', array(), UrlGeneratorInterface::ABSOLUTE_URL));
        $this->snappy->setOption('footer-html', $this->generateUrl('pdf_footer_evaluation_global', array("offset" => $pageOffset), UrlGeneratorInterface::ABSOLUTE_URL));
        $this->snappy->setOption('margin-top', 36);
        $this->snappy->setOption('margin-left', 0);
        $this->snappy->setOption('margin-right', 0);
//        $this->>snappy->setOption('margin-bottom', 20);

        $filename = $participation->getFormation()->getProgramme()->getReference()."-prerestitution-".$participation->getParticipant()->getLastname().".pdf";

        return $this->getPdfResponse($filename, $pageUrl);
    }

    /**
     * @param Formation $formation
     * @param null $token
     * @param Request $request
     * @return Response
     */
    #[Route(path: '/attestation-honneur/{id}/{participant}/{person}/{n1}/pdf/{token}', name: 'pdf_attestation_honneur_pdf', defaults: ['n1' => '0'], methods: ['GET'])]
    public function attestationHonneur(Request $request, Formation $formation, $token = null, Participant $participant = null, Person $person = null, $n1 = null)
    {
        $route = "pdf_attestation_honneur";
        $pageOffset = $request->query->get('offset');

        $this->denyAccessIfInvalidToken($formation, $token);

        $participantName = $participant ? $participant->getInvertedFullname() : "";
        $participant = $participant ? $participant->getId() : "0";
        $person = $person ? $person->getId() : "0";

        $pageUrl = $this->generateUrl($route, array('id' => $formation->getId(), 'participant' => $participant, 'person' => $person, 'n1' => $n1), UrlGeneratorInterface::ABSOLUTE_URL);
        $this->snappy->setOption('orientation', "Portrait");
        $this->snappy->setOption('javascript-delay', 1000);
        $this->snappy->setOption('margin-top', $formation->getProgramme()->getYear() >= date("Y") ? 10 : 36);
        $this->snappy->setOption('margin-left', 0);
        $this->snappy->setOption('margin-right', 0);

        $filename = sprintf("attestation-participation-%s-%s-%s.pdf", $formation->getProgramme()->getReference(), $formation->getSessionNumber(), $participantName);

        return $this->getPdfResponse($filename, $pageUrl);
    }

    /**
     * @param Formation $formation
     * @param null $token
     * @param Request $request
     * @return Response
     */
    #[Route(path: '/auto-evaluations/{id}/pdf/{token}', name: 'pdf_auto_evaluations_pdf', methods: ['GET'])]
    public function autoEvals(Request $request, Formation $formation, $token = null)
    {
        $route = "pdf_auto_evaluations";
        $pageOffset = $request->query->get('offset');

        $this->denyAccessIfInvalidToken($formation, $token);



        $pageUrl = $this->generateUrl($route, array('id' => $formation->getId()), UrlGeneratorInterface::ABSOLUTE_URL);
        $this->snappy->setOption('orientation', "Portrait");
        $this->snappy->setOption('javascript-delay', 1000);
        $this->snappy->setOption('margin-top', 36);
        $this->snappy->setOption('margin-left', 0);
        $this->snappy->setOption('margin-right', 0);
        $this->snappy->setOption('footer-html', $this->generateUrl('pdf_footer_evaluation_global', array("offset" => $pageOffset), UrlGeneratorInterface::ABSOLUTE_URL));


        $filename = sprintf("etutorat-1-%s-%s.pdf", $formation->getProgramme()->getReference(), $formation->getSessionNumber());

        return $this->getPdfResponse($filename, $pageUrl);
    }

    /**
     * @param Formation $formation
     * @param null $token
     * @param Request $request
     * @return Response
     */
    #[Route(path: '/restitution-audit-groupe/{id}/pdf/{token}', name: 'pdf_restitution_audit_groupe_pdf', methods: ['GET'])]
    public function restitutionAuditGroupe(Request $request, Formation $formation, $token = null)
    {

        $route = "pdf_audit_restitution_groupe";

        $pageOffset = $request->query->get('offset');

        $this->denyAccessIfInvalidToken($formation, $token);
        $pageUrl = $this->generateUrl($route, array('id' => $formation->getId()), UrlGeneratorInterface::ABSOLUTE_URL);
        $this->snappy->setOption('orientation', "Portrait");
        $this->snappy->setOption('javascript-delay', 1000);
        $this->snappy->setOption('header-html', $this->generateUrl('pdf_header_evaluation_global', array(), UrlGeneratorInterface::ABSOLUTE_URL));
        $this->snappy->setOption('footer-html', $this->generateUrl('pdf_footer_evaluation_global', array("offset" => $pageOffset), UrlGeneratorInterface::ABSOLUTE_URL));
        $this->snappy->setOption('margin-top', 36);
        $this->snappy->setOption('margin-left', 0);
        $this->snappy->setOption('margin-right', 0);
//        $this->>snappy->setOption('margin-bottom', 20);

        $scoringOrPr = $formation->isVignette() ? "scoring" : 'pre-restitution';
        $filename = $scoringOrPr."-audit-1-".$formation->getProgramme()->getReference().".pdf";

        return $this->getPdfResponse($filename, $pageUrl);
    }

    /**
     * @param Formation $formation
     * @param null $token
     * @param Request $request
     * @return Response
     */
    #[Route(path: '/restitution-audit-groupe-2/{id}/pdf/{token}', name: 'pdf_restitution_audit_groupe_2_pdf', methods: ['GET'])]
    public function restitutionAuditGroupe2(Request $request, Formation $formation, $token = null)
    {
        // if(!$formation->getFormateurs()->contains($former)) {
        //     throw new NotFoundHttpException();
        // }

        $route = "pdf_audit_restitution_groupe_2";

        $pageOffset = $request->query->get('offset');

        $this->denyAccessIfInvalidToken($formation, $token);
        $pageUrl = $this->generateUrl($route, array('id' => $formation->getId()), UrlGeneratorInterface::ABSOLUTE_URL);
        $this->snappy->setOption('orientation', "Portrait");
        $this->snappy->setOption('javascript-delay', 1000);
        $this->snappy->setOption('header-html', $this->generateUrl('pdf_header_evaluation_global', array(), UrlGeneratorInterface::ABSOLUTE_URL));
        $this->snappy->setOption('footer-html', $this->generateUrl('pdf_footer_evaluation_global', array("offset" => $pageOffset), UrlGeneratorInterface::ABSOLUTE_URL));
        $this->snappy->setOption('margin-top', 36);
        $this->snappy->setOption('margin-left', 0);
        $this->snappy->setOption('margin-right', 0);
//        $this->>snappy->setOption('margin-bottom', 20);

        $scoringOrPr = $formation->isVignette() ? "scoring" : 'restitution';
        $filename = $scoringOrPr."-audit-2-".$formation->getProgramme()->getReference().".pdf";

        return $this->getPdfResponse($filename, $pageUrl);
    }

    /**
     * @param FormationActalians $formation
     * @param null $token
     * @return Response
     */
    #[Route(path: '/restitution-actalians/{id}/pdf/{token}', name: 'pdf_restitution_actalians_pdf', methods: ['GET'])]
    public function restitutionActalians(Request $request, Participation $participation, $token = null)
    {
        $this->denyAccessIfInvalidToken($participation, $token);

        $pageOffset = $request->query->get('offset');

        $pageUrl = $this->generateUrl('pdf_actalians_restitution', array('id' => $participation->getId()), UrlGeneratorInterface::ABSOLUTE_URL);
        $this->snappy->setOption('orientation', "Portrait");
        $this->snappy->setOption('javascript-delay', 1000);
        $this->snappy->setOption('header-html', $this->generateUrl('pdf_header_evaluation_global', array(), UrlGeneratorInterface::ABSOLUTE_URL));
        $this->snappy->setOption('footer-html', $this->generateUrl('pdf_footer_evaluation_global', array("offset" => $pageOffset), UrlGeneratorInterface::ABSOLUTE_URL));
        $this->snappy->setOption('margin-left', 0);
        $this->snappy->setOption('margin-right', 0);

        $filename = "restitution-questionnaire-".$participation->getFormation()->getProgramme()->getReference().".pdf";

        return $this->getPdfResponse($filename, $pageUrl);
    }

    /**
     * @param Person $person
     * @return Response
     */
    #[Route(path: '/coordinator-table/{id}/pdf', name: 'pdf_coordinator_table_pdf', methods: ['GET'])]
    public function coordinatorTable(Person $person)
    {
        $pageUrl = $this->generateUrl('pdf_coordinator_table', array('id' => $person->getId(), 'year' => 2016), UrlGeneratorInterface::ABSOLUTE_URL);
        $this->snappy->setOption('orientation', "Landscape");

        return $this->getPdfResponse("file.pdf", $pageUrl);
    }

    /**
     * @param Formateur $former
     * @param Formation $formation
     * @param null                                   $token
     * @return Response
     * @internal param \Eduprat\AdminBundle\Entity\Person $person
     */
    #[Route(path: '/contract-former/{id}/{formation}/pdf/{token}', name: 'pdf_contract_former_pdf', methods: ['GET'])]
    public function contractFormer(Request $request, Formateur $former, Formation $formation, $token = null)
    {
        $this->denyAccessIfInvalidToken($formation, $token);

        $pageOffset = $request->query->get('offset');

        $pageUrl = $this->generateUrl('pdf_contract_former_html', array('id' => $former->getId(), 'formation' => $formation->getId()), UrlGeneratorInterface::ABSOLUTE_URL);
        $this->snappy->setOption('orientation', "Portrait");
        $this->snappy->setOption('header-html', $this->generateUrl('pdf_header_evaluation_global', array(), UrlGeneratorInterface::ABSOLUTE_URL));
        $this->snappy->setOption('footer-html', $this->generateUrl('pdf_footer_evaluation_global', array("offset" => $pageOffset), UrlGeneratorInterface::ABSOLUTE_URL));
        $this->snappy->setOption('margin-top', 36);
        $this->snappy->setOption('margin-left', 0);
        $this->snappy->setOption('margin-right', 0);
        $this->snappy->setOption('margin-bottom', 20);

        $filename = $formation->getProgramme()->getReference()."-contrat-".$former->getPerson()->getLastname().".pdf";

        return $this->getPdfResponse($filename, $pageUrl);
    }

    /**
     * @param Formation $formation
     * @param FinanceSousMode $financeSousMode
     * @param null $token
     * @param Request $request
     * @return Response
     * @internal param \Eduprat\AdminBundle\Entity\Person $person
     */
    #[Route(path: '/convention-pharmacie/{id}/{financeSousMode}/pdf/{token}', name: 'pdf_convention_pharmacie_pdf', methods: ['GET'])]
    public function conventionPharmacie(Request $request, Formation $formation, FinanceSousMode $financeSousMode, $token = null)
    {
        $this->denyAccessIfInvalidToken($formation, $token);

        $pageOffset = $request->query->get('offset');

        $pageUrl = $this->generateUrl('pdf_convention_pharmacie_html', array('id' => $formation->getId(), 'financeSousMode' => $financeSousMode->getId()), UrlGeneratorInterface::ABSOLUTE_URL);
        $this->snappy->setOption('orientation', "Portrait");
        $this->snappy->setOption('header-html', $this->generateUrl('pdf_header_evaluation_global', array(), UrlGeneratorInterface::ABSOLUTE_URL));
        $this->snappy->setOption('footer-html', $this->generateUrl('pdf_footer_evaluation_global', array("offset" => $pageOffset), UrlGeneratorInterface::ABSOLUTE_URL));
        $this->snappy->setOption('margin-top', 36);
        $this->snappy->setOption('margin-left', 0);
        $this->snappy->setOption('margin-right', 0);
        $this->snappy->setOption('margin-bottom', 20);

        $filename = $formation->getProgramme()->getReference()."-convention-pharmacie.pdf";

        return $this->getPdfResponse($filename, $pageUrl);
    }

    /**
     * @param Formation $formation
     * @param FinanceSousMode $financeSousMode
     * @param null $token
     * @param Request $request
     * @return Response
     * @internal param \Eduprat\AdminBundle\Entity\Person $person
     */
    #[Route(path: '/convention/{id}/{financeSousMode}/pdf/{token}', name: 'pdf_convention_pdf', methods: ['GET'])]
    #[Route(path: '/convention/{id}/pdf/{token}', name: 'pdf_convention_empty_pdf', methods: ['GET'])]
    public function convention(Formation $formation, Request $request, FinanceSousMode $financeSousMode = null, $token = null)
    {
        $this->denyAccessIfInvalidToken($formation, $token);

        $pageOffset = $request->query->get('offset');

        $parameters = array('id' => $formation->getId());
        if ($financeSousMode) {
            $parameters['financeSousMode'] = $financeSousMode->getId();
        }
        $pageUrl = $this->generateUrl($financeSousMode ? 'pdf_convention_html' : 'pdf_convention_empty_html', $parameters, UrlGeneratorInterface::ABSOLUTE_URL);
        $this->snappy->setOption('orientation', "Portrait");
        $this->snappy->setOption('header-html', $this->generateUrl('pdf_header_evaluation_global', array(), UrlGeneratorInterface::ABSOLUTE_URL));
        $this->snappy->setOption('footer-html', $this->generateUrl('pdf_footer_evaluation_global', array("offset" => $pageOffset), UrlGeneratorInterface::ABSOLUTE_URL));
        $this->snappy->setOption('margin-top', 36);
        $this->snappy->setOption('margin-left', 0);
        $this->snappy->setOption('margin-right', 0);
        $this->snappy->setOption('margin-bottom', 20);

        $financeSousModeName = $financeSousMode ? "-" . $financeSousMode->getName() : "";
        $filename = "Convention-" . $formation->getProgramme()->getReference()."-". $financeSousModeName .".pdf";

        return $this->getPdfResponse($filename, $pageUrl);
    }

    /**
     * @param Formation $formation
     * @param int $auditId
     * @param null $token
     * @param Request $request
     * @return Response
     */
    #[Route(path: '/audit/empty/{id}/{auditId}/pdf/{token}', name: 'pdf_audit_empty_pdf', requirements: ['id' => '^\d+$', 'auditId' => '^\d+$'], methods: ['GET'])]
    public function auditEmpty(Request $request, Formation $formation, int $auditId = 1, $token = null)
    {
        $this->denyAccessIfInvalidToken($formation, $token);

        $pageUrls = [];

        $nbPatients = (int) $auditId === AuditManager::SECOND_AUDIT && $formation->isVignette() ? $formation->getAudit2()->getNbPatients() : $formation->getAudit()->getNbPatients();

        for($i=1; $i<=$nbPatients; $i++) {
            $pageUrls[] = $this->generateUrl('pdf_audit_empty_html', array('id' => $formation->getId(), 'auditId' => $auditId, 'patient' => $i), UrlGeneratorInterface::ABSOLUTE_URL);
        }

        $pageOffset = $request->query->get('offset');

        $this->snappy->setOption('orientation', "Portrait");
        $this->snappy->setOption('header-html', $this->generateUrl('pdf_header_evaluation_global', array("id" => $formation->getId()), UrlGeneratorInterface::ABSOLUTE_URL));
        $this->snappy->setOption('footer-html', $this->generateUrl('pdf_footer_evaluation_global', array('offset' => $pageOffset), UrlGeneratorInterface::ABSOLUTE_URL));
        $this->snappy->setOption('margin-top', 36);
        $this->snappy->setOption('margin-left', 0);
        $this->snappy->setOption('margin-right', 0);
        $this->snappy->setOption('javascript-delay', 2000);

        $filename = "audit-vierge-".$formation->getProgramme()->getReference().".pdf";

        return $this->getPdfResponse($filename, $pageUrls);
    }

    /**
     * @param Formation $formation
     * @param null                                      $token
     * @param Request $request
     * @return Response
     */
    #[Route(path: '/etutorat/empty/{id}/pdf/{token}', name: 'pdf_etutorat_empty_pdf', methods: ['GET'])]
    public function etutoratEmpty(Request $request, Formation $formation, $token = null)
    {
        $this->denyAccessIfInvalidToken($formation, $token);

        $pageUrls = $this->generateUrl('pdf_etutorat_empty_html', array('id' => $formation->getId()), UrlGeneratorInterface::ABSOLUTE_URL);

        $pageOffset = $request->query->get('offset');

        $this->snappy->setOption('orientation', "Portrait");
        $this->snappy->setOption('header-html', $this->generateUrl('pdf_header_evaluation_global', array("id" => $formation->getId()), UrlGeneratorInterface::ABSOLUTE_URL));
        $this->snappy->setOption('footer-html', $this->generateUrl('pdf_footer_evaluation_global', array('offset' => $pageOffset), UrlGeneratorInterface::ABSOLUTE_URL));
        $this->snappy->setOption('margin-top', 36);
        $this->snappy->setOption('margin-left', 0);
        $this->snappy->setOption('margin-right', 0);
        $this->snappy->setOption('margin-bottom', 20);
        $this->snappy->setOption('javascript-delay', 300);

        $filename = "etutorat-vierge-".$formation->getProgramme()->getReference().".pdf";

        return $this->getPdfResponse($filename, $pageUrls);
    }



    /**
     * @param Formation $formation
     * @param null                                      $token
     * @param Request $request
     * @return Response
     */
    #[Route(path: '/fiche-progression/empty/{id}/pdf/{token}', name: 'pdf_progression_empty_pdf', methods: ['GET'])]
    public function progressionEmpty(Request $request, Formation $formation, $token = null)
    {
        $this->denyAccessIfInvalidToken($formation, $token);

        $pageUrls = $this->generateUrl('pdf_progression_empty_html', array('id' => $formation->getId()), UrlGeneratorInterface::ABSOLUTE_URL);

        $pageOffset = $request->query->get('offset');

        $this->snappy->setOption('orientation', "Portrait");
        $this->snappy->setOption('header-html', $this->generateUrl('pdf_header_evaluation_global', array("id" => $formation->getId()), UrlGeneratorInterface::ABSOLUTE_URL));
        $this->snappy->setOption('footer-html', $this->generateUrl('pdf_footer_evaluation_global', array('offset' => $pageOffset), UrlGeneratorInterface::ABSOLUTE_URL));
        $this->snappy->setOption('margin-top', 36);
        $this->snappy->setOption('margin-left', 0);
        $this->snappy->setOption('margin-right', 0);
        $this->snappy->setOption('margin-bottom', 20);
        $this->snappy->setOption('javascript-delay', 300);

        $filename = "progression-vierge-".$formation->getProgramme()->getReference().".pdf";

        return $this->getPdfResponse($filename, $pageUrls);
    }

    /**
     * @param Formation $formation
     * @param Participation $participation
     * @param null                                      $token
     * @param Request $request
     * @return Response
     */
    #[Route(path: '/fiche-synthese/empty/{id}/{participation}/pdf/{token}', name: 'pdf_synthese_empty_pdf', methods: ['GET'])]
    public function syntheseEmpty(Request $request, Formation $formation, $participation, $token = null)
    {
        $this->denyAccessIfInvalidToken($formation, $token);

        $pageUrls = $this->generateUrl('pdf_synthese_empty_html', array('id' => $formation->getId(), 'participation' => $participation), UrlGeneratorInterface::ABSOLUTE_URL);

        $pageOffset = $request->query->get('offset');

        $this->snappy->setOption('orientation', "Portrait");
        $this->snappy->setOption('header-html', $this->generateUrl('pdf_header_evaluation_global', array("id" => $formation->getId()), UrlGeneratorInterface::ABSOLUTE_URL));
        $this->snappy->setOption('footer-html', $this->generateUrl('pdf_footer_evaluation_global', array('offset' => $pageOffset), UrlGeneratorInterface::ABSOLUTE_URL));
        $this->snappy->setOption('margin-top', 36);
        $this->snappy->setOption('margin-left', 0);
        $this->snappy->setOption('margin-right', 0);
        $this->snappy->setOption('margin-bottom', 20);
        $this->snappy->setOption('javascript-delay', 300);

        $filename = "synthese-vierge-".$formation->getProgramme()->getReference().".pdf";

        return $this->getPdfResponse($filename, $pageUrls);
    }

    /**
     * @param Formation $formation
     * @param Participation $participation
     * @param null                                      $token
     * @param Request $request
     * @return Response
     */
    #[Route(path: '/fiche-synthese/full/{id}/{participation}/pdf/{token}', name: 'pdf_synthese_full_pdf', methods: ['GET'])]
    public function syntheseFull(Request $request, Formation $formation, $participation, $token = null)
    {
        $this->denyAccessIfInvalidToken($formation, $token);

        $pageUrls = $this->generateUrl('pdf_synthese_full_html', array('id' => $formation->getId(), 'participation' => $participation), UrlGeneratorInterface::ABSOLUTE_URL);

        $pageOffset = $request->query->get('offset');

        $this->snappy->setOption('orientation', "Portrait");
        $this->snappy->setOption('header-html', $this->generateUrl('pdf_header_evaluation_global', array("id" => $formation->getId()), UrlGeneratorInterface::ABSOLUTE_URL));
        $this->snappy->setOption('footer-html', $this->generateUrl('pdf_footer_evaluation_global', array('offset' => $pageOffset), UrlGeneratorInterface::ABSOLUTE_URL));
        $this->snappy->setOption('margin-top', 36);
        $this->snappy->setOption('margin-left', 0);
        $this->snappy->setOption('margin-right', 0);
        $this->snappy->setOption('margin-bottom', 20);
        $this->snappy->setOption('javascript-delay', 300);

        $filename = "synthese-vierge-".$formation->getProgramme()->getReference().".pdf";

        return $this->getPdfResponse($filename, $pageUrls);
    }

    /**
     * @param Participation $participation
     * @param int                                       $surveyId
     * @param null                                      $token
     * @param Request $request
     * @return Response
     */
    #[Route(path: '/survey/{id}/{surveyId}/pdf/{token}', name: 'pdf_survey_pdf', methods: ['GET'])]
    public function survey(Request $request, Participation $participation, $surveyId = 1, $token = null)
    {
        $this->denyAccessIfInvalidToken($participation, $token);

        $pageUrl = $this->generateUrl('pdf_survey_html', array('id' => $participation->getId(), 'surveyId' => $surveyId), UrlGeneratorInterface::ABSOLUTE_URL);

        $pageOffset = $request->query->get('offset');

        $this->snappy->setOption('orientation', "Portrait");
        $this->snappy->setOption('header-html', $this->generateUrl('pdf_header_evaluation_global', array("id" => $participation->getFormation()->getId()), UrlGeneratorInterface::ABSOLUTE_URL));
        $this->snappy->setOption('footer-html', $this->generateUrl('pdf_footer_evaluation_global', array('offset' => $pageOffset), UrlGeneratorInterface::ABSOLUTE_URL));
        $this->snappy->setOption('margin-left', 0);
        $this->snappy->setOption('margin-right', 0);

        $filename = "questionnaire-".$surveyId."-".$participation->getParticipant()->getFullname()."-".$participation->getFormation()->getProgramme()->getReference().".pdf";

        return $this->getPdfResponse($filename, $pageUrl);
    }

    /**
     * @param Formation $formation
     * @param FinanceSousMode $financeSousMode
     * @param int $auditId
     * @param null $token
     * @return Response
     */
    #[Route(path: '/audit-answers/{id}/{financeSousMode}/{auditId}/pdf/{token}', name: 'pdf_audit_answers_pdf', methods: ['GET'])]
    public function auditAnswers(Formation $formation, FinanceSousMode $financeSousMode, $auditId = 1, $token = null)
    {
        $this->denyAccessIfInvalidToken($formation, $token);

        $pageUrls = [];

        $nbPatients = $formation->getAudit()->getNbPatients();
        foreach($formation->getParticipations() as $participation) {
            for($i=1; $i<=$nbPatients; $i++) {
                $pageUrls[] = $this->generateUrl('pdf_audit_answers_html', array('id' => $participation->getId(), 'financeSousMode' => $financeSousMode->getId(), 'auditId' => $auditId, 'patient' => $i), UrlGeneratorInterface::ABSOLUTE_URL);
            }
        }

        $this->snappy->setOption('orientation', "Portrait");
        $this->snappy->setOption('header-html', $this->generateUrl('pdf_header_participation', array(), UrlGeneratorInterface::ABSOLUTE_URL));
        $this->snappy->setOption('footer-html', $this->generateUrl('pdf_footer_audit', array(), UrlGeneratorInterface::ABSOLUTE_URL));
        $headerH = 40;
        $headerS = 5;
        $this->snappy->setOption('margin-top', $headerH + $headerS);
        $this->snappy->setOption('margin-left', 5);
        $this->snappy->setOption('margin-right', 5);
        $this->snappy->setOption('header-spacing', $headerS);
        $this->snappy->setOption('footer-spacing', 16);
        $this->snappy->setOption('margin-bottom', 46);

        return $this->getPdfResponse("file.pdf", $pageUrls);
    }

    /**
     * @param Formation $formation
     * @param FinanceSousMode $financeSousMode
     * @param int $surveyId
     * @param null $token
     * @param Request $request
     * @return Response
     */
    #[Route(path: '/survey-answers/{id}/{financeSousMode}/{surveyId}/pdf/{token}', name: 'pdf_survey_answers_pdf', methods: ['GET'])]
    public function surveyAnswers(Request $request, Formation $formation, FinanceSousMode $financeSousMode, $surveyId = 1, $token = null)
    {
//        $this->denyAccessIfInvalidToken($formation, $token);

        $pageUrls = [];

        foreach($formation->getParticipations() as $participation) {
            $pageUrls[] = $this->generateUrl('pdf_survey_answers_html', array('id' => $participation->getId(), 'financeSousMode' => $financeSousMode->getId(), 'surveyId' => $surveyId), UrlGeneratorInterface::ABSOLUTE_URL);
        }

        $pageOffset = $request->query->get('offset');

        $this->snappy->setOption('orientation', "Portrait");
        $this->snappy->setOption('header-html', $this->generateUrl('pdf_header_evaluation_global', array("id" => $formation->getId()), UrlGeneratorInterface::ABSOLUTE_URL));
        $this->snappy->setOption('footer-html', $this->generateUrl('pdf_footer_evaluation_global', array('offset' => $pageOffset), UrlGeneratorInterface::ABSOLUTE_URL));
        $this->snappy->setOption('margin-left', 0);
        $this->snappy->setOption('margin-right', 0);

        return $this->getPdfResponse("file.pdf", $pageUrls);
    }

    /**
     * @param null                                       $token
     * @return Response
     */
    #[Route(path: '/fusion-header/{id}/pdf/{token}', name: 'pdf_fusion_header_pdf', methods: ['GET'])]
    public function fusionHeader(Request $request, Formation $formation, $token = null)
    {
        $this->denyAccessIfInvalidToken($formation, $token);

        $pageUrl = $this->generateUrl('pdf_fusion_header_html', array('id' => $formation->getId()), UrlGeneratorInterface::ABSOLUTE_URL);
        $this->snappy->setOption('orientation', "Portrait");
        $pageOffset = $request->query->get('offset');
        $this->snappy->setOption('footer-html', $this->generateUrl('pdf_footer_audit', array('offset' => $pageOffset, 'padding' => 1), UrlGeneratorInterface::ABSOLUTE_URL));
        $this->snappy->setOption('margin-left', 0);
        $this->snappy->setOption('margin-right', 0);
        $this->snappy->setOption('footer-spacing', 16);
        $this->snappy->setOption('margin-bottom', 46);

        return $this->getPdfResponse("file.pdf", $pageUrl);
    }

    /**
     * @return Response
     */
    #[Route(path: '/fusion-methodologie/{id}/pdf', name: 'pdf_fusion_methodologie_pdf', methods: ['GET'])]
    public function fusionMethodologie(Formation $formation, Request $request)
    {
        $pageUrl = $this->generateUrl('pdf_fusion_methodologie_html', array('id' => $formation->getId(), 'actalians' => json_decode($request->query->get('actalians'), true)), UrlGeneratorInterface::ABSOLUTE_URL);
        $this->snappy->setOption('orientation', "Portrait");
        $pageOffset = $request->query->get('offset');
        $this->snappy->setOption('footer-html', $this->generateUrl('pdf_footer_audit', array('offset' => $pageOffset, 'padding' => 1), UrlGeneratorInterface::ABSOLUTE_URL));
        $this->snappy->setOption('margin-left', 0);
        $this->snappy->setOption('margin-right', 0);
        $this->snappy->setOption('footer-spacing', 0);
        $this->snappy->setOption('margin-bottom', 25);

        return $this->getPdfResponse("file.pdf", $pageUrl);
    }

    /**
     * @return Response
     */
    #[Route(path: '/fusion-summary/pdf', name: 'pdf_fusion_summary_pdf', methods: ['POST'])]
    public function fusionSummary(Request $request): Response
    {
        $pageUrl = $this->generateUrl('pdf_fusion_summary_html', array(), UrlGeneratorInterface::ABSOLUTE_URL);

        $postdata = http_build_query(
            array(
                'summary' => $request->request->get('summary')
            )
        );
        $opts = array('http' =>
              array(
                  'method'  => 'POST',
                  'header'  => 'Content-type: application/x-www-form-urlencoded',
                  'content' => $postdata
              ),
              "ssl" => array(
                  "verify_peer" => false,
                  "verify_peer_name" => false,
              ),
        );
        $context  = stream_context_create($opts);
        $html = file_get_contents($pageUrl, false, $context);

        $this->snappy->setOption('orientation', "Portrait");
        $this->snappy->setOption('footer-html', $this->generateUrl('pdf_footer_audit', array('offset' => 1), UrlGeneratorInterface::ABSOLUTE_URL));
        $this->snappy->setOption('margin-left', 5);
        $this->snappy->setOption('margin-right', 5);
        $this->snappy->setOption('footer-spacing', 16);
        $this->snappy->setOption('margin-bottom', 46);

        return new Response(
            $this->snappy->getOutputFromHtml($html),
            Response::HTTP_OK,
            array(
                'Content-Type'          => 'application/pdf',
                'Content-Disposition'   => 'inline; filename="file.pdf"'
            )
        );
    }

    /**
     * @param Request $request
     * @param Programme $programme
     * @param $role
     * @param null $token
     * @return Response
     */
    #[Route(path: '/programme/{programme}/{role}/pdf/{token}', name: 'pdf_evaluation_programme_global_role_pdf', methods: ['GET'])]
    public function pdfEvaluationGlobalProgramme(Programme $programme, $role, $token = null)
    {
        $this->denyAccessIfInvalidToken($programme, $token);
        $pageUrl = $this->generateUrl('pdf_evaluation_programme_global_role', array("programme" => $programme->getId(), "role" => $role), UrlGeneratorInterface::ABSOLUTE_URL);
        return $this->getEvaluationGlobalResponse($pageUrl);
    }

    /**
     * @param Request $request
     * @param Programme $programme
     * @param $role
     * @param null $token
     * @return Response
     */
    #[Route(path: '/formation/{formation}/{role}/pdf/{token}', name: 'pdf_evaluation_formation_global_role_pdf', methods: ['GET'])]
    public function pdfEvaluationGlobalFormation(Formation $formation, $role, $token = null)
    {
        $this->denyAccessIfInvalidToken($formation, $token);
        $pageUrl = $this->generateUrl('pdf_evaluation_formation_global_role', array("formation" => $formation->getId(), "role" => $role), UrlGeneratorInterface::ABSOLUTE_URL);
        return $this->getEvaluationGlobalResponse($pageUrl);
    }

    /**
     * @param Request $request
     * @param Formation $formation
     * @param Formateur $former
     * @param null $token
     * @return RedirectResponse|Response
     */
    #[Route(path: '/programme/{formation}/former/{former}/pdf/{token}', name: 'pdf_evaluation_formation_global_former_pdf', methods: ['GET'])]
    public function pdfEvaluationGlobalFormer(Formation $formation, Formateur $former, $token = null)
    {
        $this->denyAccessIfInvalidToken($former, $token);
        $pageUrl = $this->generateUrl('pdf_evaluation_formation_global_former', array("formation" => $formation->getId(), "former" => $former->getId()), UrlGeneratorInterface::ABSOLUTE_URL);
        return $this->getEvaluationGlobalResponse($pageUrl);
    }

    /**
     * @param Request $request
     * @param Person $person
     * @param $year
     * @param null $token
     * @return RedirectResponse|Response
     */
    #[Route(path: '/former/{person}/{year}/pdf/{token}', name: 'pdf_evaluation_global_former_year_pdf', methods: ['GET'])]
    public function pdfEvaluationGlobalFormerYear(Person $person, $year, $token = null)
    {
        $this->denyAccessIfInvalidToken($person, $token);
        $pageUrl = $this->generateUrl('pdf_evaluation_global_former_year', array("person" => $person->getId(), "year" => $year), UrlGeneratorInterface::ABSOLUTE_URL);
        return $this->getEvaluationGlobalResponse($pageUrl);
    }

    /**
     * @param Request $request
     * @param Person $person
     * @param $year
     * @param null $token
     * @return RedirectResponse|Response
     */
    #[Route(path: '/coordinator/{person}/{year}/pdf/{token}', name: 'pdf_evaluation_global_coordinator_year_pdf', methods: ['GET'])]
    public function pdfEvaluationGlobalCoordinatorYear(Person $person, $year, $token = null)
    {
        $this->denyAccessIfInvalidToken($person, $token);
        $pageUrl = $this->generateUrl('pdf_evaluation_global_coordinator_year', array("person" => $person->getId(), "year" => $year), UrlGeneratorInterface::ABSOLUTE_URL);
        return $this->getEvaluationGlobalResponse($pageUrl);
    }

    /**
     * @param Request $request
     * @param $title
     * @param $role
     * @param $year
     * @param null $token
     * @return Response
     */
    #[Route(path: '/topo/{year}/{role}/{title}/pdf/{token}', name: 'pdf_evaluation_global_topo_pdf', requirements: ['title' => '.+'], methods: ['GET'])]
    public function pdfEvaluationGlobalTopo(string $title, $role, $year, $token = null)
    {
        $pageUrl = $this->generateUrl('pdf_evaluation_global_topo', array("title" => $title, "role" => $role, "year" => $year), UrlGeneratorInterface::ABSOLUTE_URL);
        return $this->getEvaluationGlobalResponse($pageUrl);
    }

    public function getEvaluationGlobalResponse($pageUrl) {
        $this->snappy->setOption('orientation', "Portrait");
        $this->snappy->setOption('footer-html', $this->generateUrl('pdf_footer_evaluation_global', array(), UrlGeneratorInterface::ABSOLUTE_URL));
        $this->snappy->setOption('margin-top', 0);
        $this->snappy->setOption('margin-left', 0);
        $this->snappy->setOption('margin-right', 0);
        $this->snappy->setOption('margin-bottom', 20);

        return $this->getPdfResponse("file.pdf", $pageUrl);
    }

    /**
     * @param Formation $formation
     * @param FinanceSousMode $financeSousMode
     * @param null $token
     * @return Response
     */
    #[Route(path: '/attestation-presence/{id}/pdf/{token}', name: 'pdf_attestation-presence_pdf', methods: ['GET'])]
    public function attestationPresence(Formation $formation, $token = null)
    {
        $this->denyAccessIfInvalidToken($formation, $token);

        $pageUrl = $this->generateUrl('pdf_attestation-presence_html', array('id' => $formation->getId()), UrlGeneratorInterface::ABSOLUTE_URL);
        $this->snappy->setOption('disable-javascript', true);
        $this->snappy->setOption('orientation', "Portrait");
        $this->snappy->setOption('header-html', $this->generateUrl('pdf_header_attestation_presence_document', array("actalians" => false, 'isClasseVirtuelleZoom' => $formation->getProgramme()->isClasseVirtuelle() && false), UrlGeneratorInterface::ABSOLUTE_URL));
        // $this->snappy->setOption('footer-html', $this->generateUrl('pdf_footer_traceability_document', array(), UrlGeneratorInterface::ABSOLUTE_URL));
        // $this->snappy->setOption('margin-bottom', 24.2);

        $headerH = 30;
        $headerS = 5;
        $this->snappy->setOption('margin-top', $headerH + $headerS);
        $this->snappy->setOption('header-spacing', $headerS);
        $this->snappy->setOption('margin-left', 0);
        $this->snappy->setOption('margin-right', 0);

        $formRef = $formation->getProgramme()->getReference();
        $filename = "doc-de-tracabilite-".$formRef.".pdf";

        return $this->getPdfResponse($filename, $pageUrl);
    }

    /**
     * @param Participation $participation
     * @param int                                       $auditId
     * @param null                                      $token
     * @param Request $request
     * @return Response
     */
    #[Route(path: '/tcs/{id}/pdf/{token}', name: 'pdf_tcs_pdf', methods: ['GET'])]
    public function tcs(Request $request, Participation $participation, $token = null)
    {
        $this->denyAccessIfInvalidToken($participation, $token);

        $pageOffset = $request->query->get('offset');

        $pageUrl = $this->generateUrl('pdf_tcs_html', array('id' => $participation->getId()), UrlGeneratorInterface::ABSOLUTE_URL);
        $this->snappy->setOption('orientation', "Portrait");
        $this->snappy->setOption('javascript-delay', 1000);
        $this->snappy->setOption('header-html', $this->generateUrl('pdf_header_evaluation_global', array("id" => $participation->getFormation()->getId()), UrlGeneratorInterface::ABSOLUTE_URL));
        $this->snappy->setOption('footer-html', $this->generateUrl('pdf_footer_evaluation_global', array('offset' => $pageOffset), UrlGeneratorInterface::ABSOLUTE_URL));
        $this->snappy->setOption('margin-top', 36);
        $this->snappy->setOption('margin-left', 0);
        $this->snappy->setOption('margin-right', 0);

        $filename = $participation->getFormation()->getProgramme()->getReference()."-tcs-".$participation->getParticipant()->getLastname().".pdf";

        return $this->getPdfResponse($filename, $pageUrl);
    }

    /**
     * @param Formation $formation
     * @param null $token
     * @param Request $request
     * @return Response
     */
    #[Route(path: '/tcs/empty/{id}/pdf/{token}', name: 'pdf_tcs_empty_pdf', methods: ['GET'])]
    public function tcsEmpty(Request $request, Formation $formation, $token = null)
    {
        $this->denyAccessIfInvalidToken($formation, $token);

        $pageOffset = $request->query->get('offset');

        $pageUrl = $this->generateUrl('pdf_tcs_empty_html', array('id' => $formation->getId()), UrlGeneratorInterface::ABSOLUTE_URL);
        $this->snappy->setOption('orientation', "Portrait");
        $this->snappy->setOption('javascript-delay', 1000);
        $this->snappy->setOption('header-html', $this->generateUrl('pdf_header_evaluation_global', array("id" => $formation->getId()), UrlGeneratorInterface::ABSOLUTE_URL));
        $this->snappy->setOption('footer-html', $this->generateUrl('pdf_footer_evaluation_global', array('offset' => $pageOffset), UrlGeneratorInterface::ABSOLUTE_URL));
        $this->snappy->setOption('margin-top', 36);
        $this->snappy->setOption('margin-left', 0);
        $this->snappy->setOption('margin-right', 0);

        $filename = "tcs-vierge-".$formation->getProgramme()->getReference().".pdf";

        return $this->getPdfResponse($filename, $pageUrl);
    }

    public function generatePdfAndResponse($downloadedPlaquetteFile, $user, DownloadedPlaquetteFileManager $downloadedPlaquetteFileManager, $pageUrl, $filename)
    {
        $dpf = new DownloadedPlaquetteFile();
        $dpf->setTitre('Soirée de formation');
        $new_download_plaquette_file_form = $this->createForm(DownloadedPlaquetteFileType::class, $dpf);
        $filename = $this->stripAccents($filename);
        try {
            $directLink = sprintf(DownloadedPlaquetteFile::PATHS_DIR[$downloadedPlaquetteFile->getType()], $user->getId(), $filename);
            $filefullroot = $this->parameterBag->get('kernel.project_dir').'/public/'.$directLink;
            $downloadedPlaquetteFile->setDirectLink($directLink);
            $downloadedPlaquetteFileManager->removeFile($downloadedPlaquetteFile);
            $this->snappy->generate($pageUrl, $filefullroot);
        } catch (\Exception $e) {
            $this->pdfLogger->critical($e->getMessage());
            return new JsonResponse([
                'etat' => false,
                'error' => $e->getMessage(),
                'download_plaquette_file_form' => $this->renderView('admin/plaquette/formDownload.html.twig', array(
                    'download_plaquette_file_form' => $new_download_plaquette_file_form->createView(),
                )),
            ]);
        }
        $downloadedPlaquetteFileManager->addDownloadPlaquetteFile($downloadedPlaquetteFile);
        return new JsonResponse([
            'etat' => true,
            'pdf' => $filename,
            'filefullroot' => $filefullroot,
            'directLink' => $directLink,
            'download_plaquette_file_form' => $this->renderView('admin/plaquette/formDownload.html.twig', array(
                'download_plaquette_file_form' => $new_download_plaquette_file_form->createView(),
            )),
        ]);
    }

    public function stripAccents($str): string
    {
        return strtr(utf8_decode($str), utf8_decode('àáâãäçèéêëìíîïñòóôõöùúûüýÿÀÁÂÃÄÇÈÉÊËÌÍÎÏÑÒÓÔÕÖÙÚÛÜÝ'), 'aaaaaceeeeiiiinooooouuuuyyAAAAACEEEEIIIINOOOOOUUUUY');
    }

    public function getPdfResponse($filename, $pageUrls) {
        try {
            $content = $this->snappy->getOutput((array)$pageUrls);
        } catch (\Exception $e) {
            $this->pdfLogger->critical($e->getMessage());
            throw new HttpException(Response::HTTP_INTERNAL_SERVER_ERROR, $e->getMessage());
        }
        return new Response(
            $content,
            Response::HTTP_OK,
            array(
                'Content-Type'          => 'application/pdf',
                'Content-Disposition'   => sprintf("inline; filename=\"%s\"", $filename)
            )
        );
    }
}
