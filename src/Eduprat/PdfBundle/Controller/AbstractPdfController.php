<?php

namespace Eduprat\PdfBundle\Controller;

use Ed<PERSON><PERSON>\DomainBundle\Entity\ObjectWithTokenInterface;
use Symfony\Bundle\FrameworkBundle\Controller\AbstractController;
use Symfony\Component\HttpKernel\Exception\AccessDeniedHttpException;
use Symfony\Component\Security\Core\Authorization\AuthorizationCheckerInterface;

abstract class AbstractPdfController extends AbstractController
{
    public function __construct(
        private readonly AuthorizationCheckerInterface $authorizationChecker
    )
    {
    }

    protected function denyAccessIfInvalidToken(ObjectWithTokenInterface $object, $token, $message = 'Access Denied.'): void
    {
        if (!($this->authorizationChecker->isGranted('IS_AUTHENTICATED_FULLY') && $this->authorizationChecker->isGranted('ROLE_SUPER_ADMIN')) && $object->getToken() !== $token) {
            throw new AccessDeniedHttpException();
        }
    }
}