<?php

namespace Eduprat\PdfBundle\Services;

use Doctrine\ORM\EntityManagerInterface;
use Psr\Log\LoggerInterface;
use Symfony\Component\DependencyInjection\ParameterBag\ParameterBagInterface;
use Twig\Environment;
use Doctrine\Common\Collections\ArrayCollection;
use Eduprat\AuditBundle\Services\CourseManager;


class TraceabilityPDF extends AbstractPDFGenerator
{
    private String $footer = "pdf/footers/gotenberg-traceability.html.twig";

    public function generate(array $params = [], ?array $paramsHeader = [], ?array $paramsFooter = [], ?string $file = null): \Psr\Http\Message\ResponseInterface|string
    {
        $formation = $params['formation'] ?? throw new \LogicException('parameter formation not found');
        $financeSousMode = $params['financeSousMode'] ?? throw new \LogicException('parameter formation not found');
        $zoom = $params['zoom'] ?? throw new \LogicException('parameter formation not found');
        $empty = $params['empty'] ?? throw new \LogicException('parameter formation not found');
        
        // On ordonne les participants dans l'ordre alphabétique des lastnames
        $participations = $formation->getParticipationsPerMode($financeSousMode);
        $iterator = $participations->getIterator();
        $iterator->uasort(function ($a, $b) {
            return (ucfirst(strtolower($a->getParticipant()->getLastname())) < ucfirst(strtolower($b->getParticipant()->getLastname()))) ? -1 : 1;
        });

        $averageParticipationTime = 0;

        /** @var Participation $participation */
        foreach($participations as $participation) {
            $totalTime = $participation->getTotalTimeLogs();
            if($totalTime > 0) {
                $participation->setTotalTimeText($this->timestampToHoursMinutes($totalTime));
            }
            $averageParticipationTime += $totalTime;
        }

        $averageParticipationTime = $averageParticipationTime / max(count($participations), 1);
        $averageParticipationTime = $this->timestampToHoursMinutes($averageParticipationTime);

        $averageParticipationTime = 0;

        /** @var Participation $participation */
        foreach($participations as $participation) {
            $totalTime = $participation->getTotalTimeLogs();
            if($totalTime > 0) {
                $participation->setTotalTimeText($this->timestampToHoursMinutes($totalTime));
            }
            $averageParticipationTime += $totalTime;
        }

        $averageParticipationTime = $averageParticipationTime / max(count($participations), 1);
        $averageParticipationTime = $this->timestampToHoursMinutes($averageParticipationTime);

        if ($financeSousMode->isActalians()) {
            $template = 'traceability_document_actalians';
        } elseif($formation->getProgramme()->isClasseVirtuelle() && $request->get("zoom")) {
            $template = 'traceability_document_virtuelle';
        } else {
            $template = 'traceability_document';
        }

        $content = $this->twig->render('pdf/' . $template . '.html.twig', array(
            'formation' => $formation,
            'participations' => $participations,
            'averageParticipationTime' => $averageParticipationTime,
            'isZoom' => $zoom,
            'isEmpty' => $empty,
        ));


        if ($financeSousMode->isActalians()) {
            $this->footer = 'pdf/footers/gotenberg-evaluation-global.html.twig';
        }

        return $this->build($content, $paramsHeader, $paramsFooter, $file);
    }

    protected function margins(): array
    {
        return [2.4, 1.1, 0, 0];
    }

    public function getHeaderTwigPath(): string
    {
        return 'pdf/headers/gotenberg-traceability-document.html.twig';
    }

    public function getFooterTwigPath(): string
    {
        return $this->footer;
    }

    private function timestampToHoursMinutes($timestamp): string
    {
        $hours = floor($timestamp / 3600);
        $timestamp = $timestamp - ($hours * 3600);
        $minutes = floor(($timestamp / 60) % 60);
        $secondes = $timestamp - ($minutes * 60);
        $minutes = $secondes >= 30 ? $minutes + 1 : $minutes;
        return sprintf("%02dh%02d", $hours, $minutes);
    }
}