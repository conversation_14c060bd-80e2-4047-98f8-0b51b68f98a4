<?php

namespace Eduprat\PdfBundle\Services;

use Eduprat\DomainBundle\Services\InvoiceManager;
use Doctrine\Common\Collections\ArrayCollection;
use Doctrine\ORM\EntityManagerInterface;
use Psr\Log\LoggerInterface;
use Symfony\Component\DependencyInjection\ParameterBag\ParameterBagInterface;
use Twig\Environment;

class InvoicePDF extends AbstractPDFGenerator
{

    public function __construct(
        Environment            $twig,
        LoggerInterface        $pdfLogger,
        EntityManagerInterface $entityManager,
        ParameterBagInterface  $parameterBag,
        protected readonly InvoiceManager  $invoiceManager,
    )
    {
        parent::__construct($twig, $pdfLogger, $entityManager, $parameterBag);
    }

    public function generate(array $params = [], ?array $paramsHeader = [], ?array $paramsFooter = [], ?string $file = null): \Psr\Http\Message\ResponseInterface|string
    {
        $formation = $params['formation'] ?? throw new \LogicException('parameter formation not found');
        $financeSousMode = $params['financeSousMode'] ?? throw new \LogicException('parameter financeSousMode not found');
        $n1 = $params['n1'] ?? throw new \LogicException('parameter n1 not found');
        $proforma = $params['proforma'] ?? throw new \LogicException('parameter n1 not found');

        $participations = $formation->getParticipationsPerMode($financeSousMode);
        $iterator = $participations->getIterator();
        $iterator->uasort(function ($a, $b) {
            return (ucfirst(strtolower($a->getParticipant()->getLastname())) < ucfirst(strtolower($b->getParticipant()->getLastname()))) ? -1 : 1;
        });

        $openingDate = $formation->getOpeningDate();
        $closingDate = $formation->getClosingDate();
        $year = $n1 ? ((int) $openingDate->format("Y")) + 1 : $openingDate->format("Y");

        if ($formation->isPluriAnnuelle()) {
            $openingDate = $formation->getFirstUnityDateOfYear($year);
            $closingDate = $formation->getLastUnityDateOfYear($year);
        }

        $participations = new ArrayCollection(iterator_to_array($iterator));

        if ($proforma) {
            $invoice = $this->invoiceManager->getInvoiceProforma($formation, $financeSousMode, $year);
            $template = "invoice_proforma";
        } else {
            $invoice = $this->invoiceManager->getInvoice($formation, $financeSousMode, $year);
            $template = "invoice";
        }
        

        $content = $this->twig->render('pdf/' . $template . '.html.twig', array(
            'formation' => $formation,
            'financeSousMode' => $financeSousMode,
            'participations' => $participations,
            'invoice' => $invoice,
            'openingDate' => $openingDate,
            'closingDate' => $closingDate,
            'n1' => $n1,
        ));

        return $this->build($content, $paramsHeader, $paramsFooter, $file);
    }

    protected function margins(): array
    {
        return [0, 1.5, 0, 0];
    }

    public function getHeaderTwigPath(): string
    {
        return 'pdf/headers/gotenberg-no-header.html.twig';
    }

    public function getFooterTwigPath(): string
    {
        return 'pdf/footers/gotenberg-invoice.html.twig';
    }
}