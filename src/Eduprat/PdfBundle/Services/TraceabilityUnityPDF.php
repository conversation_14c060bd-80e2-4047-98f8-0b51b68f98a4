<?php

namespace Eduprat\PdfBundle\Services;

use Doctrine\ORM\EntityManagerInterface;
use Psr\Log\LoggerInterface;
use Symfony\Component\DependencyInjection\ParameterBag\ParameterBagInterface;
use Twig\Environment;
use Doctrine\Common\Collections\ArrayCollection;
use Eduprat\AuditBundle\Services\CourseManager;


class TraceabilityUnityPDF extends AbstractPDFGenerator
{

    private String $footer = "pdf/footers/gotenberg-traceability.html.twig";
    
    public function __construct(
        Environment            $twig,
        LoggerInterface        $pdfLogger,
        EntityManagerInterface $entityManager,
        ParameterBagInterface  $parameterBag,
        protected readonly CourseManager  $courseManager,
    )
    {
        parent::__construct($twig, $pdfLogger, $entityManager, $parameterBag);
    }

    public function generate(array $params = [], ?array $paramsHeader = [], ?array $paramsFooter = [], ?string $file = null): \Psr\Http\Message\ResponseInterface|string
    {
        $formation = $params['formation'] ?? throw new \LogicException('parameter formation not found');
        $financeSousMode = $params['financeSousMode'] ?? throw new \LogicException('parameter formation not found');
        $unity = $params['unity'] ?? throw new \LogicException('parameter formation not found');
        
        // On ordonne les participants dans l'ordre alphabétique des lastnames
        $participations = $formation->getParticipationsPerMode($financeSousMode);
        $iterator = $participations->getIterator();
        $iterator->uasort(function ($a, $b) {
            return (ucfirst(strtolower($a->getParticipant()->getLastname())) < ucfirst(strtolower($b->getParticipant()->getLastname()))) ? -1 : 1;
        });

        $participations = new ArrayCollection(iterator_to_array($iterator));

        foreach($participations as $participation) {
            $totalTime = $participation->getTotalTimeLogsByStep("step_".$unity, true, true, (bool)$formation->getProgramme()->isElearningOneUnity(), false, true, true);
            if($totalTime > 0) {
                $participation->setTotalTime($totalTime / 60);
            }
        }

        $modulesTimes = $formation->getProgramme()->isElearningOneUnity() ? $formation->getModuleTimesByUnityToArray() : $formation->getModuleTimesByUnityToArray()[$unity];
        $modulesTimes = $formation->getProgramme()->isElearningOneUnity() ? array_merge($modulesTimes[1], $modulesTimes[2], $modulesTimes[3]) : $modulesTimes;
        $closingDate = $formation->getLastUnityDateOfYear($formation->getUnityByPosition($unity)->getClosingDate()->format("Y"));        

        $template = "traceability_document_unity";

        $content = $this->twig->render('pdf/' . $template . '.html.twig', array(
            'formation' => $formation,
            'participations' => $participations,
            'unityPosition' => $unity,
            'unity' => $formation->getProgramme()->getUnityByPosition($unity),
            'moduleTimes' => $modulesTimes,
            "modules" => $this->courseManager->getModuleList($formation),
            "closingDate" => $closingDate
        ));


        if ($financeSousMode->isActalians()) {
            $this->footer = 'pdf/footers/gotenberg-evaluation-global.html.twig';
        }

        return $this->build($content, $paramsHeader, $paramsFooter, $file);
    }

    protected function margins(): array
    {
        return [2.4, 1.1, 0, 0];
    }

    public function getHeaderTwigPath(): string
    {
        return 'pdf/headers/gotenberg-traceability-document.html.twig';
    }

    public function getFooterTwigPath(): string
    {
        return $this->footer;
    }
}