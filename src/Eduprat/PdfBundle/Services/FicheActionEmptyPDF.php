<?php

namespace Eduprat\PdfBundle\Services;

use Psr\Http\Message\ResponseInterface;
use Doctrine\ORM\EntityManagerInterface;
use Ed<PERSON>rat\AuditBundle\Form\FicheActionCollectionType;
use Eduprat\DomainBundle\Entity\FicheAction;
use Eduprat\DomainBundle\Entity\Formation;
use Eduprat\DomainBundle\Entity\Participation;
use Psr\Log\LoggerInterface;
use Symfony\Component\DependencyInjection\ParameterBag\ParameterBagInterface;
use Symfony\Component\Form\FormFactoryInterface;
use Symfony\Component\HttpFoundation\Request;
use Twig\Environment;

class FicheActionEmptyPDF extends AbstractPDFGenerator
{
    public function __construct(
        Environment            $twig,
        LoggerInterface        $pdfLogger,
        EntityManagerInterface $entityManager,
        ParameterBagInterface  $parameterBag,
        private readonly FormFactoryInterface $formBuilder,
    )
    {
        parent::__construct($twig, $pdfLogger, $entityManager, $parameterBag);
    }


    public function generate(array $params = [], ?array $paramsHeader = [], ?array $paramsFooter = [], ?string $file = null): string|ResponseInterface
    {
        $formation = $params['formation'] ?? throw new \LogicException('parameter formation not found');
        $request = $params['request'] ?? throw new \LogicException('parameter request not found');

        $content = $this->generateContent($formation, $request);

        $paramsHeader['formation'] = $formation;
        return $this->build($content, $paramsHeader, $paramsFooter, $file);
    }

    protected function margins(): array
    {
        return [1.5, 1, 0, 0];
    }

    public function getHeaderTwigPath(): string
    {
        return 'pdf/headers/gotenberg-evaluation-global.html.twig';
    }

    public function getFooterTwigPath(): string
    {
        return 'pdf/footers/gotenberg-evaluation-global.html.twig';
    }

    private function generateContent(Formation $formation, Request $request): string
    {
        $participation = new Participation();
        $participation->setFormation($formation);

        for ($i = 1; $i <= 5; $i++) {
            $sampleAction = new FicheAction();
            $participation->addFicheAction($sampleAction);
        }

        $form = $this->formBuilder->create(FicheActionCollectionType::class, $participation, array(
            "step" => "all",
        ));

        $programme = $participation->getFormation()->getProgramme();
        $hasEtutorat = $programme->hasEtutorat();
        $form->handleRequest($request);

        return $this->twig->render('pdf/empty/action.html.twig', array(
            "participation" => $participation,
            "formation" => $participation->getFormation(),
            "hasEtutorat" => $hasEtutorat,
            'form' => $form->createView(),
        ));
    }
}
