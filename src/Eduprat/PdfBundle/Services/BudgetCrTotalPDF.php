<?php

namespace Eduprat\PdfBundle\Services;

use Psr\Http\Message\ResponseInterface;
use Doctrine\ORM\EntityManagerInterface;
use Eduprat\AdminBundle\Services\CoordinatorHonorary;
use Eduprat\DomainBundle\Entity\Coordinator;
use Eduprat\DomainBundle\Entity\Formation;
use Eduprat\DomainBundle\Entity\Participation;
use Psr\Log\LoggerInterface;
use Symfony\Component\DependencyInjection\ParameterBag\ParameterBagInterface;
use Twig\Environment;

class BudgetCrTotalPDF extends AbstractPDFGenerator
{
    public function __construct(
        Environment            $twig,
        LoggerInterface        $pdfLogger,
        EntityManagerInterface $entityManager,
        ParameterBagInterface  $parameterBag,
        private readonly CoordinatorHonorary $coordinatorHonoraryService,
)
    {
        parent::__construct($twig, $pdfLogger, $entityManager, $parameterBag);
    }


    public function generate(array $params = [], ?array $paramsHeader = [], ?array $paramsFooter = [], ?string $file = null): string|ResponseInterface
    {
        $formation = $params['formation'] ?? throw new \LogicException('parameter formation not found');
        $coordinator = $params['coordinator'] ?? throw new \LogicException('parameter coordinator not found');
        $n1 = $params['n1'] ?? throw new \LogicException('parameter n1 not found');

        $content = $this->generateContent($formation, $coordinator, $n1);

        return $this->build($content, $paramsHeader, $paramsFooter, $file);
    }

    protected function margins(): array
    {
        return [1.3, 1, 0, 0];
    }

    public function getHeaderTwigPath(): string
    {
        return 'pdf/headers/gotenberg-evaluation-global.html.twig';
    }

    public function getFooterTwigPath(): string
    {
        return 'pdf/footers/gotenberg-evaluation-global.html.twig';
    }

    private function generateContent(Formation $formation, Coordinator $coordinator, ?bool $n1 = false): string
    {
        $newHonorary = $formation->getStartDate() >= new \DateTime($this->parameterBag->get('honoraires.migration_date'));

        $caDetails = array();
        $formations = array();
        if (!$newHonorary) {
            $formations[$formation->getProgramme()->getReference()] = $this->coordinatorHonoraryService->calculHonorarySession($formation, $coordinator, $n1);
        } else {
            /** @var Participation $participation */
            foreach ($formation->getParticipationsByCoordinator($coordinator->getPerson()) as $participation) {
                if (!isset($caDetails[(string) $n1 ? $participation->getPriceYearN1() : $participation->getPrice()])) {
                    $caDetails[(string) $n1 ? $participation->getPriceYearN1() : $participation->getPrice()] = array(
                        "price" => $n1 ? $participation->getPriceYearN1() : $participation->getPrice(),
                        "count" => 0,
                        "sum" => 0
                    );
                }
                $caDetails[(string) $n1 ? $participation->getPriceYearN1() : $participation->getPrice()]["count"]++;
                $caDetails[(string) $n1 ? $participation->getPriceYearN1() : $participation->getPrice()]["sum"] += $n1 ? $participation->getPriceYearN1() : $participation->getPrice();
            }
        }

        $coordinatorRepo = $this->entityManager->getRepository(Coordinator::class);
        $budgetCR = array();
        $budgetCR['coordinator'] = $coordinatorRepo->findCoordinatorByFormationAndPerson($formation, $coordinator->getPerson());
        $budgetCR['honorary'] = $this->coordinatorHonoraryService->calculTotalHonorary($formation, $coordinator, true, false, true, $n1);

        $restaurationHonorary = $coordinator->getRestaurationHonorary();

        $person = $coordinator->getPerson();

        $showCosts = $coordinator->takeAccountBecauseOnSite($n1);

        $openingDate = $formation->getOpeningDate();
        $closingDate = $formation->getClosingDate();
        $year = $n1 ? ((int) $openingDate->format("Y")) + 1 : $openingDate->format("Y");

        if ($formation->isPluriAnnuelle()) {
            $openingDate = $formation->getFirstUnityDateOfYear($year);
            $closingDate = $formation->getLastUnityDateOfYear($year);
        }

        return $this->twig->render('pdf/budget_cr_total.html.twig', array(
            'coordinator' => $person,
            'coordinatorObject' => $coordinator,
            'restaurationHonorary' => $restaurationHonorary,
            'formation' => $formation,
            'budgetCR' => $budgetCR,
            'formations' => $formations,
            'newHonorary' => $newHonorary,
            'caDetails' => $caDetails,
            'n1' => $n1,
            'showCosts' => $showCosts,
            'openingDate' => $openingDate,
            'closingDate' => $closingDate,
        ));
    }
}