<?php

namespace Eduprat\CrmBundle\Services;

use Doctrine\ORM\EntityManagerInterface;
use Eduprat\AdminBundle\Entity\ParticipantSearch;
use Eduprat\DomainBundle\Entity\Participant;
use Eduprat\DomainBundle\Entity\Participation;
use Eduprat\DomainBundle\Entity\Programme;
use Eduprat\DomainBundle\Form\ProgrammeType;

class ReportingService {

    /**
     * @var EntityManagerInterface
     */
    private EntityManagerInterface $entityManager;

    public function __construct(EntityManagerInterface $entityManager) {
        $this->entityManager = $entityManager;
    }

    public function generateYearReport(array $years)
    {
        return array(
            "andpcStatuses" => $this->getAndpcCounts($years),
            "professional" => $this->getProfessionalReport($years),
            "methods" => $this->getMethodReport($years),
            "formats" => $this->getFormatReport($years),
            "exercises" => $this->getExercisesModeReport($years),
        );
    }

    public function generateParticipantsReport(array $years)
    {
        return array(
            "categories" => null,
            "prospectCategories" => $this->getProspectCategory(),
            "participationCategories" => $this->getParticipationCategories($years),
            "caCategories" => $this->getCaCategories($years),
            "participationExercisesModes" => $this->getParticipationExercisesModes($years),
            "participationMethods" => $this->getParticipationMethods($years),
            "participationFormats" => $this->getParticipationFormats($years),
            "participationActionCategories" => $this->getParticipationActionCategories($years),
        );
    }

    public function generateParticipantsReportCategories(ParticipantSearch $search, array $years)
    {
        return array(
            "categories" => $this->getCategoryReport($search, $years),
        );
    }

    public function getAndpcStatusColor($status) {
        switch ($status) {
            case Programme::ANDPC_STATUS_SUBMITTED: return "blue"; break;
            case Programme::ANDPC_STATUS_PUBLISHED: return "green"; break;
            case Programme::ANDPC_STATUS_EVALUATED: return "yellow"; break;
            case Programme::ANDPC_STATUS_REJECTED: return "red"; break;
        }
        return null;
    }

    public function getAndpcStatusIcon($status) {
        switch ($status) {
            case Programme::ANDPC_STATUS_SUBMITTED: return "upload"; break;
            case Programme::ANDPC_STATUS_PUBLISHED: return "check-circle"; break;
            case Programme::ANDPC_STATUS_EVALUATED: return "search"; break;
            case Programme::ANDPC_STATUS_REJECTED: return "times-circle"; break;
        }
        return null;
    }

    public function getAndpcCounts(array $years)
    {
        $repo = $this->entityManager->getRepository(Programme::class);
        $result = array();

        foreach ($years as $y) {
            $currentCounts = $repo->countByAndpcStatus($y);

            $andpcStatuses = array();

            foreach (Programme::ANDPC_STATUSES as $index => $andpcStatus) {
                $andpcStatuses[$index] = array(
                    "label" => $andpcStatus . "s",
                    "total" => 0,
                    "previous" => 0,
                    "icon" => $this->getAndpcStatusIcon($index),
                    "color" => $this->getAndpcStatusColor($index)
                );
            }

            foreach ($currentCounts as $count) {
                if ($count["andpcStatus"]) {
                    $andpcStatuses[$count["andpcStatus"]]["total"] = (int) $count["total"];
                }
            }

            $result[$y] = $andpcStatuses;
        }
        
        return $result;
    }

    public function getProfessionalReport(array $years)
    {
        $repo = $this->entityManager->getRepository(Programme::class);

        $sortByTotal = function($a, $b) {
            return $b['total'] <=> $a['total'];
        };

        $result = array();

        foreach ($years as $y) {
            $counts = $repo->countByCategory($y);

            $categoriesGroups = array();
            $categoriesMono = array();
            $totalActionsMono = 0;
            $totalActionsPluri = 0;

            foreach ($counts as $currentCount) {
                if ($currentCount["categories"]) {
                    $categories = unserialize($currentCount["categories"]);
                    if ($categories) {
                        sort($categories);
                        $categoriesArray = count($categories) > 1 ? "categoriesGroups" : "categoriesMono";
                        if (count($categories) > 1) {
                            $totalActionsPluri += $currentCount["total"];
                        } else {
                            $totalActionsMono += $currentCount["total"];
                        }
                        $hash = md5(serialize($categories));
                        if (!isset($$categoriesArray[$hash])) {
                            $$categoriesArray[$hash] = array(
                                "categories" => $categories,
                                "total" => 0
                            );
                        }
                        $$categoriesArray[$hash]["total"] += (int) $currentCount["total"];
                    }
                }
            }

            usort($categoriesGroups, $sortByTotal);
            usort($categoriesMono, $sortByTotal);

            foreach ($categoriesGroups as &$item) {
                $item["percent"] = $item["total"] / $totalActionsPluri * 100;
            }

            foreach ($categoriesMono as &$item) {
                $item["percent"] = $item["total"] / $totalActionsMono * 100;
            }

            $result[$y] = array(
                "categoriesGroup" => $categoriesGroups,
                "categoriesMono" => $categoriesMono,
                "totalActionsMono" => $totalActionsMono,
                "totalActionsPluri" => $totalActionsPluri,
            );
        }

        return $result;
    }

    public function getMethodReport(array $years)
    {
        $repo = $this->entityManager->getRepository(Programme::class);

        $sortByTotal = function($a, $b) {
            return $b['total'] <=> $a['total'];
        };

        $result = array();

        foreach ($years as $y) {
            $counts = $repo->countByMethod($y);

            $methods = array(
                Programme::METHOD_CONTINUE =>  array(
                    "total" => 0,
                    "categories" => array()
                ),
                Programme::METHOD_INTEGRE =>  array(
                    "total" => 0,
                    "categories" => array()
                ),
            );

            foreach ($counts as $currentCount) {
                if ($currentCount["method"]) {
                    $method = $currentCount["method"];
                    $total = (int)$currentCount["total"];
                    $categories = unserialize($currentCount["categories"]);
                    $methods[$method]["total"] += $total;
                    if ($categories) {
                        foreach ($categories as $category) {
                            if (!isset($methods[$method]["categories"][$category])) {
                                $methods[$method]["categories"][$category] = array("category" => $category, "total" => 0);
                            }
                            $methods[$method]["categories"][$category]["total"] += $total;
                        }
                    }
                }
            }

            foreach ($methods as $index => $method) {
                usort($methods[$index]["categories"], $sortByTotal);
                foreach ($methods[$index]["categories"] as $category => &$value) {
                    $value["percent"] = $value["total"] / $method["total"] * 100;
                }
            }

            $result[$y] = $methods;
        }

        return $result;
    }

    public function getFormatReport(array $years)
    {
        $repo = $this->entityManager->getRepository(Programme::class);

        $result = array();

        foreach ($years as $y) {
            $counts = $repo->countByFormat($y);
            $formats = array(
                Programme::FORMAT_MIXTE => 0,
                Programme::FORMAT_ELEARNING => 0,
                Programme::FORMAT_PRESENTIEL => 0,
            );

            foreach ($counts as $currentCount) {
                $durationNotPresentielleTotal = ($currentCount["durationNotPresentielle"] ? $currentCount["durationNotPresentielle"] : 0) + ($currentCount["durationNotPresentielleActalians"] ? $currentCount["durationNotPresentielleActalians"] : 0);
                $durationPresentielle = $currentCount["durationPresentielle"] ? $currentCount["durationPresentielle"] : 0;
                $format = Programme::FORMAT_MIXTE;
                $format = $durationNotPresentielleTotal > 0 ? Programme::FORMAT_ELEARNING : $format;
                $format = $durationPresentielle > 0 ? Programme::FORMAT_PRESENTIEL : $format;
                $format = $durationNotPresentielleTotal > 0 && $durationPresentielle > 0 ? Programme::FORMAT_MIXTE : $format;
                $formats[$format] += (int) $currentCount["total"];
            }

            $result[$y] = $formats;
        }

        return $result;
    }

    public function getExercisesModeReport(array $years)
    {
        $repo = $this->entityManager->getRepository(Programme::class);

        $result = array();

        foreach ($years as $y) {
            $counts = $repo->countByExercisesMode($y);
            $exercises = array(
                "mixte" =>  array(
                    "label" => "Mixte (libéral + salarié)",
                    "total" => 0
                ),
                "salarie" =>  array(
                    "label" => "Exclusivement salarié",
                    "total" => 0
                ),
                "liberal" =>  array(
                    "label" => "Exclusivement libéral",
                    "total" => 0
                ),
            );

            foreach ($counts as $currentCount) {
                if ($currentCount["exercisesMode"]) {
                    $total = (int) $currentCount["total"];
                    $isLiberal = strpos($currentCount["exercisesMode"], 'Libéral') !== false;
                    $isSalarie = strpos($currentCount["exercisesMode"], 'Salarié') !== false;
                    $mode = null;
                    if ($isLiberal && $isSalarie) {
                        $mode = "mixte";
                    } else if ($isLiberal && !$isSalarie) {
                        $mode = "liberal";
                    } else if ($isSalarie && !$isLiberal) {
                        $mode = "salarie";
                    }
                    $exercises[$mode]["total"] += $total;
                }
            }

            $result[$y] = $exercises;
        }

        return $result;
    }

    public function getCategoryReport(ParticipantSearch $search, array $years)
    {
        $repo = $this->entityManager->getRepository(Participant::class);

        $months = array_map(function($n) {
            return str_pad((string) $n, 2, 0, STR_PAD_LEFT);
        }, range(1, 12));

        $categories = array_values(ProgrammeType::getCategories());

        $result = array();

        foreach ($categories as $category) {
            foreach ($months as $month) {
                foreach ($years as $y) {
                    $result[$category][$month][$y] = array(
                        "total"   => 0,
                        "color" => "green",
                    );
                    if (((int) $month) % 3 === 0) {
                        $trimester = "T" . ceil($month / 3);
                        $result[$category][$trimester][$y] = array(
                            "total"   => 0,
                            "color" => "red",
                        );
                    }
                    if (((int) $month) % 6 === 0) {
                        $semester = "S" . ceil($month / 6);

                        $result[$category][$semester][$y] = array(
                            "total"   => 0,
                            "color" => "blue",
                        );
                    }
                    if ($month === "12") {
                        $result[$category]["total"][$y] = array(
                            "total"   => 0,
                            "color" => "purple",
                        );
                    }
                }
            }
        }

        foreach ($years as $y) {

            $counts = $repo->countByCategory($search, $y);

            foreach ($counts as $count) {
                if (isset($result[$count["category"]][$count["sMonth"]][$y])) {
                    $month = (int) $count["sMonth"];
                    $semester = "S" . ceil($month / 6);
                    $trimester = "T" . ceil($month / 3);
                    $total = $count["total"];
                    $result[$count["category"]][$count["sMonth"]][$y]["total"] += $total;
                    $result[$count["category"]][$semester][$y]["total"] += $total;
                    $result[$count["category"]][$trimester][$y]["total"] += $total;
                    $result[$count["category"]]["total"][$y]["total"] += $total;
                }
            }
        }

        foreach ($result as &$categories) {
            foreach ($categories as &$months) {
                $y1 = array_keys($months)[0];
                $y2 = array_keys($months)[1];
                $months["diff"]  = array(
                    "total" => $months[$y2]["total"] > 0 ? (round(($months[$y1]["total"] - $months[$y2]["total"]) / $months[$y2]["total"] * 100, 2) . "%") : "NA",
                    "color" => "green",
                );
            }
        }

        ksort($result);
        
        return $result;
    }

    public function getProspectCategory()
    {
        $repo = $this->entityManager->getRepository(Participant::class);

        $categories = array_values(ProgrammeType::getCategories());

        $allKey = "Toutes professions confondues";
        $categories[] = $allKey;

        $result = array();

        $sortByTotal = function($a, $b) {
            return $b['total'] <=> $a['total'];
        };

        foreach ($categories as $category) {
            $result[$category] = array(
                "total"   => 0,
                "prospect"   => 0,
                "participant"   => 0,
                "percent" => 0,
            );
        }

        $counts = $repo->countByCategoryProspect();

        foreach ($counts as $count) {
            if (isset($result[$count["category"]])) {
                $key = (bool) ($count["isProspect"]) ? "prospect" : "participant";
                $result[$count["category"]][$key] += (int) $count["total"];
                $result[$allKey][$key] += (int) $count["total"];
            }
        }
        

        foreach ($result as $category => $values) {
            $result[$category]["total"] = $values["prospect"] + $values["participant"];
            $result[$category]["percent"] = $result[$category]["total"] > 0 ? (round($values["participant"] / $result[$category]["total"] * 100, 2) . "%") : "NA";
        }

        uasort($result, $sortByTotal);

        $all = $result[$allKey];
        unset($result[$allKey]);
        $result = array_slice($result, 0, 10);
        $result[$allKey] = $all;

        return $result;
    }

    public function getParticipationCategories(array $years)
    {
        $repo = $this->entityManager->getRepository(Participation::class);

        $categories = array_values(ProgrammeType::getCategories());
        $allKey = "Toutes professions confondues";
        $categories[] = $allKey;

        $result = array();

        foreach ($categories as $category) {
            foreach ($years as $y) {
                $result[$category][$y] = array(
                    "participation"      => 0,
                    "participant"        => 0,
                    "ratio"              => 0,
                    "participation_evol" => 0,
                    "participant_evol"   => 0,
                    "ratio_evol"         => 0,
                );
            }
        }

        foreach ($years as $y) {
            $counts = $repo->countByCategoryProspect($y);

            foreach ($counts as $count) {
                if (isset($result[$count["category"]])) {
                    $participation = (int)$count["participation"];
                    $participant = (int)$count["participant"];
                    $result[$count["category"]][$y]["participation"] += $participation;
                    $result[$allKey][$y]["participation"] += $participation;
                    $result[$count["category"]][$y]["participant"] += $participant;
                    $result[$allKey][$y]["participant"] += $participant;
                }
            }
        }
        
        foreach ($result as $category => $values) {
            foreach ($years as $year) {
                $participant = $result[$category][$year]["participant"];
                $participation = $result[$category][$year]["participation"];
                $result[$category][$year]["ratio"] = $participant > 0 ? round($participation / $participant, 2) : 0;
            }
            $year1 = $result[$category][$years[0]];
            $year2 = $result[$category][$years[1]];
            foreach (array("participation", "participant", "ratio") as $item) {
                $result[$category][$years[0]][$item . "_evol"] = $year2[$item] > 0 ? (round(($year1[$item] - $year2[$item]) / $year2[$item] * 100, 0) . "%") : "NA";
            }
        }

        $result = array_filter($result, function ($a) use ($years) {
            $a = $a[$years[0]];
            return $a["participation_evol"] !== "NA" || $a["participant_evol"] !== "NA" || $a["ratio_evol"] !== "NA";
        });
        
        return $result;
    }

    public function getCaCategories(array $years)
    {
        $repo = $this->entityManager->getRepository(Participation::class);

        $categories = array_values(ProgrammeType::getCategories());

        $result = array();

        $allKey = "Toutes professions confondues";
        $categories[] = $allKey;
        foreach ($categories as $category) {
            foreach ($years as $y) {
                $result[$category][$y] = 0;
            }
            $result[$category]["evol"] = "NA";
        }

        foreach ($years as $y) {
            $counts = $repo->countCaByCategory($y);
            foreach ($counts as $count) {
                if (isset($result[$count["category"]])) {
                    $total = (int) $count["total"];
                    $result[$count["category"]][$y] += $total;
                    $result[$allKey][$y] += $total;
                }
            }
        }

        foreach ($result as $category => $values) {
            $year1 = $result[$category][$years[0]];
            $year2 = $result[$category][$years[1]];
            $result[$category]["evol"] = $year2 > 0 ? (round(($year1 - $year2) / $year2 * 100, 0) . "%") : "NA";
        }

//        $result = array_filter($result, function ($a) use ($years) {
//            return $a[$years[0]] > 0 || $a[$years[1]] > 0;
//        });
        
        return $result;
    }

    public function getParticipationExercisesModes(array $years)
    {
        $repo = $this->entityManager->getRepository(Participation::class);

        $values = array();
        foreach ($years as $year) {
            $values[$year] = array(
                "participation"      => 0,
                "participant"        => 0,
                "ratio"              => 0,
                "participation_evol" => 0,
                "participant_evol"   => 0,
                "ratio_evol"         => 0,
            );
        }

        $result = array(
            "liberal" =>  array(
                "label" => "Libéral",
                "values" => $values
            ),
            "salarie" =>  array(
                "label" => "Salarié",
                "values" => $values
            ),
            "mixte" =>  array(
                "label" => "Libéral & Salarié",
                "values" => $values
            ),
            "all" =>  array(
                "label" => "Tous les modes confondus",
                "values" => $values
            ),
        );


        foreach ($years as $y) {
            $counts = $repo->countByExercisesMode($y);
            foreach ($counts as $count) {
                if ($count["exercisesMode"]) {
                    $isLiberal = strpos($count["exercisesMode"], 'Libéral') !== false;
                    $isSalarie = strpos($count["exercisesMode"], 'Salarié') !== false;
                    $mode = null;
                    if ($isLiberal && $isSalarie) {
                        $mode = "mixte";
                    } else if ($isLiberal && !$isSalarie) {
                        $mode = "liberal";
                    } else if ($isSalarie && !$isLiberal) {
                        $mode = "salarie";
                    }
                    $participation = (int) $count["participation"];
                    $participant = (int) $count["participant"];
                    $result[$mode]["values"][$y]["participation"] += $participation;
                    $result["all"]["values"][$y]["participation"] += $participation;
                    $result[$mode]["values"][$y]["participant"] += $participant;
                    $result["all"]["values"][$y]["participant"] += $participant;
                }
            }
        }
        
        foreach ($result as $mode => $values) {
            foreach ($years as $year) {
                $participant = $result[$mode]["values"][$year]["participant"];
                $participation = $result[$mode]["values"][$year]["participation"];
                $result[$mode]["values"][$year]["ratio"] = $participant > 0 ? round($participation / $participant, 2) : 0;
            }
            $year1 = $result[$mode]["values"][$years[0]];
            $year2 = $result[$mode]["values"][$years[1]];
            foreach (array("participation", "participant", "ratio") as $item) {
                $result[$mode]["values"][$years[0]][$item . "_evol"] = $year2[$item] > 0 ? (round(($year1[$item] - $year2[$item]) / $year2[$item] * 100, 0) . "%") : "NA";
            }
        }

        return $result;
    }

    public function getParticipationMethods(array $years)
    {
        $repo = $this->entityManager->getRepository(Participation::class);

        $categories = array_values(ProgrammeType::getCategories());
        $methods = array_keys(Programme::METHODS);

        $result = array();

        $allKey = "Toutes professions confondues";
        $categories[] = $allKey;

        foreach ($categories as $category) {
            foreach ($methods as $method) {
                foreach (array_reverse($years) as $y) {
                    $result[$category][$method][$y] = array(
                        "total" => 0,
                        "percent" => 0
                    );
                }
            }
        }

        $totalYear = array();
        foreach ($years as $year) {
            foreach ($categories as $category) {
                $totalYear[$year][$category] = 0;
            }
        }
        
        foreach ($years as $y) {
            $counts = $repo->countByMethod($y);

            foreach ($counts as $count) {
                if (isset($result[$count["category"]])) {
                    $participation = (int)$count["participation"];
                    $result[$count["category"]][$count["method"]][$y]["total"] += $participation;
                    $result[$allKey][$count["method"]][$y]["total"] += $participation;
                    $totalYear[$y][$count["category"]] += $participation;
                    $totalYear[$y][$allKey] += $participation;
                }
            }
        }
        foreach ($result as $category => $methods) {
            foreach ($methods as $method => $values) {
                foreach ($values as $year => $value) {
                    $result[$category][$method][$year]["percent"] = $totalYear[$year][$category] > 0 ?(round(($result[$category][$method][$year]["total"] / $totalYear[$year][$category]) * 100, 2) . "%") : "NA";
                }
            }
        }

        return $result;
    }

    public function getParticipationFormats(array $years)
    {
        $repo = $this->entityManager->getRepository(Participation::class);

        $categories = array_values(ProgrammeType::getCategories());

        $formats = array(Programme::FORMAT_MIXTE, Programme::FORMAT_ELEARNING, Programme::FORMAT_PRESENTIEL);

        $result = array();

        $allKey = "Toutes professions confondues";
        $categories[] = $allKey;

        foreach ($categories as $category) {
            foreach ($formats as $format) {
                foreach (array_reverse($years) as $y) {
                    $result[$category][$format][$y] = array(
                        "total" => 0,
                        "percent" => 0
                    );
                }
            }
        }

        $totalYear = array();
        foreach ($years as $year) {
            foreach ($categories as $category) {
                $totalYear[$year][$category] = 0;
            }
        }

        foreach ($years as $y) {
            $counts = $repo->countByFormat($y);

            foreach ($counts as $count) {
                if (isset($result[$count["category"]])) {
                    $durationNotPresentielleTotal = ($count["durationNotPresentielle"] ? $count["durationNotPresentielle"] : 0) + ($count["durationNotPresentielleActalians"] ? $count["durationNotPresentielleActalians"] : 0);
                    $durationPresentielle = $count["durationPresentielle"] ? $count["durationPresentielle"] : 0;
                    $format = Programme::FORMAT_MIXTE;
                    $format = $durationNotPresentielleTotal > 0 ? Programme::FORMAT_ELEARNING : $format;
                    $format = $durationPresentielle > 0 ? Programme::FORMAT_PRESENTIEL : $format;
                    $format = $durationNotPresentielleTotal > 0 && $durationPresentielle > 0 ? Programme::FORMAT_MIXTE : $format;
                    $participation = (int)$count["participation"];
                    $result[$count["category"]][$format][$y]["total"] += $participation;
                    $result[$allKey][$format][$y]["total"] += $participation;
                    $totalYear[$y][$count["category"]] += $participation;
                    $totalYear[$y][$allKey] += $participation;
                }
            }
        }
        
        foreach ($result as $category => $formats) {
            foreach ($formats as $format => $values) {
                foreach ($values as $year => $value) {
                    $result[$category][$format][$year]["percent"] = $totalYear[$year][$category] > 0 ?(round(($result[$category][$format][$year]["total"] / $totalYear[$year][$category]) * 100, 2) . "%") : "NA";
                }
            }
        }
        
        return $result;
    }

    public function getParticipationActionCategories(array $years)
    {
        $repo = $this->entityManager->getRepository(Participation::class);

        $categories = array_values(ProgrammeType::getCategories());

        $formats = array("Pluriprofessionnelle", "Monoprofessionnelle");

        $result = array();

        $allKey = "Toutes professions confondues";
        $categories[] = $allKey;

        foreach ($categories as $category) {
            foreach ($formats as $format) {
                foreach (array_reverse($years) as $y) {
                    $result[$category][$format][$y] = array(
                        "total" => 0,
                        "percent" => 0
                    );
                }
            }
        }

        $totalYear = array();
        foreach ($years as $year) {
            foreach ($categories as $category) {
                $totalYear[$year][$category] = 0;
            }
        }

        foreach ($years as $y) {
            $counts = $repo->countByProgrammeCategories($y);

            foreach ($counts as $count) {
                if (isset($result[$count["category"]])) {
                    $categories = unserialize($count["categories"]);
                    if ($categories) {
                        $format = count($categories) > 1 ? "Pluriprofessionnelle" : "Monoprofessionnelle";
                        $participation = (int)$count["participation"];
                        $result[$count["category"]][$format][$y]["total"] += $participation;
                        $result[$allKey][$format][$y]["total"] += $participation;
                        $totalYear[$y][$count["category"]] += $participation;
                        $totalYear[$y][$allKey] += $participation;
                    }
                }
            }
        }

        foreach ($result as $category => $formats) {
            foreach ($formats as $format => $values) {
                foreach ($values as $year => $value) {
                    $result[$category][$format][$year]["percent"] = $totalYear[$year][$category] > 0 ?(round(($result[$category][$format][$year]["total"] / $totalYear[$year][$category]) * 100, 2) . "%") : "NA";
                }
            }
        }
        
        return $result;
    }

}
