<?php

namespace Eduprat\CrmBundle\Controller;

use Alienor\ApiBundle\Services\FlashMessages;
use DateTime;
use Doctrine\ORM\EntityManagerInterface;
use Eduprat\AdminBundle\Command\GenerateCSVParticipantAnalysisCommand;
use Eduprat\AdminBundle\Services\UserManager;
use Doctrine\DBAL\Exception\ForeignKeyConstraintViolationException;
use Eduprat\AdminBundle\Command\GenerateCSVParticipantCommand;
use Eduprat\AdminBundle\Entity\ParticipantSearch;
use Eduprat\AdminBundle\Entity\Person;
use Eduprat\AdminBundle\Form\ChangePasswordType;
use Eduprat\AdminBundle\Http\CsvFileResponse;
use Eduprat\AdminBundle\Services\CsvBilanExport;
use Eduprat\AdminBundle\Services\Regions;
use Eduprat\DomainBundle\Controller\EdupratController;
use Eduprat\DomainBundle\Entity\EvaluationGlobalAnswer;
use Eduprat\DomainBundle\Entity\LeadHistory;
use Eduprat\DomainBundle\Entity\Participant;
use Eduprat\DomainBundle\Entity\Participation;
use Eduprat\DomainBundle\Form\ParticipantType;
use Eduprat\DomainBundle\Repository\ParticipantRepository;
use Eduprat\DomainBundle\Services\AdressesService;
use Eduprat\DomainBundle\Services\SearchHandler;
use Symfony\Component\Security\Http\Attribute\IsGranted;
use Symfony\Component\Form\FormInterface;
use Symfony\Component\HttpFoundation\BinaryFileResponse;
use Symfony\Component\HttpFoundation\JsonResponse;
use Symfony\Component\HttpFoundation\RedirectResponse;
use Symfony\Component\HttpFoundation\Request;
use Symfony\Component\Routing\Attribute\Route;
use Symfony\Component\HttpFoundation\ResponseHeaderBag;
use Symfony\Component\HttpKernel\Exception\NotFoundHttpException;
use Symfony\Component\Process\Process;
use Symfony\Component\HttpFoundation\Response;
use Eduprat\DomainBundle\Entity\ParticipantDateDownload;
use Eduprat\DomainBundle\Services\EmailSender;
use Eduprat\DomainBundle\Services\ParticipationLogger;
use Symfony\Component\Asset\Packages;
use Symfony\Component\Form\Extension\Core\Type\FormType;
use Symfony\Component\Form\FormFactoryInterface;
use Symfony\Component\Form\FormBuilderInterface;
use Symfony\Component\Validator\Constraints\File;
use Symfony\Component\PropertyAccess\PropertyAccess;
use Symfony\Contracts\Translation\TranslatorInterface;
use Vich\UploaderBundle\Form\Type\VichFileType;
use Vich\UploaderBundle\Templating\Helper\UploaderHelper;

/**
 * Participant controller.
 */
#[Route(path: '/participant')]
#[IsGranted('ROLE_COORDINATOR_LBI')]
class ParticipantController extends EdupratController
{

    /**
     * @var UserManager
     */
    public $userManager;

    private ParticipationLogger $participationLogger;

    public function __construct(FlashMessages $flashMessages, SearchHandler $searchHandler, UserManager $userManager, TranslatorInterface $translator, Packages $packages, UploaderHelper $uploaderHelper,  ParticipationLogger $participationLogger)
    {
        $this->userManager = $userManager;
        
        parent::__construct($flashMessages, $searchHandler, $translator, $packages, $uploaderHelper);
        $this->participationLogger = $participationLogger;
    }

    
    /**
     * Lists all Participant entities.
     *
     * @param Request $request
     * @return Response
     */
    #[Route(path: '/liste/{page}', methods: ['GET', 'POST'], defaults: ['page' => '1'], name: 'admin_participant_index', requirements: ['page' => '^\d+$'])]
    #[IsGranted('ROLE_COORDINATOR_LBI')]
    public function index(Request $request, string $page, Regions $regionsService, EntityManagerInterface $em, SearchHandler $searchHandler)
    {
        /** @var ParticipantRepository $participantRepositoty */
        $participantRepository = $em->getRepository(Participant::class);

        $zipSearch = array();
        if($this->getUser()->isCoordinator()) {
            $zipSearch['coordinatorId'] = $this->getUser()->getId();
        }
        else if($this->getUser()->isSupervisor()) {
            $zipSearch['supervisorId'] = $this->getUser()->getId();
        }
        
        $result = $participantRepository->getZipCodes($zipSearch);

        $zipCodes = array_column($result, "zipCode");

        $regions = $regionsService->getRegions($zipCodes);

        $displayCoordinatorFilter = false;
        if($this->getUser()->isSupervisorFr() || $this->getUser()->isSupervisor() || $this->getUser()->isWebmaster() || in_array('ROLE_SUPER_ADMIN', $this->getUser()->getRoles())) {
            $displayCoordinatorFilter = true;
        }

        $type = $request->query->has("sendinblue") ? "sib" : "default";
        $searchHandle = $searchHandler->handle(ParticipantSearch::class, array(
            'displayCoordinatorFilter' => $displayCoordinatorFilter,
            'regions' => $regions,
            'type' => $type
        ));

        if ($searchHandle["redirect"]) {
            return $this->redirect($searchHandle["redirect"]);
        }

        /** @var ParticipantSearch $search */
        $search = $searchHandle["search"];
        $search->setUserFilters($this->getUser());

        $search->regionZipCodes = $regionsService->getDepsByRegion($search->region);

        $nbPerPage = 30;
        $max = 10;
        $count = $participantRepository->countSearchResults($search);
        $participants = $participantRepository->findSearchResults($search, $page, $nbPerPage);

        $npages = ceil($count / $nbPerPage);
        $current = intval($page);
        $inf = max(1, intval($current - (($max == 1 ? 0 : $max-1) / 2)));
        $sup = min($inf + ($max-1), $npages);
        $pageRange = range($inf, $sup);
        $pagination = array(
            'nb' => $count,
            'page' => $page,
            'npages' => $npages,
            'page_range' => $pageRange,
            'participants' => $participants,
        );

        $sib = $request->query->get("sendinblue") !== null ? true : false;
        return $this->render('crm/participant/index.html.twig', array_merge($pagination, array(
            'form' => $searchHandle["searchForm"]->createView(),
            'search' => $search,
            'mergeUrl' => $this->generateUrl('admin_participant_fusion', array("participant1" => "__1__", "participant2" => "__2__")),
            'hasGenerated' => GenerateCSVParticipantCommand::hasGeneratedFile($this->getUser()),
            'hasFinished' => GenerateCSVParticipantCommand::generateFileIsFinished($this->getUser()),
            'hasError' => GenerateCSVParticipantCommand::hasGeneratedFileError($this->getUser()),
            'sib' => $sib
        )));
    }
    
    /**
     * @param Request $request
     * @return RedirectResponse|Response
     */
    #[Route(path: '/create', name: 'admin_participant_create')]
    public function create(Request $request, EntityManagerInterface $entityManager, EmailSender $emailSender) {
        $participant = new Participant();
        $form = $this->createForm(ParticipantType::class, $participant);

        $form->handleRequest($request);

        if ($form->isSubmitted() && $form->isValid()) {
            if ($participant->canCreateAccount()) {
                $user = $this->userManager->createFromParticipant($participant);
                $user->setParticipant($participant);
                $entityManager->persist($user);
                $participant->setUser($user);
            }
            
            if($participant->isLeadOn()) {
                $participant->setLeadCreationDate(new DateTime('now'));
                if($participant->getLeadReferent()) {
                    $emailSender->sendNewLead($participant);
                }
            }

            $entityManager->persist($participant);
            $entityManager->flush();

            $this->flashMessages->addSuccess('participant.create.success');
            return $this->redirectToRoute('admin_participant_show', array("id" => $participant->getId()));
        }

        return $this->render('crm/participant/edit.html.twig', array(
            'form' => $form,
            'edit' => false
        ));
    }

    /**
     * show an existing Participant entity.
     *
     * @param Request     $request
     * @param Participant $participant
     * @return RedirectResponse|Response
     */
    #[Route(path: '/{id}/show', methods: ['GET', 'POST'], name: 'admin_participant_show')]
    public function show(Request $request, EntityManagerInterface $em, Participant $participant, FormFactoryInterface $formFactory): Response
    {
        $this->denyAccessUnlessGranted('view', $participant);
        $participations = $participant->getParticipations();
        /** @var Person $user */
        $user = $this->getUser();
        if ($user->isCoordinator()) {
            $participations = $participations->filter(function(Participation $participation) use ($user) {
                if (count($participation->getFormation()->getCoordinators()) > 1) {
                    return $participation->getCoordinator() && $participation->getCoordinator()->getPerson() === $user;
                }
                return $participation->getFormation()->getCoordinators()->first() && $participation->getFormation()->getCoordinators()->first()->getPerson() === $user;
            });
        } else if ($user->isPharmacieFormer()) {
            $participations = $participations->filter(function(Participation $participation) use ($user) {
                return in_array($user->getId(), $participation->getFormation()->getProgramme()->getFormateursPersons()->map(function(Person $u) {
                    return $u->getId();
                })->toArray());
            });
        }

        $participantDateDownloadRepo = $em->getRepository(ParticipantDateDownload::class);
        $participantDateDownload = $participantDateDownloadRepo->findBy(array(
            'participant' => $participant->getId()
        ));
        
        $dates = array();

        foreach ($participantDateDownload as $value) {
            $dates[$value->getFormation()->getId()] = array(
                'participation' => $value->getParticipationDownloadedAt(),
                'restitution' => $value->getRestitutionDownloadedAt(),
                'topos' => $value->getToposDownloadedAt()
            );
        }

        $forms_facture_attestation_views = [];
        foreach($participant->getParticipations() as $participation) {
            if (!$participation->getFormation()->getProgramme()->isFormatPresentiel()) {
                $form_facture_attestation_views = $this->drawUploadAttestationForm($request, 'attestationHonneurFile', $participation, $formFactory);

                if ($form_facture_attestation_views->isSubmitted() && $form_facture_attestation_views->isValid()) {
                    $em->persist($participation);
                    $em->flush();

                    if ($participation->getAttestationHonneur()) {
                        $this->flashMessages->addSuccess('admin.participation.attestation_honneur.importSucess');
                    } else {
                        $this->flashMessages->addError('admin.participation.attestation_honneur.importError');
                    }
                }

                $forms_facture_attestation_views[$participation->getId()] = $form_facture_attestation_views->createView();
            }
        }
        

        return $this->render('crm/participant/show.html.twig', array(
            'participant' => $participant,
            'forms_attestation_honneur' => $forms_facture_attestation_views,
            'participations' => $participations,
            'dates' => $dates
        ));
    }

    /**
     * Displays a form to edit an existing Participant entity.
     *
     * @param Request     $request
     * @param Participant $participant
     * @return Response
     */
    #[Route(path: '/{id}/edit', methods: ['GET', 'POST'], name: 'admin_participant_edit')]
    #[IsGranted('ROLE_COORDINATOR')]
    public function edit(Request $request, EntityManagerInterface $entityManager, Participant $participant, UserManager $userManager, EmailSender $emailSender)
    {
        $allowed = true;
        $oldParticipant = clone $participant;
        if ($this->getUser()->isSupervisor() || $this->getUser()->isCoordinator()) {
            $search = new ParticipantSearch();
            $search->setUserFilters($this->getUser());
            $search->id = $participant->getId();
            if(!$entityManager->getRepository(Participant::class)->findSearchAllow($search)) {
                $allowed = false;
            }
        }

        if($allowed) {
            $form = $this->createForm(ParticipantType::class, $participant);
            $form->handleRequest($request);
    
            if ($form->isSubmitted() &&  $form->isValid()) {
                if ($participant->canCreateAccount()) {
                    $user = $this->userManager->createFromParticipant($participant);
                    $user->setParticipant($participant);
                    $entityManager->persist($user);
                    $participant->setUser($user);
                }

                // Si le participant devient Lead
                $isNewLead = false;
                if($oldParticipant->getLeadStatus() == null && $participant->getLeadStatus() != null) {
                    $participant->setLeadCreationDate(new DateTime('now'));
                    $participant->setExerciceMode($participant->getLastParticipation() &&  !$participant->isProspect() ? $participant->getLastParticipation()->getExerciseMode() : null);
                    $isNewLead = true;
                }
         

                if($isNewLead && $participant->getLeadReferent()) {
                    $emailSender->sendNewLead($participant);
                }

                $entityManager->persist($participant);
                $entityManager->flush();
    
                $this->flashMessages->addSuccess('participant.edit.success');
                return $this->redirectToRoute('admin_participant_show', array("id" => $participant->getId()));
            }
    
            $viewVars = array(
                'participant' => $participant,
                'form' => $form->createView(),
                'edit' => true
            );
    
            $user = $participant->getUser();
    
            if (!is_null($user)) {
                $formPass = $this->createForm(ChangePasswordType::class, $user, array(
                    'validation_groups' => array('password_edit'),
                ));
    
                $formPass->handleRequest($request);
    
                if ($formPass->isSubmitted() &&  $formPass->isValid()) {
                    $user->setHasCreatedPassword(true);
                    $userManager->updateUser($user);
                    $this->flashMessages->addSuccess('participant.edit.success');
                    return $this->redirectToRoute('admin_participant_index');
                }
                $viewVars = array_merge($viewVars, array('formPass' => $formPass->createView()));
            }
    
            return $this->render('crm/participant/edit.html.twig', $viewVars);
        }
        else {
            $this->flashMessages->addError('participant.edit.error');
            return $this->redirectToRoute('admin_participant_index');
        }
    }

    /**
     * Deletes a Participant entity.
     *
     * @param Request $request
     * @param Participant $participant
     * @return RedirectResponse|Response
     */
    #[Route(path: '/{id}/delete', methods: ['GET', 'POST'], name: 'admin_participant_delete')]
    #[IsGranted('ROLE_COORDINATOR_LBI')]
    public function delete(Request $request, EntityManagerInterface $em, Participant $participant)
    {
        if ($this->getUser()->isSupervisor() || $this->getUser()->isCoordinator()) {
            $search = new ParticipantSearch();
            $search->setUserFilters($this->getUser());
            $search->id = $participant->getId();
            if(!$em->getRepository(Participant::class)->findSearchAllow($search)) {
                return $this->createAccessDeniedException();
            }
        }

        $form = $this->createDeleteForm($participant);
        $form->handleRequest($request);

        if ($form->isSubmitted() && $form->isValid()) {
            try {
                $em->remove($participant);
                $em->flush();
            } catch (ForeignKeyConstraintViolationException $e) {
                $this->flashMessages->addError('participant.delete.constraint_error');
                return $this->redirectToRoute('admin_participant_index');
            }
            $this->flashMessages->addSuccess('participant.delete.success');
            return $this->redirectToRoute('admin_participant_index');
        }

        return $this->render('crm/participant/delete.html.twig', array(
            'participant' => $participant,
            'form' => $form,
        ));
    }

    /**
     * Lists all Participant entities.
     *
     * @param Request $request
     * @return Response
     */
    #[Route(path: '/analyse/{page}', methods: ['GET', 'POST'], defaults: ['page' => '1'], name: 'crm_participant_analyse', requirements: ['page' => '^\d+$'])]
    #[IsGranted('ROLE_COORDINATOR_LBI')]
    public function analyse(Request $request, EntityManagerInterface $em, SearchHandler $searchHandler, string $page)
    {
        $sortBy = $request->query->get('sortBy');
        $order = $request->query->get('order');

        /** @var ParticipantRepository $participantRepositoty */
        $participantRepository = $em->getRepository(Participant::class);

        $displayCoordinatorFilter = false;
        if($this->getUser()->isSupervisorFr() || $this->getUser()->isSupervisor() || $this->getUser()->isWebmaster() || in_array('ROLE_SUPER_ADMIN', $this->getUser()->getRoles())) {
            $displayCoordinatorFilter = true;
        }

        $searchHandle = $searchHandler->handle(ParticipantSearch::class, array(
            'displayCoordinatorFilter' => $displayCoordinatorFilter,
            'regions' => array(),
            'type' => "default"
        ));

        if ($searchHandle["redirect"]) {
            return $this->redirect($searchHandle["redirect"]);
        }

        /** @var ParticipantSearch $search */
        $search = $searchHandle["search"];
        $search->setUserFilters($this->getUser());
        $search->isProspect = "false";
        $search->isActif = "true";

        $nbPerPage = 50;
        $max = 10;
        $count = $participantRepository->countSearchResults($search);
        $persons = $participantRepository->findSearchResults($search, $page, $nbPerPage, $sortBy, $order);

        $npages = ceil($count / $nbPerPage);
        $current = intval($page);
        $inf = max(1, intval($current - (($max == 1 ? 0 : $max-1) / 2)));
        $sup = min($inf + ($max-1), $npages);
        $pageRange = range($inf, $sup);
        $pagination = array(
            'nb' => $count,
            'page' => $page,
            'npages' => $npages,
            'page_range' => $pageRange,
            'participants' => $persons,
        );


        return $this->render('crm/participant/analyse.html.twig', array_merge($pagination, array(
            'form' => $searchHandle["searchForm"]->createView(),
            'search' => $search,
            'extraParams' => array_merge($search->toArray(), array(
                'sortBy' => $sortBy,
                'order' => $order,
            )),
            'hasGenerated' => GenerateCSVParticipantAnalysisCommand::hasGeneratedFile($this->getUser()),
            'hasFinished' => GenerateCSVParticipantAnalysisCommand::generateFileIsFinished($this->getUser()),
            'hasError' => GenerateCSVParticipantAnalysisCommand::hasGeneratedFileError($this->getUser()),
        )));
    }

    /**
     * Merge two Participant entity.
     */
    #[Route(path: '/{participant1}/fusion/{participant2}', methods: ['GET', 'POST'], name: 'admin_participant_fusion')]
    #[IsGranted('ROLE_WEBMASTER')]
    public function fusion(Request $request, EntityManagerInterface $em, Participant $participant1, Participant $participant2)
    {
        $psAndProspectMerge = false;
        if ($participant1->isProspect() && !$participant2->isProspect()) {
            $tempPs1 = $participant1;
            $participant1 = $participant2;
            $participant2 = $tempPs1;
            $psAndProspectMerge = true;
        } elseif (!$participant1->isProspect() && $participant2->isProspect()) {
            $psAndProspectMerge = true;
        }

        $form = $this->createFormBuilder()
            ->setAction($this->generateUrl('admin_participant_fusion', array(
                'participant1' => $participant1->getId(),
                'participant2' => $participant2->getId(),
            )))
            ->setMethod(Request::METHOD_POST)
            ->getForm()
        ;

         $this->mergeActualLeadState($em, $participant1, $participant2);

        $attributes = array(
            "civility",
            "lastname",
            "firstname",
            "birthname",
            "address",
            "zipCode",
            "city",
            "phone",
            "email",
            "rpps",
            "adeli",
            "category",
            "speciality",
            "uga",
            "isProspect",
            "leadStatus",
            "leadType",
            "advisor",
            "leadCreationDate",
            "leadReferent",
            "leadContactDate",
            "leadComment",
            "leadCommentEduprat",
            "leadState"
        );

        $added = array();
        $edited = array();
        $participations = array();
        $leadHistories = array();

        $propertyAccessor = PropertyAccess::createPropertyAccessor();

        if ($psAndProspectMerge) {
            foreach ($attributes as $attribute) {
                $oldValue = $propertyAccessor->getValue($participant1, $attribute);
                $newValue = $propertyAccessor->getValue($participant2, $attribute);
                if ($oldValue !== null && $oldValue !== "") {
                    $propertyAccessor->setValue($participant2, $attribute, $oldValue);
                    $added[] = $attribute;
                } else if ($oldValue !== $newValue) {
                    if ($oldValue instanceof \DateTime && $newValue instanceof \DateTime) {
                        if ($oldValue->format('Y-m-d') !== $newValue->format('Y-m-d')) {
                            $edited[] = $attribute;
                        }
                    } else {
                        $edited[] = $attribute;
                    }
                }
            }
            if ($participant2->isLeadOn()) {
                foreach ($participant1->getParticipations() as $participation) {
                    if ($participation->getCreatedAt()->format('Y-m-d') >= $participant2->getLeadCreationDate()->format('Y-m-d')) {
                        $participation->setIsLead(true);
                    }
                }
            }
        }

        foreach ($attributes as $attribute) {
            $oldValue = $propertyAccessor->getValue($participant1, $attribute);
            $newValue = $propertyAccessor->getValue($participant2, $attribute);
            if ($newValue === null || $newValue === "") {
                $propertyAccessor->setValue($participant2, $attribute, $oldValue);
                $added[] = $attribute;
            } else if ($oldValue !== $newValue) {
                if ($oldValue instanceof \DateTime && $newValue instanceof \DateTime) {
                    if ($oldValue->format('Y-m-d') !== $newValue->format('Y-m-d')) {
                        $edited[] = $attribute;
                    }
                } else {
                    $edited[] = $attribute;
                }
            }
        }

        $oldParticipations = clone $participant1->getParticipations();

        /** @var Participation $participation */
        foreach ($participant1->getParticipations() as $participation) {
            $participant2->addParticipation($participation);
            $participant1->removeParticipation($participation);
            $participation->setParticipant($participant2);
            $participations[] = $participation->getId();
        }

        /** @var ParticipantDateDownload $date */
        foreach ($participant1->getDownloadDates() as $date) {
            $participant2->addDownloadDates($date);
            $participant1->removeDownloadDates($date);
            $date->setParticipant($participant2);
        }

        /** @var LeadHistory $leadHistory */
        foreach ($participant1->getLeadHistories() as $leadHistory) {
            $participant2->addLeadHistory($leadHistory);
            $participant1->removeLeadHistory($leadHistory);
            $leadHistory->setParticipant($participant2);
            $leadHistories[] = $leadHistory->getId();
        }

        if ($participant1->getUser()) {
            /** @var EvaluationGlobalAnswer $answer */
            $evaluationGlobalAnswers = $participant1->getUser()->getEvaluationGlobalAnswers();
        } else {
            $evaluationGlobalAnswers = array();
        }
        
        $accountToRemove = null;

        if (is_null($participant2->getUser()) || !$participant2->getUser()->isHasCreatedPassword()) {
            if ($participant2->getUser()) {
                $accountToRemove = $participant2->getUser();
            }
            $participant2->setUser($participant1->getUser());
            if ($participant2->getUser()) {
                $participant2->getUser()->setParticipant($participant2);
            }
            $participant1->setUser(null);
        }

        foreach ($evaluationGlobalAnswers as $answer) {
            $answer->setPerson($participant2->getuser());
        }

        $form->handleRequest($request);

        if ($form->isSubmitted() && $form->isValid()) {
            $em->persist($participant2);
            if ($participant2->getUser()) {
                $em->persist($participant2->getUser());
            }
            foreach ($participant2->getParticipations() as $participation) {
                $em->persist($participation);
            }
            foreach ($participant2->getLeadHistories() as $leadHistory) {
                $em->persist($leadHistory);
            }
            foreach ($participant2->getDownloadDates() as $date) {
                $em->persist($date);
            }
            if ($participant2->getUser()) {
                foreach ($participant2->getUser()->getEvaluationGlobalAnswers() as $answer) {
                    $em->persist($answer);
                }
            }
            if ($accountToRemove) {
                foreach ($accountToRemove->getEvaluationGlobalAnswers() as $answer) {
                    $answer->setPerson($participant2->getUser());
                    $em->persist($answer);
                }
                $em->remove($accountToRemove);
            }
            $em->flush();
            $em->remove($participant1);
            $em->flush();
            $this->flashMessages->addSuccess('participant.merge.success');
            return $this->redirectToRoute('admin_participant_index');
        }

        return $this->render('crm/participant/fusion.html.twig', array(
            'participant1' => $participant1,
            'participant2' => $participant2,
            'participations' => $participations,
            'oldParticipations' => $oldParticipations,
            'added' => $added,
            'edited' => $edited,
            'accountToRemove' => $accountToRemove,
            'form' => $form,
        ));
    }

    public function mergeActualLeadState(EntityManagerInterface $em, Participant $participant1, Participant $participant2) {
        if ($participant1->getLeadCreationDate() && $participant2->getLeadCreationDate()) {
            $newLeadHistory = $participant1->getLeadCreationDate() > $participant2->getLeadCreationDate() ?  new LeadHistory($participant2) : new LeadHistory($participant1);
            $em->persist($newLeadHistory);
            if ($participant1->getLeadCreationDate() < $participant2->getLeadCreationDate()) {
                $participant1->setLeadStatus($participant2->getLeadStatus());
                $participant1->setLeadType($participant2->getLeadType());
                $participant1->setAdvisor($participant2->getAdvisor());
                $participant1->setLeadContactDate($participant2->getLeadContactDate());
                $participant1->setLeadComment($participant2->getLeadComment());
                $participant1->setLeadCommentEduprat($participant2->getLeadCommentEduprat());
                $participant1->setLeadReferent($participant2->getLeadReferent());
                $participant1->setLeadState($participant2->getLeadState());
                $participant1->setLeadCreationDate($participant2->getLeadCreationDate());
            } else {
                $participant2->setLeadStatus($participant1->getLeadStatus());
                $participant2->setLeadType($participant1->getLeadType());
                $participant2->setAdvisor($participant1->getAdvisor());
                $participant2->setLeadContactDate($participant1->getLeadContactDate());
                $participant2->setLeadComment($participant1->getLeadComment());
                $participant2->setLeadCommentEduprat($participant1->getLeadCommentEduprat());
                $participant2->setLeadReferent($participant1->getLeadReferent());
                $participant2->setLeadState($participant1->getLeadState());
                $participant2->setLeadCreationDate($participant1->getLeadCreationDate());
            }
        }
    }

    /**
     * @param Participant $participant
     * @param CsvBilanExport $csvExport
     * @return RedirectResponse|Response
     */
    #[Route(path: '/{id}/export-email-history', methods: ['GET'], name: 'admin_participant_export_emails')]
    #[IsGranted('ROLE_COORDINATOR')]
    public function exportMailerHistory(Participant $participant, CsvBilanExport $csvExport): CsvFileResponse
    {
        $data = $csvExport->exportMailHistoryParticipant($participant);
        return new CsvFileResponse($data, sprintf("export_emails_%s.csv", $participant->getUser()->getEmail()));
    }

    /**
     * @param Request $request
     * @param Participant $participant
     * @return RedirectResponse|Response
     */
    #[Route(path: '/{id}/export-participations-histories', methods: ['GET'], name: 'admin_formation_export_participations_histories')]
    #[IsGranted('ROLE_COORDINATOR')]
    public function exportParticipationHistories(Participant $participant, CsvBilanExport $csvExport): CsvFileResponse
    {
        $data = $csvExport->exportParticipationHistories($participant);
        return new CsvFileResponse($data, sprintf("export_histories_participations_%s.csv", $participant->getId()));
    }

    #[Route(path: '/participant-generate-csv-export', methods: ['POST'], name: 'participant_generate_csv_export')]
    public function participantGenerateCsvExport(Request $request, SearchHandler $searchHandler): JsonResponse
    {
        $search = new ParticipantSearch();
        $searchHandler->handleArray($search, $request->request->all('eduprat_participant_search'));

        $projectDir = $this->getParameter('kernel.project_dir');
        $command = "eduprat:csv_participant";
        $json = str_replace("\"", "\\\"", json_encode($search->getParams()));
        $cmd = sprintf("php %s/bin/console %s %s \"%s\"", $projectDir, $command, $this->getUser()->getId(), $json);
        $cmd = sprintf("%s --env=%s >/dev/null 2>&1 &", $cmd, $this->getParameter('kernel.environment'));

        $process = Process::fromShellCommandline($cmd);
        $process->run();
        if (!$process->isSuccessful()) {
            throw new \RuntimeException($process->getErrorOutput());
        }

        $pid = $process->getOutput();

        return new JsonResponse(array("status" => "ok", "pid" => $pid));
    }

    /**
     * @param $type
     * @param Request $request
     * @return JsonResponse
     */
    #[Route(path: '/participant-generate-csv-export-sib', methods: ['POST'], name: 'participant_generate_csv_export_sendinblue')]
    public function participantGenerateCsvExportSendinblue(Request $request, SearchHandler $searchHandler): JsonResponse
    {
        $search = new ParticipantSearch();
        $searchHandler->handleArray($search, $request->request->all('eduprat_participant_search'));

        $command = sprintf("eduprat:csv_participant_sib %s '%s'", $this->getUser()->getId(), json_encode($search->getParams()));

        $projectDir = $this->getParameter('kernel.project_dir');
        $cmd = sprintf("php %s/bin/console %s", $projectDir, $command);
        $cmd = sprintf("%s --env=%s >/dev/null 2>&1 &", $cmd, $this->getParameter('kernel.environment'));

        $process = Process::fromShellCommandline($cmd);
        $process->run();
        if (!$process->isSuccessful()) {
            throw new \RuntimeException($process->getErrorOutput());
        }

        $pid = $process->getOutput();

        return new JsonResponse(array("status" => "ok", "pid" => $pid));
    }

    /**
     * @param $type
     * @param Request $request
     * @return JsonResponse
     */
    #[Route(path: '/participant-generate-csv-export-analysis', methods: ['POST'], name: 'participant_generate_csv_export_analysis')]
    public function participantGenerateCsvExportAnalysis(Request $request, SearchHandler $searchHandler): JsonResponse
    {
        $search = new ParticipantSearch();

        $searchHandler->handleArray($search, $request->request->all('eduprat_participant_search'));

        $command = sprintf("eduprat:csv_participant_analysis %s '%s'", $this->getUser()->getId(), json_encode(array_merge(
            $search->getParams(),
            array(
                "sortBy" => $request->query->get("sortBy"),
                "order"  => $request->query->get("order"),
            )
        ), JSON_THROW_ON_ERROR));

        $projectDir = $this->getParameter('kernel.project_dir');
        $cmd = sprintf("php %s/bin/console %s", $projectDir, $command);
        $cmd = sprintf("%s --env=%s >/dev/null 2>&1 &", $cmd, $this->getParameter('kernel.environment'));

        $process = Process::fromShellCommandline($cmd);
        $process->run();
        if (!$process->isSuccessful()) {
            throw new \RuntimeException($process->getErrorOutput());
        }

        $pid = $process->getOutput();

        return new JsonResponse(array("status" => "ok", "pid" => $pid));
    }

    /**
     * @param Request $request
     * @return Response
     */
    #[Route(path: '/csv-participant-file-get', methods: ['GET'], name: 'csv_participant_file_get')]
    public function csvParticipantFileGet(): BinaryFileResponse
    {
        $file = GenerateCSVParticipantCommand::getGeneratedFilePath($this->getUser());
        if($file && file_exists($file) && filesize($file) > 0) {
            $response = new BinaryFileResponse($file);

            $response->setContentDisposition(
                ResponseHeaderBag::DISPOSITION_ATTACHMENT,
                basename($file));

            return $response;
        }
        else {
            throw new NotFoundHttpException();
        }
    }

    /**
     * @param Request $request
     * @return Response
     */
    #[Route(path: '/csv-participant-analysis-file-get', methods: ['GET'], name: 'csv_participant_analysis_file_get')]
    public function csvParticipantAnalysisFileGet(): BinaryFileResponse
    {
        $file = GenerateCSVParticipantAnalysisCommand::getGeneratedFilePath($this->getUser());
        if($file && file_exists($file) && filesize($file) > 0) {
            $response = new BinaryFileResponse($file);

            $response->setContentDisposition(
                ResponseHeaderBag::DISPOSITION_ATTACHMENT,
                basename($file));

            return $response;
        }
        else {
            throw new NotFoundHttpException();
        }
    }

    /**
     * @param Request $request
     * @param AdressesService $adressesService
     * @return RedirectResponse|Response
     */
    #[Route(path: '/process-uga', methods: ['GET'], name: 'admin_participant_process_uga')]
    public function processUga(Request $request, AdressesService $adressesService): JsonResponse
    {
        $uga = $adressesService->getAddressUGA($request->query->get("address"), $request->query->get("zipCode"), $request->query->get("city"));
        return new JsonResponse(array(
            "uga" => $uga
        ));
    }

    public function drawUploadAttestationForm(Request $request, $fieldName, $participation, $formFactory) {
        $genForm = $formFactory->createNamedBuilder($fieldName.'_'.$participation->getId(), FormType::class, $participation);
        $this->drawUploadFormField($genForm, $fieldName, false, "12M");
        $form = $genForm->getForm();
        $form->handleRequest($request);
        return $form;
    }

    public function drawUploadFormField(FormBuilderInterface $builder, $fieldName, $allow_delete = true, $maxSize="128M") {
        $builder->add($fieldName, VichFileType::class, array(
            'label' => false,
            'required' => false,
            'download_uri' => false,
            'allow_delete' => $allow_delete,
            'error_bubbling' => true,
            'constraints' => new File(
                array(
                    'mimeTypes' => array(
                        'image/png', 'image/jpeg', 'application/pdf', 'application/x-pdf'
                    ),
                    'mimeTypesMessage' => 'formation.mimeTypesAttestation',
                    'maxSize' => $maxSize
                )
            )
        ));
    }

    /**
     * Creates a form to delete a Participant entity.
     */
    private function createDeleteForm(Participant $participant): FormInterface
    {
        return $this->createFormBuilder()
            ->setAction($this->generateUrl('admin_participant_delete', array('id' => $participant->getId())))
            ->setMethod(Request::METHOD_POST)
            ->getForm()
            ;
    }

        /**
     * Displays a form to edit an existing commande entity.
     *
     * @param Request                            $request
     * @param Participant $participant
     * @return RedirectResponse|Response
     */
    #[Route(path: '/update/{id}/{status}', methods: ['GET', 'POST'], name: 'participant_updateStatus')]
    #[Security("is_granted('ROLE_WEBMASTER') or is_granted('ROLE_COORDINATOR')")]
    public function updateLead(Request $request, EntityManagerInterface $entityManager, Participant $participant, $status = null)
    {
        $form = $this->createForm(ParticipantType::class, $participant);
        $form->handleRequest($request);
    
        $participant->setStatus($status !== "null" ? $status : null);
        
        $entityManager->flush();
        return $this->json(true);
    }
}
