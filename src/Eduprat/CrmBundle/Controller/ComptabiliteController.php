<?php

namespace Eduprat\CrmBundle\Controller;

use Doctrine\ORM\EntityManagerInterface;
use Symfony\Component\Security\Http\Attribute\IsGranted;
use Symfony\Component\HttpFoundation\Response;
use Alienor\ApiBundle\Services\FlashMessages;
use Eduprat\AdminBundle\Command\GenerateCSVHistoryCommand;
use Eduprat\AdminBundle\Command\GenerateCSVParticipantMissingAttestationsCommand;
use Eduprat\AdminBundle\Command\GenerateCSVParticipantMissingModulesCommand;
use Eduprat\AdminBundle\Entity\Person;
use Eduprat\AuditBundle\Services\CourseManager;
use Eduprat\CrmBundle\Model\AttestationComptabiliteSearch;
use Eduprat\CrmBundle\Model\ComptabiliteSearch;
use Eduprat\CrmBundle\Services\ComptabiliteService;
use Eduprat\DomainBundle\Controller\EdupratController;
use Eduprat\DomainBundle\Services\SearchHandler;
use Symfony\Component\HttpFoundation\Request;
use Symfony\Component\Routing\Attribute\Route;
use Eduprat\DomainBundle\Services\ParticipationLogger;
use Symfony\Component\Form\Extension\Core\Type\FormType;
use Symfony\Component\Form\FormBuilderInterface;
use Symfony\Component\Validator\Constraints\File;
use Symfony\Component\Form\FormFactoryInterface;
use Symfony\Component\HttpFoundation\BinaryFileResponse;
use Symfony\Component\HttpFoundation\JsonResponse;
use Symfony\Component\HttpFoundation\ResponseHeaderBag;
use Symfony\Component\HttpKernel\Exception\NotFoundHttpException;
use Symfony\Component\Process\Process;
use Vich\UploaderBundle\Form\Type\VichFileType;

/**
 * Class ComptabiliteController
 */
#[Route(path: '/comptabilite')]
#[IsGranted('ROLE_COORDINATOR_LBI')]
class ComptabiliteController extends EdupratController
{

    private ParticipationLogger $participationLogger;

    public function __construct(FlashMessages $flashMessages, SearchHandler $searchHandler, ParticipationLogger $participationLogger)
    {
        parent::__construct($flashMessages, $searchHandler);
        $this->participationLogger = $participationLogger;
    }


    /**
     * Lists all Formation entities.
     *
     * @param Request $request
     * @param ComptabiliteService $comptabiliteService
     * @return Response
     */
    #[Route(path: '/suivi/{year}/{page}', methods: ['GET', 'POST'], defaults: ['year' => 'tous', 'page' => '1'], name: 'admin_crm_comptabilite', requirements: ['year' => '^(tous|\d{4})$', 'page' => '^\d+$'])]
    #[IsGranted('ROLE_COORDINATOR')]
    public function suivi(string $year, string $page, ComptabiliteService $comptabiliteService, CourseManager $courseManager)
    {
        /** @var Person $user */
        $user = $this->getUser();

        $searchHandle = $this->searchHandler->handle(ComptabiliteSearch::class, array("displayStartDate" => true), array(
            "year" => $year,
            "user" => $user
        ), array(
            "user" => $user
        ));

        if ($searchHandle["redirect"]) {
            return $this->redirect($searchHandle["redirect"]);
        }

        /** @var ComptabiliteSearch $search */
        $search = $searchHandle["search"];

        $nbPerPage = 100;
        $report = $comptabiliteService->generateReport($search, $page, $nbPerPage);

        $pagination = $this->searchHandler->getPagination($report["participations"]["count"], $report["participations"]["result"], $page, $nbPerPage);

        foreach($report["participations"]["result"] as $key => $result) {
            $step = ["id" => $result["participation"]->getNextModule()];
            if ($result["participation"]->getNextModule() == "reunion")
            {
                if ($result["participation"]->getFormation()->isElearning()) {
                    $nextLesson = $result["participation"]->getNextLesson();
                    $nextLessonPos = $nextLesson ? $nextLesson->getPosition() : 0;
                    $countLessons = $result["participation"]->getFormation()->getElearning() ? count($result["participation"]->getFormation()->getElearning()->getLessons()) : 0;
                    $report["participations"]["result"][$key]["moduleName"] = "Modules e-learning - Leçon " . $nextLessonPos . " / " . $countLessons;
                } else {
                    $report["participations"]["result"][$key]["moduleName"] = "Réunion";                    
                }
            } else {
                $report["participations"]["result"][$key]["moduleName"] = $courseManager->getModuleLabel($result["participation"], $step);
            }
            $report["participations"]["result"][$key]["moduleStep"] = $courseManager->getModuleStep($result["participation"]->getNextModule());
        }

        return $this->render('crm/comptabilite/index.html.twig', array_merge($pagination, array(
            "report" => $report,
            "form" => $searchHandle["searchForm"]->createView(),
            "search" => $search,
            'courseManager' => $courseManager,
            'hasGenerated' => GenerateCSVParticipantMissingModulesCommand::hasGeneratedFile($this->getUser()),
            'hasFinished' => GenerateCSVParticipantMissingModulesCommand::generateFileIsFinished($this->getUser()),
            'hasError' => GenerateCSVParticipantMissingModulesCommand::hasGeneratedFileError($this->getUser()),
        )));
    }

    /**
     * Lists all Formation entities.
     *
     * @param Request $request
     * @param ComptabiliteService $comptabiliteService
     * @return Response
     */
    #[Route(path: '/inscriptions/{page}', methods: ['GET', 'POST'], defaults: ['page' => '1'], name: 'admin_crm_comptabilite_inscriptions', requirements: ['page' => '^\d+$'])]
    #[IsGranted('ROLE_COORDINATOR')]
    public function inscriptions(string $page, ComptabiliteService $comptabiliteService)
    {
        /** @var Person $user */
        $user = $this->getUser();

        $searchHandle = $this->searchHandler->handle(ComptabiliteSearch::class, [
            "displayStartDate" => false,
            "displayPartenariat" => true,
            'displayQuerySearchDate' => false,
            'displayDateInscription' => true
        ], array(
            "user" => $user
        ), array(
            "user" => $user
        ));

        if ($searchHandle["redirect"]) {
            return $this->redirect($searchHandle["redirect"]);
        }

        /** @var ComptabiliteSearch $search */
        $search = $searchHandle["search"];

        $nbPerPage = 40;

        $report = $comptabiliteService->getSearchInscriptions($search, $page, $nbPerPage);
        $count = $comptabiliteService->getCountInscriptions($search);

        $npages = ceil($count / $nbPerPage);
        $current = intval($page);
        $max = 10;
        $inf = max(1, intval($current - (($max == 1 ? 0 : $max - 1) / 2)));
        $sup = min($inf + ($max - 1), $npages);
        $pageRange = range($inf, $sup);
        $pagination = array(
            'nb' => $count,
            'page' => $page,
            'npages' => $npages,
            'page_range' => $pageRange,
            'records' => $report,
        );

        return $this->render('crm/comptabilite/inscriptions.html.twig', array_merge($pagination, array(
            "report" => $report,
            "form" => $searchHandle["searchForm"]->createView(),
            "search" => $search,
            'history' => true,
            'hasGenerated' => GenerateCSVHistoryCommand::hasGeneratedFile($this->getUser()),
            'hasFinished' => GenerateCSVHistoryCommand::generateFileIsFinished($this->getUser()),
            'hasError' => GenerateCSVHistoryCommand::hasGeneratedFileError($this->getUser()),
        )));
    }

    /**
     * @param Request $request
     * @param $page
     * @param ComptabiliteService $comptabiliteService
     * @param SearchHandler $searchHandler
     * @param Person|null $coordinator
     * @return Response
     */
    #[Route(path: '/documents/{year}/{page}', methods: ['GET', 'POST'], defaults: ['year' => 'tous', 'page' => '1'], name: 'admin_crm_comptabilite_files', requirements: ['year' => '^(tous|\d{4})$', 'page' => '^\d+$'])]
    #[IsGranted('ROLE_COORDINATOR')]
    public function files(string $year, string $page, ComptabiliteService $comptabiliteService)
    {
        /** @var Person $user */
        $user = $this->getUser();

        $searchHandle = $this->searchHandler->handle(ComptabiliteSearch::class, array("displayStartDate" => true), array(
            "year" => $year,
            "user" => $user
        ), array(
            "user" => $user
        ));

        if ($searchHandle["redirect"]) {
            return $this->redirect($searchHandle["redirect"]);
        }

        /** @var ComptabiliteSearch $search */
        $search = $searchHandle["search"];

        $nbPerPage = 50;
        $report = $comptabiliteService->generateFilesReport($search, $page, $nbPerPage);

        $pagination = $this->searchHandler->getPagination($report["files"]["count"], $report["files"]["result"], $page, $nbPerPage);

        return $this->render('crm/comptabilite/files.html.twig', array_merge($pagination, array(
            "report" => $report,
            "form" => $searchHandle["searchForm"]->createView(),
            "search" => $search
        )));
    }

    /**
     * @param Request $request
     * @param $page
     * @param ComptabiliteService $comptabiliteService
     * @param SearchHandler $searchHandler
     * @param Person|null $coordinator
     * @return Response
     */
    #[Route(path: '/attestations/{year}/{page}', methods: ['GET', 'POST'], defaults: ['year' => 'tous', 'page' => '1'], name: 'admin_crm_comptabilite_attestation_files', requirements: ['year' => '^(tous|\d{4})$', 'page' => '^\d+$'])]
    #[IsGranted('ROLE_COORDINATOR')]
    public function AttestationFiles(Request $request, EntityManagerInterface $entityManager, string $year, string $page, ComptabiliteService $comptabiliteService, FormFactoryInterface $formFactory, CourseManager $courseManager)
    {
        /** @var Person $user */
        $user = $this->getUser();

        $searchHandle = $this->searchHandler->handle(AttestationComptabiliteSearch::class, array("displayStartDate" => true), array(
            "year" => $year,
            "user" => $user
        ), array(
            "user" => $user
        ));

        if ($searchHandle["redirect"]) {
            return $this->redirect($searchHandle["redirect"]);
        }

        /** @var AttestationComptabiliteSearch $search */
        $search = $searchHandle["search"];

        $nbPerPage = 50;
        $report = $comptabiliteService->generateAttestationFilesReport($search, $page, $nbPerPage);
        $pagination = $this->searchHandler->getPagination($report["files"]["count"], $report["files"]["result"], $page, $nbPerPage);

        $forms_attestation_views = [];
        $forms_attestation_viewsN1 = [];
        foreach($report["files"]["result"]  as $participation) {
            $form_attestation_views = $this->drawUploadAttestationForm($request, 'attestationHonneurFile', $participation, $formFactory);
            $form_attestation_viewsN1 = $this->drawUploadAttestationForm($request, 'attestationHonneurFileN1', $participation, $formFactory);

            if (($form_attestation_views->isSubmitted() && $form_attestation_views->isValid())
                || ($form_attestation_viewsN1->isSubmitted() && $form_attestation_viewsN1->isValid())) {

                $entityManager->persist($participation);
                $entityManager->flush();

                if (($form_attestation_views->isSubmitted() && $participation->getAttestationHonneur())
                    || ($form_attestation_viewsN1->isSubmitted() && $participation->getAttestationHonneurN1())) {
                    $this->flashMessages->addSuccess('admin.participation.attestation_honneur.importSucess');
                } else {
                    $this->flashMessages->addError('admin.participation.attestation_honneur.importError');
                }

                return $this->redirectToRoute("admin_crm_comptabilite_attestation_files", array_merge(
                    array("year" => $year, "page" => $page), $request->query->all()
                ));
            }

            $forms_attestation_views[$participation->getId()] = $form_attestation_views->createView();
            $forms_attestation_viewsN1[$participation->getId()] = $form_attestation_viewsN1->createView();
        }

        return $this->render('crm/attestation/files.html.twig', array_merge($pagination, array(
            "report" => $report,
            "form" => $searchHandle["searchForm"]->createView(),
            "search" => $search,
            'forms_attestation_honneur' => $forms_attestation_views,
            'forms_attestation_honneurN1' => $forms_attestation_viewsN1,
            'hasGenerated' => GenerateCSVParticipantMissingAttestationsCommand::hasGeneratedFile($this->getUser()),
            'hasFinished' => GenerateCSVParticipantMissingAttestationsCommand::generateFileIsFinished($this->getUser()),
            'hasError' => GenerateCSVParticipantMissingAttestationsCommand::hasGeneratedFileError($this->getUser()),
            'courseManager' => $courseManager,
        )));
    }

    public function drawUploadAttestationForm(Request $request, $fieldName, $participation, $formFactory) {
        $genForm = $formFactory->createNamedBuilder($fieldName.'_'.$participation->getId(), FormType::class, $participation);
        $this->drawUploadFormField($genForm, $fieldName, false, "12M");
        $form = $genForm->getForm();
        $form->handleRequest($request);
        return $form;
    }

    public function drawUploadFormField(FormBuilderInterface $builder, $fieldName, $allow_delete = true, $maxSize="128M") {
        $builder->add($fieldName, VichFileType::class, array(
            'label' => false,
            'required' => false,
            'download_uri' => false,
            'allow_delete' => $allow_delete,
            'error_bubbling' => true,
            'constraints' => new File(
                array(
                    'mimeTypes' => array(
                        'image/png', 'image/jpeg', 'application/pdf', 'application/x-pdf'
                    ),
                    'mimeTypesMessage' => 'formation.mimeTypesAttestation',
                    'maxSize' => $maxSize
                )
            )
        ));
    }


    /**
     * @param $type
     * @param Request $request
     * @return JsonResponse
     */
    #[Route(path: '/participation-generate-missing-modules-export', methods: ['POST'], name: 'participation_generate_missing_modules')]
    public function participantGenerateMissingModulesCsvExport(Request $request, SearchHandler $searchHandler): JsonResponse
    {
        $search = new ComptabiliteSearch();
        $searchHandler->handleArray($search, $request->request->all('eduprat_comptabilite_search'));

        $command = sprintf("eduprat:csv_participantion_missing_modules %s '%s'", $this->getUser()->getId(), json_encode($search->getParams()));

        $projectDir = $this->getParameter('kernel.project_dir');
        $cmd = sprintf("php %s/bin/console %s", $projectDir, $command);
        $cmd = sprintf("%s --env=%s >/dev/null 2>&1 &", $cmd, $this->getParameter('kernel.environment'));

        $process = Process::fromShellCommandline($cmd);
        $process->run();
        if (!$process->isSuccessful()) {
            throw new \RuntimeException($process->getErrorOutput());
        }

        $pid = $process->getOutput();

        return new JsonResponse(array("status" => "ok", "pid" => $pid));
    }

    /**
     * @param Request $request
     * @return Response
     */
    #[Route(path: '/csv-missing-modules-file-get', methods: ['GET'], name: 'csv_missing_modules_file_get')]
    public function csvMissingModulesFileGet(): BinaryFileResponse
    {
        $file = GenerateCSVParticipantMissingModulesCommand::getGeneratedFilePath($this->getUser());
        if($file && file_exists($file) && filesize($file) > 0) {
            $response = new BinaryFileResponse($file);

            $response->setContentDisposition(
                ResponseHeaderBag::DISPOSITION_ATTACHMENT,
                basename($file));

            return $response;
        }
        else {
            throw new NotFoundHttpException();
        }
    }

    /**
     * @param Request $request
     * @return Response
     */
    #[Route(path: '/csv-participations-history-file-get', methods: ['GET'], name: 'csv_participations_history_file_get')]
    public function csvParticipationHistoryFileGet(): BinaryFileResponse
    {
        $file = GenerateCSVHistoryCommand::getGeneratedFilePath($this->getUser());
        if($file && file_exists($file) && filesize($file) > 0) {
            $response = new BinaryFileResponse($file);

            $response->setContentDisposition(
                ResponseHeaderBag::DISPOSITION_ATTACHMENT,
                basename($file));

            return $response;
        }
        else {
            throw new NotFoundHttpException();
        }
    }


        /**
     * @param $type
     * @param Request $request
     * @return JsonResponse
     */
    #[Route(path: '/participation-generate-missing-attestations-export', methods: ['POST'], name: 'participation_generate_missing_attestations')]
    public function participantGenerateMissingAttestationsCsvExport(Request $request, SearchHandler $searchHandler): JsonResponse
    {
        $search = new AttestationComptabiliteSearch();
        $searchHandler->handleArray($search, $request->request->all('eduprat_comptabilite_search'));


        
        $command = sprintf("eduprat:csv_participantion_missing_attestations %s '%s'", $this->getUser()->getId(), json_encode($search->getParams()));
        
        $projectDir = $this->getParameter('kernel.project_dir');
        $cmd = sprintf("php %s/bin/console %s", $projectDir, $command);
        $cmd = sprintf("%s --env=%s >/dev/null 2>&1 &", $cmd, $this->getParameter('kernel.environment'));

        $process = Process::fromShellCommandline($cmd);
        $process->run();
        if (!$process->isSuccessful()) {
            throw new \RuntimeException($process->getErrorOutput());
        }

        $pid = $process->getOutput();

        return new JsonResponse(array("status" => "ok", "pid" => $pid));
    }

    /**
     * @param Request $request
     * @return Response
     */
    #[Route(path: '/csv-missing-attestations-file-get', methods: ['GET'], name: 'csv_missing_attestations_file_get')]
    public function csvMissingAttestationsFileGet(): BinaryFileResponse
    {
        $file = GenerateCSVParticipantMissingAttestationsCommand::getGeneratedFilePath($this->getUser());
        if($file && file_exists($file) && filesize($file) > 0) {
            $response = new BinaryFileResponse($file);

            $response->setContentDisposition(
                ResponseHeaderBag::DISPOSITION_ATTACHMENT,
                basename($file));

            return $response;
        }
        else {
            throw new NotFoundHttpException();
        }
    }

    #[Route(path: '/participant-history-generate-csv-export', methods: ['POST'], name: 'participant_history_generate_csv_export')]
    public function participantHistoryGenerateCsvExport(Request $request, SearchHandler $searchHandler): JsonResponse
    {
        $search = new ComptabiliteSearch();
        $searchHandler->handleArray($search, $request->request->all('eduprat_comptabilite_search'));

        $projectDir = $this->getParameter('kernel.project_dir');
        $command = "eduprat:csv_participant_history";
        $json = str_replace("\"", "\\\"", json_encode($search->getParams()));
        $cmd = sprintf("php %s/bin/console %s %s \"%s\"", $projectDir, $command, $this->getUser()->getId(), $json);
        $cmd = sprintf("%s --env=%s >/dev/null 2>&1 &", $cmd, $this->getParameter('kernel.environment'));

        $process = Process::fromShellCommandline($cmd);
        $process->run();
        if (!$process->isSuccessful()) {
            throw new \RuntimeException($process->getErrorOutput());
        }

        $pid = $process->getOutput();

        return new JsonResponse(array("status" => "ok", "pid" => $pid));
    }
}
