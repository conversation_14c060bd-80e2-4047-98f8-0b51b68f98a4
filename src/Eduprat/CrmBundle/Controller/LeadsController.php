<?php

namespace Eduprat\CrmBundle\Controller;

use Symfony\Component\ExpressionLanguage\Expression;
use Symfony\Component\HttpFoundation\Response;
use Doctrine\ORM\EntityManagerInterface;
use Ed<PERSON>rat\AdminBundle\Command\GenerateCSVLeadCommand;
use Eduprat\AdminBundle\Entity\LeadSearch;
use Eduprat\CrmBundle\Services\LeadsService;
use Eduprat\DomainBundle\Controller\EdupratController;
use Eduprat\DomainBundle\Entity\Participant;
use Eduprat\DomainBundle\Entity\LeadHistory;
use Eduprat\DomainBundle\Form\ParticipantType;
use Eduprat\DomainBundle\Form\ProgrammeType;
use Eduprat\DomainBundle\Services\SearchHandler;
use Symfony\Component\Security\Http\Attribute\IsGranted;
use Symfony\Component\Form\Extension\Core\Type\FileType;
use Symfony\Component\Form\FormFactoryInterface;
use Symfony\Component\HttpFoundation\BinaryFileResponse;
use Symfony\Component\HttpFoundation\File\UploadedFile;
use Symfony\Component\HttpFoundation\JsonResponse;
use Symfony\Component\HttpFoundation\Request;
use Symfony\Component\HttpFoundation\ResponseHeaderBag;
use Symfony\Component\HttpKernel\Exception\NotFoundHttpException;
use Symfony\Component\Process\Process;
use Symfony\Component\Routing\Attribute\Route;
use Symfony\Component\Validator\Constraints\File;
use Symfony\Component\Form\FormInterface;

#[Route(path: '/leads')]
class LeadsController extends EdupratController
{
    #[Route(path: '/import/{partner}', name: 'crm_leads_import')]
    public function import($partner, Request $request, FormFactoryInterface $formFactory, LeadsService $leadsService)
    {
        $sessionKey = 'current_import_leads';

        $form = $this->createFormBuilder()
            ->add('leadsFile', FileType::class, array(
                'label' => false,
                'constraints' => new File(
                    array(
                        'mimeTypes' => array(
                            'text/plain', 'text/csv', 'application/octet-stream'
                        ),
                        'mimeTypesMessage' => 'Le fichier choisi n\'est pas un fichier au format CSV'
                    )
                )
            ))
            ->getForm();

        $form->handleRequest($request);

        $formSubmit = $formFactory->createNamedBuilder('submit')->getForm();
        $formSubmit->handleRequest($request);

        $preview = null;
        $coordinators = null;
        $advisors = null;

        $coordinators = $leadsService->getCoordinators();
        $advisors = $leadsService->getAdvisors();
        $codeApporteurList = [];
        $rppsList = [];
        foreach($advisors as $advisor) {
            if ($advisor->getCodeApporteur()) {
                $codeApporteurList[$advisor->getCodeApporteur()] = $advisor;
            }
        }

        if ($form->isSubmitted()) {
            if ($form->isValid()) {
                $file = $form->get('leadsFile');
                /** @var UploadedFile $fileData */
                $fileData = $file->getData();

                $nbLinesIgnored = 1;
                $datas = $leadsService->parseCSV($fileData->getRealPath(), $nbLinesIgnored, ";", true);

                $request->getSession()->set($sessionKey, $datas);
                $preview = $leadsService->previewLeads($partner, $datas, true);

                foreach($preview["updated"] as $item) {
                    foreach($item["participants"] as $participant) {
                        if($participant->getRpps()) {
                            $rppsList[$participant->getRpps()] = $participant;
                        }
                    }
                }

                if (count($preview['errors']) > 0) {
                    $preview['errors'] = array_map("unserialize", array_unique(array_map("serialize", $preview['errors'])));
                    foreach ($preview['errors'] as $error) {
                        $errorData = $error['participant'];
                        $params = ['%name%' => $errorData[LeadsService::FIELD_FIRSTNAME] . " " . $errorData[LeadsService::FIELD_LASTNAME]];
                        if (isset($error["message"])) {
                            $params["%error%"] = $error["message"];
                        }
                        $this->flashMessages->addError($error['error'], $params);
                    }
                }
            } else {
                foreach ($form->getErrors(true) as $key => $error) {
                    $this->flashMessages->addError($error->getMessage());
                }
            }
        }

        if ($formSubmit && $formSubmit->isSubmitted()) {
            $datas = $request->getSession()->get($sessionKey);
            $thereIsError = $leadsService->importLeads($partner, $request->request->all(), $datas);

            if($thereIsError) {
                foreach($thereIsError as $error) {
                    $params = ['%name%' => $error['participant']->getFirstName() . " " . $error['participant']->getLastName()];
                    if (isset($error["message"])) {
                        $params["%error%"] = $error["message"];
                    }
                    $this->flashMessages->addError($error['error'], $params);
                }
                return $this->redirectToRoute("crm_leads_import", array("partner" => $partner));
            }

            $this->flashMessages->addSuccess("crm.leads.import.success");

            return $this->redirectToRoute("crm_leads_import", array("partner" => $partner));
        }

        return $this->render('crm/leads/import.html.twig', array(
            'partner' => $partner,
            'form' => $form,
            'formSubmit' => $formSubmit,
            'preview' => $preview,
            'coordinators' => $coordinators,
            'codeApporteurList' => $codeApporteurList,
            'rppsList' => $rppsList,
            'advisors' => $advisors,
            "categories" => ProgrammeType::getCategories(),
            "specialities" => ProgrammeType::getFlatSpecialities()
        ));
    }


    /**
     * Lists all Participant (Lead) entities.
     *
     * @param Request $request
     * @return Response
     */
    #[Route(path: '/suivi/{page}', methods: ['GET', 'POST'], defaults: ['page' => '1'], name: 'crm_leads_suivi', requirements: ['page' => '^\d+$'])]
    #[IsGranted('ROLE_COORDINATOR_LBI')]
    public function indexLead(Request $request, EntityManagerInterface $em, string $page,  SearchHandler $searchHandler)
    {
        /** @var ParticipantRepository $participantRepositoty */
        $participantRepository = $em->getRepository(Participant::class);


        $displayCoordinatorFilter = false;
        if($this->getUser()->isSupervisor() || $this->getUser()->isSupervisorFr() || $this->getUser()->isWebmaster() || in_array('ROLE_SUPER_ADMIN', $this->getUser()->getRoles())) {
            $displayCoordinatorFilter = true;
        }

        $supervisorId = $this->getUser()->isSupervisor() ? $this->getUser()->getId(): null;

        $type = $request->query->has("sendinblue") ? "sib" : "default";
        $searchHandle = $searchHandler->handle(LeadSearch::class, array(
            'displayCoordinatorFilter' => $displayCoordinatorFilter,
            'type' => $type,
            'isCoordinator' => $this->getUser()->isCoordinator() ? $this->getUser() : false,
            'supervisorId' => $supervisorId,
        ));

        if ($searchHandle["redirect"]) {
            return $this->redirect($searchHandle["redirect"]);
        }

        /** @var LeadSearch $search */
        $search = $searchHandle["search"];
        $search->setUserFilters($this->getUser());

        $nbPerPage = 30;
        $max = 10;
        $count = $participantRepository->countLeadSearchResults($search);
        $participants = $participantRepository->findLeadSearchResults($search, $page, $nbPerPage);

        $npages = ceil($count / $nbPerPage);
        $current = intval($page);
        $inf = max(1, intval($current - (($max == 1 ? 0 : $max-1) / 2)));
        $sup = min($inf + ($max-1), $npages);
        $pageRange = range($inf, $sup);
        $pagination = array(
            'nb' => $count,
            'page' => $page,
            'npages' => $npages,
            'page_range' => $pageRange,
            'participants' => $participants,
        );

        $sib = $request->query->get("sendinblue") !== null ? true : false;
        return $this->render('crm/leads/suivi.html.twig', array_merge($pagination, array(
            'form' => $searchHandle["searchForm"]->createView(),
            'search' => $search,
            'hasGenerated' => GenerateCSVLeadCommand::hasGeneratedFile($this->getUser()),
            'hasFinished' => GenerateCSVLeadCommand::generateFileIsFinished($this->getUser()),
            'hasError' => GenerateCSVLeadCommand::hasGeneratedFileError($this->getUser()),
            'sib' => $sib,
            'crm' => true,
        )));
    }

    #[Route(path: '/lead-generate-csv-export', methods: ['POST'], name: 'lead_generate_csv_export')]
    public function leadGenerateCsvExport(Request $request, SearchHandler $searchHandler): JsonResponse
    {
        $search = new LeadSearch();
        $searchHandler->handleArray($search, $request->request->all('eduprat_lead_search'));

        $projectDir = $this->getParameter('kernel.project_dir');
        $command = "eduprat:csv_lead";
        $json = str_replace("\"", "\\\"", json_encode($search->getParams()));
        $cmd = sprintf("php %s/bin/console %s %s \"%s\"", $projectDir, $command, $this->getUser()->getId(), $json);
        $cmd = sprintf("%s --env=%s >/dev/null 2>&1 &", $cmd, $this->getParameter('kernel.environment'));

        $process = Process::fromShellCommandline($cmd);
        $process->run();
        if (!$process->isSuccessful()) {
            throw new \RuntimeException($process->getErrorOutput());
        }

        $pid = $process->getOutput();

        return new JsonResponse(array("status" => "ok", "pid" => $pid));
    }

    /**
     * @param Request $request
     * @return Response
     */
    #[Route(path: '/csv-lead-file-get', methods: ['GET'], name: 'csv_lead_file_get')]
    public function csvLeadFileGet(): BinaryFileResponse
    {
        $file = GenerateCSVLeadCommand::getGeneratedFilePath($this->getUser());
        if($file && file_exists($file) && filesize($file) > 0) {
            $response = new BinaryFileResponse($file);

            $response->setContentDisposition(
                ResponseHeaderBag::DISPOSITION_ATTACHMENT,
                basename($file));

            return $response;
        }
        else {
            throw new NotFoundHttpException();
        }
    }

        /**
     * Displays a form to edit an existing commande entity.
     *
     * @param Request                            $request
     * @param Participant $participant
     */
    #[Route(path: '/update/{id}/{leadContactDate}/{leadComment}/{leadCommentEduprat}/{leadState}', methods: ['GET', 'POST'], name: 'crm_updateLead')]
    #[IsGranted(new Expression('is_granted("ROLE_WEBMASTER") or is_granted("ROLE_COORDINATOR")'))]
    public function updateLead(Request $request, EntityManagerInterface $entityManager, Participant $participant, $leadContactDate = null, $leadComment = null, $leadCommentEduprat = null, $leadState = null): JsonResponse
    {
        $form = $this->createForm(ParticipantType::class, $participant);
        $form->handleRequest($request);

        $participant->setLeadComment($leadComment !== "null" ? str_replace('_s_', '/', $leadComment) : null);
        $participant->setLeadCommentEduprat($leadCommentEduprat !== "null" ? str_replace('_s_', '/', $leadCommentEduprat) : null);
        $participant->setLeadContactDate($leadContactDate !== "null" ? str_replace('_s_', '/', $leadContactDate) : null);

        $participant->setLeadState($leadState);

        $entityManager->flush();
        return $this->json(true);
    }

     /**
     * Deletes a LeadHistory entity.
     *
     * @param Request $request
     * @param LeadHistory $leadHistory
     * @return RedirectResponse|Response
     */
    #[Route(path: '/{id}/deleteLeadHistory', methods: ['GET', 'POST'], name: 'admin_lead_hisory_delete')]
    public function delete(Request $request, LeadHistory $leadHistory, EntityManagerInterface $em)
    {
        $form = $this->createLeadHistoryDeleteForm($leadHistory);
        $form->handleRequest($request);

        if ($form->isSubmitted() && $form->isValid()) {
            try {
                $em->remove($leadHistory);
                $em->flush();
            } catch (ForeignKeyConstraintViolationException $e) {
                $this->flashMessages->addError("Une erreur inattendue s'est produite pendant la suppression de l'historique de Lead");
                return $this->redirectToRoute('crm_leads_suivi');
            }

            $this->flashMessages->addSuccess("L'historique a été supprimé avec succès");
            return $this->redirectToRoute('crm_leads_suivi');
        }

        return $this->render('crm/lead_history/delete.html.twig', array(
            'leadHistory' => $leadHistory,
            'form' => $form,
        ));
    }

    /**
     * Creates a form to delete a LeadHistory entity.
     */
    private function createLeadHistoryDeleteForm(LeadHistory $leadHistory): FormInterface
    {
        return $this->createFormBuilder()
            ->setAction($this->generateUrl('admin_lead_hisory_delete', array('id' => $leadHistory->getId())))
            ->setMethod(Request::METHOD_POST)
            ->getForm()
            ;
    }
}
