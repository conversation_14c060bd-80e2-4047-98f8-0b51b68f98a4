<?php

namespace Eduprat\AdminBundle\Form;

use <PERSON><PERSON><PERSON>\AdminBundle\Validator\Constraints\NotAlreadyUsedPassword;
use Symfony\Component\Form\AbstractType;
use Symfony\Component\Form\Extension\Core\Type\PasswordType;
use Symfony\Component\Form\Extension\Core\Type\RepeatedType;
use Symfony\Component\Form\FormBuilderInterface;
use Symfony\Component\OptionsResolver\OptionsResolver;
use Symfony\Component\Validator\Constraints\NotBlank;
use Symfony\Component\Validator\Constraints\NotCompromisedPassword;
use Symfony\Component\Validator\Constraints\Regex;

class ChangePasswordType extends AbstractType
{

    public function buildForm(FormBuilderInterface $builder, array $options): void
    {
        $builder->add('plainPassword', RepeatedType::class, array(
            'type' => PasswordType::class,
            'first_options' => array('label' => 'form.new_password'),
            'second_options' => array('label' => 'form.new_password_confirmation'),
            'invalid_message' => 'password.edit.mismatch',
            'constraints' => array(
                new NotCompromisedPassword(array('groups' => ['Default', 'password_edit'])),
                new NotBlank(array('groups' => ['Default', 'password_edit'])),
                new Regex(array(
                    'groups' => ['Default', 'password_edit'],
                    'pattern' => '/^(?=.*[a-z])(?=.*[A-Z])(?=.*[0-9])(?=.*[^a-zA-Z0-9]).{8,}$/',
                    'match' => true,
                    'message' => 'password.edit.constraints',
                )),
                new NotAlreadyUsedPassword(array('groups' => ['Default', 'password_edit', 'password_edit_extranet']))
            ),
        ));
    }

    public function configureOptions(OptionsResolver $resolver): void
    {
        $resolver->setDefaults(array(
        ));
    }

    public function getBlockPrefix(): string
    {
        return 'eduprat_admin_change_password';
    }
}
