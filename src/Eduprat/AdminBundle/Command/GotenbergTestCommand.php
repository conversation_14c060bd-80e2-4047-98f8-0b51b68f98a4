<?php

namespace Eduprat\AdminBundle\Command;

use <PERSON>enberg\Gotenberg;
use Gotenberg\Stream;
use Symfony\Component\Console\Attribute\AsCommand;
use Symfony\Component\Console\Command\Command;
use Symfony\Component\Console\Input\InputInterface;
use Symfony\Component\Console\Output\OutputInterface;
use Symfony\Component\DependencyInjection\ParameterBag\ParameterBagInterface;
use Symfony\Component\Routing\Generator\UrlGeneratorInterface;
use Symfony\Component\Routing\RouterInterface;

#[AsCommand(
    name: 'app:gotenberg-test',
    description: 'Test de Gotenberg',
)]
class GotenbergTestCommand extends Command
{


    private RouterInterface $router;
    private ParameterBagInterface $parameterBag;

    public function __construct(RouterInterface $router, ParameterBagInterface $parameterBag)
    {
        parent::__construct();
        $this->router = $router;
        $this->parameterBag = $parameterBag;
    }

    protected function configure(): void
    {
    }

    protected function execute(InputInterface $input, OutputInterface $output): int
    {
        $pageUrl = $this->router->generate('admin_gotenberg_corps', [], UrlGeneratorInterface::ABSOLUTE_URL);

        $request = Gotenberg::chromium('http://gotenberg:3000')
            ->pdf()
//            ->printBackground()
//            ->assets(
//                Stream::path('https://paul.int.alienor.xyz/admin/bootstrap/css/bootstrap.min.css'),
//                Stream::path('https://paul.int.alienor.xyz/admin/dist/css/font-awesome.min.css'),
//                Stream::path('https://paul.int.alienor.xyz/admin/dist/css/AdminLTE.min.css'),
//                Stream::path('https://paul.int.alienor.xyz/css/front.css'),
//                Stream::path('https://paul.int.alienor.xyz/css/evaluation.css'),
//                Stream::path('https://paul.int.alienor.xyz/font/stylesheet.css'),
//                Stream::path('https://paul.int.alienor.xyz/img/eduprat_element_logo.png'),
//            )

            ->trace('debug', 'Request-Id')
            ->preferCssPageSize()
//                ->paperSize(8.27, 11.7)
//            ->margins(1, 0.68, 0.39, 0.39)
//                ->paperSize( 8.27, 11.7)
//            ->emulateScreenMediaType()
            ->header(Stream::path($this->parameterBag->get('kernel.project_dir').'/header.html'))
            ->footer(Stream::path($this->parameterBag->get('kernel.project_dir').'/footer.html'))
//                ->assets()
                ->webhook($this->router->generate('admin_gotenberg_debug', [], UrlGeneratorInterface::ABSOLUTE_URL), $this->router->generate('admin_gotenberg_debug_error', [], UrlGeneratorInterface::ABSOLUTE_URL))
            ->url($pageUrl);
        return Command::SUCCESS;
    }
}
