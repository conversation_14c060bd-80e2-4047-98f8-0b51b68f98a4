<?php

namespace Eduprat\AdminBundle\Command;

use Doctrine\ORM\EntityManagerInterface;
use Eduprat\DomainBundle\Entity\Participant;
use Symfony\Component\Console\Attribute\AsCommand;
use Symfony\Component\Console\Command\Command;
use Symfony\Component\Console\Input\InputInterface;
use Symfony\Component\Console\Output\OutputInterface;
use Symfony\Component\VarDumper\VarDumper;

#[AsCommand(name:'eduprat:init_participations_lead', description: "Attribue isLead true aux participations rattachées à des leads")]
class initParticipationLeadCommand extends Command
{
    private $formationRepo;

    /**
     * @var EntityManagerInterface
     */
    private $entityManager;


    public function __construct(EntityManagerInterface $entityManager)
    {
        parent::__construct();
        $this->entityManager = $entityManager;
    }

    protected function configure(): void
    {
    }

    protected function execute(InputInterface $input, OutputInterface $output): int
    {
        $leads = $this->entityManager
            ->createQueryBuilder()
            ->select('p')
            ->from(Participant::class, 'p')
            ->where('p.leadStatus = :leadStatus')->setParameter('leadStatus', Participant::LEAD_ON_VALUE)
            ->getQuery()
            ->getResult()
            ;

        $compt = 0;
        foreach ($leads as $lead) {
            foreach($lead->getParticipations() as $participation) {
                if ($participation->getFormation()->getStartDate() >= $lead->getLeadCreationDate()) {
                    $participation->setIsLead(true);
                }
            }

            if ($compt % 5 == 0) {
                VarDumper::dump($compt .'/'. count($leads));
            }
            $compt++;
        }
        $this->entityManager->flush();
        return Command::SUCCESS;
    }
}
