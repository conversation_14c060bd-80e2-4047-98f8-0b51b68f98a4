<?php
namespace Eduprat\AdminBundle\Command;

use Doctrine\ORM\EntityManagerInterface;
use Ed<PERSON><PERSON>\DomainBundle\Entity\Formation;
use Symfony\Component\Console\Attribute\AsCommand;
use Symfony\Component\Console\Command\Command;
use Symfony\Component\Console\Input\InputInterface;
use Symfony\Component\Console\Output\OutputInterface;
#[AsCommand(name: 'eduprat:midcourse_auto_check_accounted_command')]
class MidCourseAutoCheckAccountedCommand extends Command
{
    /**
     * @var EntityManagerInterface
     */
    private $entityManager;

    public function __construct(EntityManagerInterface $entityManager)
    {
        $this->entityManager = $entityManager;
        parent::__construct();
    }
    protected function configure(): void
    {
    }

    protected function execute(InputInterface $input, OutputInterface $output): int
    {

        $formations = $this->findAllPluriannual();
        foreach($formations as $formation) {
            if ($formation->isPluriannuelle()) {
                if ($formation->isLocked()) {
                    $formation->setAccountedMidCourse(true);
                    $formation->setAccounted(false);
                } else {
                    $formation->setAccountedMidCourse(false);
                }
            }
        }
        $this->entityManager->flush();
        return 0;
    }
   public function findAllPluriannual(): array
   {
       $formationRepo = $this->entityManager->getRepository(Formation::class);
       $qb = $formationRepo->createQueryBuilder('f');
       return $qb
           ->select('f')
           ->where($qb->expr()->neq('YEAR(f.openingDate)', 'YEAR(f.closingDate)'))
           ->getQuery()
           ->getResult()
           ;
   }
}
