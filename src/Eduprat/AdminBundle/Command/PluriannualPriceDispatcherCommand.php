<?php
namespace Eduprat\AdminBundle\Command;

use Doctrine\ORM\EntityManagerInterface;
use Eduprat\AdminBundle\Services\PriceDispatcher;
use Eduprat\DomainBundle\Entity\Formation;
use Symfony\Component\Console\Attribute\AsCommand;
use Symfony\Component\Console\Command\Command;
use Symfony\Component\Console\Input\InputInterface;
use Symfony\Component\Console\Output\OutputInterface;
#[AsCommand(name: 'eduprat:pluriannual_price_dispatching')]
class PluriannualPriceDispatcherCommand extends Command
{
    /**
     * @var EntityManagerInterface
     */
    private $entityManager;

    /**
     * @var PriceDispatcher
     */
    private $priceDispatcher;

    public function __construct(EntityManagerInterface $entityManager, PriceDispatcher $priceDispatcher)
    {
        $this->entityManager = $entityManager;
        $this->priceDispatcher = $priceDispatcher;
        parent::__construct();
    }
    protected function configure(): void
    {
    }

    protected function execute(InputInterface $input, OutputInterface $output): int
    {
        $formations = $this->findAllPluriannualInCurrentYear();
        foreach($formations as $formation) {
            foreach ($formation->getParticipations() as $participation) {
                $this->priceDispatcher->dispatchPluriannualPrice($participation);
            }
        }
        $this->entityManager->flush();
        return 0;
    }

   public function findAllPluriannualInCurrentYear(): array
   {
       $formationRepo = $this->entityManager->getRepository(Formation::class);
       $qb = $formationRepo->createQueryBuilder('f');
       return $qb
           ->select('f')
           ->where($qb->expr()->eq('YEAR(f.openingDate)', ":openingYear"))
           ->andWhere($qb->expr()->eq('YEAR(f.closingDate)', ":closingYear"))
            ->setParameter("openingYear", date('Y'))
            ->setParameter("closingYear", (date('Y') +1))
           ->getQuery()
           ->getResult()
           ;
   }
}
