<?php

namespace Eduprat\AdminBundle\Command;

use Symfony\Component\Console\Attribute\AsCommand;
use Doctrine\ORM\EntityManagerInterface;
use Eduprat\DomainBundle\Entity\Formation;
use Alienor\EmailBundle\Services\Sender;
use Eduprat\DomainBundle\Services\EmailSender;
use Eduprat\DomainBundle\Services\Email\EmailFormationOpened;
use Symfony\Component\Console\Command\Command;
use Symfony\Component\Console\Input\InputArgument;
use Symfony\Component\Console\Input\InputInterface;
use Symfony\Component\Console\Output\OutputInterface;

#[AsCommand(name: 'eduprat:asychr_opening_emails', description: "Génère le fichier CSV d'exports des modules manquants")]
class SendAsynchrOpeningEmailFormAddedCommand extends Command
{
    private $router;

    /**
     * @var Sender
     */
    private $sender;

    /**
     * @var EmailSender
     */
    private $emailSender;

    /**
     * @var EntityManagerInterface
     */
    private $entityManager;


    public function __construct(Sender $sender, EmailSender $emailSender, EntityManagerInterface $entityManager)
    {
        parent::__construct();
        $this->sender = $sender;
        $this->emailSender = $emailSender;
        $this->entityManager = $entityManager;
    }

    protected function configure(): void
    {
        $this
            ->addArgument('formation', InputArgument::REQUIRED, 'Formation id')
            ;
    }

    protected function execute(InputInterface $input, OutputInterface $output): int
    {
        $this->formationRepo = $this->entityManager->getRepository(Formation::class);
        $formation = $this->formationRepo->find($input->getArgument('formation'));

        try {
            $checker = $this->sender->getChecker(EmailFormationOpened::ALIAS);
            $this->emailSender->sendAllAutomaticParticipationEmail($checker ,$formation);

        } catch (\Exception $e) {
            $output->writeln($e->getMessage());
        }
        return Command::SUCCESS;
    }

    /**
     * @param $user
     * @return bool
     */
    static public function hasGeneratedFile($user) {
        return self::getGeneratedFilePath($user) && file_exists(self::getGeneratedFilePath($user));
    }

    /**
     * @param $user
     * @return bool
     */
    static public function hasGeneratedFileError($user) {
        return self::getGeneratedFileErrorPath($user) && file_exists(self::getGeneratedFileErrorPath($user));
    }

    /**
     * @param $user
     * @return bool
     */
    static public function generateFileIsFinished($user) {
        if (self::hasGeneratedFile($user)) {
            return filesize(self::getGeneratedFilePath($user)) > 0;
        }
        return false;
    }

    /**
     * @param $user
     * @return string
     */
    static public function getGeneratedFileBasePath() {
        return __DIR__ . sprintf("/../../../../uploads/participation/modulesManquants/");
    }

    /**
     * @param $user
     * @return string
     */
    static public function getGeneratedFilePath($user) {
        return sprintf(self::getGeneratedFileBasePath() . "eduprat_participants_missing_modules_%s.csv", $user->getId());
    }

    /**
     * @param $user
     * @return string
     */
    static public function getGeneratedFileTmpPath($user) {
        return sprintf(self::getGeneratedFileBasePath() . "eduprat_participants_missing_modules_%s.tmp", $user->getId());
    }

    /**
     * @param $user
     * @return string
     */
    static public function getGeneratedFileErrorPath($user) {
        return sprintf(self::getGeneratedFileBasePath() . "eduprat_participants_missing_modules_%s.log", $user->getId());
    }

}
