<?php

namespace Eduprat\AdminBundle\Command;

use Doctrine\Common\Collections\Order;
use Doctrine\ORM\EntityManagerInterface;
use Eduprat\DomainBundle\Entity\Participation;
use Symfony\Component\Console\Attribute\AsCommand;
use Symfony\Component\Console\Command\Command;
use Symfony\Component\Console\Input\InputInterface;
use Symfony\Component\Console\Input\InputOption;
use Symfony\Component\Console\Output\OutputInterface;
use Symfony\Component\Console\Style\SymfonyStyle;

#[AsCommand(
    name: 'app:init-participation-dateModuleManquantExpiration',
    description: '[One Shot] Commande pour initialiser dateModuleManquantExpiration de Participation',
)]
class InitParticipationDateModuleManquantExpirationCommand extends Command
{
    public function __construct(private EntityManagerInterface $entityManager)
    {
        parent::__construct();
    }

    protected function configure(): void
    {
        $this
            ->addOption('force', null, InputOption::VALUE_NONE, 'forcer la modification de la base de données')
        ;
    }

    protected function execute(InputInterface $input, OutputInterface $output): int
    {
        $io = new SymfonyStyle($input, $output);

        $feRepo = $this->entityManager->getRepository(Participation::class);

        $debug = [];
        $participations = $feRepo->createQueryBuilder('p')
            ->orderBy('p.id', Order::Descending->value)
            ->getQuery()
            ->getResult();

        $io->progressStart(count($participations));
        /** @var Participation $participation */
        foreach ($participations as $participation) {
            $participation->calculateEcheanceNextModule();
            $debug[] = [
                'id' => $participation->getId(),
                'echeance' => $input->getOption('force') ? $participation->getDateModuleManquantExpiration() : $participation->getDateModuleManquantExpiration()?->format('d/m/Y h:i:s'),
            ];
        }


        if (!$input->getOption('force')) {
            $io->table(
                ['Id', 'echeance'],
                $debug
            );
            $io->note(sprintf('%s participations à mettre à jour', count($debug)));
            $io->note('Utiliser --force pour lancer la commande');
        } else {
//            $io->note('Maj de la base');
            try {
                $connection = $this->entityManager->getConnection();
                $stmt = $connection->prepare("UPDATE participation SET `date_module_manquant_expiration` = ? where id = ?");
                foreach ($debug as $d) {
                    $stmt->bindValue(1, $d['echeance'] ?? null, "datetime");
                    $stmt->bindValue(2, $d['id']);
                    $resultSet = $stmt->executeQuery();
                    $io->progressAdvance();
                }
                $io->progressFinish();
                $io->success('Maj de la base.');
            } catch (\Exception $e) {
                $io->error('Erreur lors de la Maj de la base. '.$e->getMessage());
            }
        }
        return Command::SUCCESS;
    }
}
