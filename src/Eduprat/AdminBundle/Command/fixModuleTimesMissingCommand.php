<?php
namespace Eduprat\AdminBundle\Command;

use Symfony\Component\Console\Attribute\AsCommand;
use Doctrine\ORM\EntityManagerInterface;
use Eduprat\DomainBundle\Entity\Formation;
use Symfony\Component\Console\Command\Command;
use Symfony\Component\Console\Input\InputInterface;
use Symfony\Component\Console\Output\OutputInterface;
use Eduprat\DomainBundle\Entity\ModuleTimes;

#[AsCommand(name: 'eduprat:fix_missing_module_times', description: "Création des moduleTimes manquants pour les formations")]
class fixModuleTimesMissingCommand extends Command
{
    /**
     * @var EntityManagerInterface
     */
    private $entityManager;

    public function __construct(EntityManagerInterface $entityManager)
    {
        $this->entityManager = $entityManager;
        parent::__construct();
    }
    protected function configure(): void
    {
    }

    protected function execute(InputInterface $input, OutputInterface $output): int
    {
        $formations = $this->findMissingModuleTimesFormations();
        $count = 0;
        $formationCount = count($formations);
        foreach ($formations as $formation) {
            if (!count($formation->getModuleTimes())) {
                dump($formation->getId());
                $formation->addModuleTime(new ModuleTimes($formation->getDiscr()));
            }
            $count++;
        }
        $this->entityManager->flush();
        dump($count . "/" . $formationCount);
        return 0;
    }
   public function findMissingModuleTimesFormations(): array
   {
       $formationRepository = $this->entityManager->getRepository(Formation::class);
       $qb = $formationRepository->createQueryBuilder('f');
       return $qb
            ->select('f')
            ->leftJoin('f.moduleTimes', 'mt')
            ->andWhere('mt.id IS NULL')
            ->getQuery()
            ->getResult()
           ;
   }
}
