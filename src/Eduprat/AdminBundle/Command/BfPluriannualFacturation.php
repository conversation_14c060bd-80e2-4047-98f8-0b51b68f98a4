<?php
namespace Eduprat\AdminBundle\Command;

use Doctrine\ORM\EntityManagerInterface;
use Ed<PERSON>rat\DomainBundle\Entity\Formation;
use Symfony\Component\Console\Attribute\AsCommand;
use Symfony\Component\Console\Command\Command;
use Symfony\Component\Console\Input\InputInterface;
use Symfony\Component\Console\Output\OutputInterface;

#[AsCommand(name: 'eduprat:bf:pluriannual:facturation')]
class BfPluriannualFacturation extends Command
{
    public function __construct(
        private readonly EntityManagerInterface $entityManager,
    ) {
        parent::__construct();
    }
    protected function configure(): void
    {
    }

    protected function execute(InputInterface $input, OutputInterface $output): int
    {
        $formations = $this->findAllPluriannual();
        dump(count($formations));
        foreach($formations as $formation) {
            $mustBeFactured = true;
            foreach ($formation->getFinanceSousModes() as $financeSousMode) {
                if (!$formation->isFacturedFinanceSousMode($financeSousMode->getId())) {
                    $mustBeFactured = false;
                }
                if (!$formation->isFacturedFinanceSousMode($financeSousMode->getId(), true)) {
                    $mustBeFactured = false;
                }
            }
            if ($formation->getBilled() != $mustBeFactured) {
                dump($formation->getId() . " - " . ($mustBeFactured ? "doit être facturée" : "ne doit pas être facturée"));
                $formation->setBilled($mustBeFactured);
            }

        }
        $this->entityManager->flush();
        return 0;
    }

    public function findAllPluriannual(): array
    {
        $formationRepo = $this->entityManager->getRepository(Formation::class);
        $qb = $formationRepo->createQueryBuilder('f');
        return $qb
            ->select('f')
            ->where($qb->expr()->neq('YEAR(f.openingDate)', 'YEAR(f.closingDate)'))
            ->getQuery()
            ->getResult()
            ;
    }
}
