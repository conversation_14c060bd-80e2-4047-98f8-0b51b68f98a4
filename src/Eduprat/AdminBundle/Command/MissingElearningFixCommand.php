<?php

namespace Eduprat\AdminBundle\Command;

use Doctrine\ORM\EntityManagerInterface;
use Eduprat\AuditBundle\Services\CourseManager;
use Eduprat\DomainBundle\Entity\Participation;
use Symfony\Component\Console\Attribute\AsCommand;
use Symfony\Component\Console\Command\Command;
use Symfony\Component\Console\Input\InputInterface;
use Symfony\Component\Console\Input\InputOption;
use Symfony\Component\Console\Output\OutputInterface;

#[AsCommand(name: 'eduprat:missing_elearning_fix', description: "BF module elearning manquant alors que fait")]
class MissingElearningFixCommand extends Command
{
    private $participationRepo;

    /**
     * @var EntityManagerInterface
     */
    private $entityManager;

    /**
     * @var CourseManager
     */
    private $courseManager;

    public function __construct(EntityManagerInterface $entityManager, CourseManager $courseManager)
    {
        parent::__construct();
        $this->entityManager = $entityManager;
        $this->courseManager = $courseManager;
    }

    protected function configure(): void
    {
        $this
            ->addOption("force", "f", InputOption::VALUE_NONE, "A passer pour valider les modifications")
            ;
    }

    protected function execute(InputInterface $input, OutputInterface $output): int
    {
        $this->participationRepo = $this->entityManager->getRepository(Participation::class);

        $queryBuilder = $this->entityManager
            ->createQueryBuilder()
            ->select('p')
            ->from(Participation::class, 'p');

        $participations = $queryBuilder
            ->innerJoin('p.formation', 'f')
            ->where($queryBuilder->expr()->gte('f.startDate', ':start'))->setParameter(':start', "2022-01-01 00:00:00")
            ->andWhere('p.archived = false')
            ->andWhere('p.nextModule IS NOT NULL')
            ->andWhere('p.nextModule = :reunion')
            ->setParameter('reunion', "reunion")
            ->getQuery()
            ->getResult()
            ;

        $output->writeln("Participations : " . count($participations));

        /** @var Participation $participation */
        foreach($participations as $participation) {
            $currentNext = $participation->getNextModule();
            $courseDetail = $this->courseManager->getCourseDetail($participation);
            $nextModule = $this->courseManager->getNextNotCompletedModule($participation, $courseDetail);
            $participation->setNextModule($nextModule["id"] ?? null);

            if ($participation->getNextModule() !== $currentNext) {
                $output->writeln(sprintf("%s : %s -> %s", $participation->getId() ?? "NULL", $currentNext, $participation->getNextModule() ?? "NULL"));

                if ($input->getOption("force")) {
                    $this->entityManager->persist($participation);
                    $this->entityManager->flush();
                }
            }
        }
        return Command::SUCCESS;
    }
}
