<?php

namespace Eduprat\AdminBundle\Command;

use Symfony\Component\Console\Attribute\AsCommand;
use Doctrine\ORM\EntityManagerInterface;
use Eduprat\DomainBundle\Entity\Formation;
use Eduprat\DomainBundle\Entity\Programme;
use Eduprat\DomainBundle\Entity\UnityFormation;
use Symfony\Component\Console\Command\Command;
use Symfony\Component\Console\Input\InputInterface;
use Symfony\Component\Console\Input\InputOption;
use Symfony\Component\Console\Output\OutputInterface;
use Symfony\Component\Console\Style\SymfonyStyle;
use Symfony\Contracts\Translation\TranslatorInterface;

#[AsCommand(name: 'eduprat:init_descriptif_hors_connexion_audit', description: '[Oneshot] Init descriptif hors connexion audit')]
class InitDescriptifHeureHorsLigneAuditCommand extends Command
{
    public function __construct(
        private EntityManagerInterface $entityManager,
        private TranslatorInterface $translator
    ) {
        parent::__construct();
    }

    protected function configure(): void
    {
        $this
            ->addOption("force", "f", InputOption::VALUE_NONE, "A passer pour valider les modifications")
        ;
    }

    protected function execute(InputInterface $input, OutputInterface $output): int
    {
        $io = new SymfonyStyle($input, $output);

        $programmes = $this->entityManager
            ->createQueryBuilder()
            ->select('p')
            ->from(Programme::class, 'p')
            ->where('p.year >= :year')->setParameter('year', "2023")
            ->andWhere('p.sessionType = :audit')->setParameter('audit', Formation::TYPE_AUDIT)
            ->andWhere('p.formType = :auditFormType')->setParameter('auditFormType', Formation::FORM_TYPE_AUDIT)
            ->getQuery()
            ->getResult()
        ;

        if ($input->getOption("force")) {
            $io->progressStart(count($programmes));
            $cpt = 0;
            foreach ($programmes as $programme) {
                /** @var UnityFormation $unity */
                foreach ($programme->getUnities() as $unityIndex => $unity) {
                    if ($unityIndex === 0) {
                        if ($unity->getDescriptifOffline()) {
                            $unity->setDescriptifOffline(
                                $this->translator->trans('admin.programme.descriptifOffline.formation_audit_audit.unity1')
                                . ' ' . $unity->getDescriptifOffline()
                            );
                        } else {
                            $unity->setDescriptifOffline(
                                $this->translator->trans('admin.programme.descriptifOffline.formation_audit_audit.unity1')
                            );
                        }

                        $this->entityManager->persist($unity);
                    }
                    if ($unityIndex === 2) {
                        if ($unity->getDescriptifOffline()) {
                            $unity->setDescriptifOffline(
                                $this->translator->trans('admin.programme.descriptifOffline.formation_audit_audit.unity3')
                                . ' ' . $unity->getDescriptifOffline()
                            );
                        } else {
                            $unity->setDescriptifOffline(
                                $this->translator->trans('admin.programme.descriptifOffline.formation_audit_audit.unity3')
                            );
                        }
                        $this->entityManager->persist($unity);
                    }
                }
                if ($cpt % 100 === 0) {
                    $this->entityManager->flush();
                }
                $cpt++;
                $io->progressAdvance();
            }
            $this->entityManager->flush();
            $io->progressFinish();
        }

        if (!$input->getOption('force')) {
            $debug = [];
            /**
             * @var  $unityIndex
             * @var Programme $programme
             */
            foreach ($programmes as $programme) {
                foreach ($programme->getUnities() as $unityIndex => $unity) {
                    $debug[] = [
                        $programme->getId(),
                        $programme->getReference(),
                        $unityIndex,
                        $unity->getDescriptifOffline(),
                    ];
                }
            }
            $io->table(
                ['Id', 'reference', 'unity', 'descriptif'],
                $debug
            );
            $io->note(sprintf('%s programmes à mettre à jour', count($debug)));
            $io->note('Utiliser --force pour lancer la commande');
        }

        return Command::SUCCESS;
    }
}
