<?php
namespace Eduprat\AdminBundle\Command;

use Doctrine\ORM\EntityManagerInterface;
use Eduprat\DomainBundle\Entity\Formation;
use Symfony\Component\Console\Attribute\AsCommand;
use Symfony\Component\Console\Command\Command;
use Symfony\Component\Console\Input\InputInterface;
use Symfony\Component\Console\Output\OutputInterface;

#[AsCommand(name: 'eduprat:default_min_time_for_presentials', description: "Default for minTimeForPresentialsCommand")]
class defaultForMinTimeForPresentialsCommand extends Command
{
    /**
     * @var EntityManagerInterface
     */
    private $entityManager;

    public function __construct(EntityManagerInterface $entityManager)
    {
        $this->entityManager = $entityManager;
        parent::__construct();
    }
    protected function configure(): void
    {
    }

    protected function execute(InputInterface $input, OutputInterface $output): int
    {
        $formations = $this->findAllPresentials();
        $count = 0;
        $formationCount = count($formations);

        foreach ($formations as $formation) {
            $count++;
            $mdMinTime = $formation->getModuleMinTimes()->first();
            $mdMinTime->setFormPresession(0);
            $mdMinTime->setFormPostsession(0);
            if ($count%50 == 0) {
                $this->entityManager->flush();
                dump($count . "/" . $formationCount);
            }
        }
        $this->entityManager->flush();
        dump($count . "/" . $formationCount);
        return 0;
    }

    public function findAllPresentials(): array
    {
        $formationRepo = $this->entityManager->getRepository(Formation::class);
        $qb = $formationRepo->createQueryBuilder('f');
        return $qb
            ->select('f')
            ->leftJoin('f.programme', 'p')
            ->where('p.sessionType = :sessionType')->setParameter('sessionType', Formation::TYPE_PRESENTIELLE)
            ->getQuery()
            ->getResult()
            ;
    }
}
