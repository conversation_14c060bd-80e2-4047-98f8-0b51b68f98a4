<?php

namespace Eduprat\AdminBundle\Command;

use Eduprat\DomainBundle\Services\ConfigManager;
use Symfony\Component\Console\Attribute\AsCommand;
use Symfony\Component\Console\Command\Command;
use Symfony\Component\Console\Input\InputInterface;
use Symfony\Component\Console\Output\OutputInterface;

#[AsCommand(name: 'eduprat:init_config', description: "Initialise la configuration en bdd")]
class InitConfigCommand extends Command
{
    /** @var ConfigManager */
    private $configManager;

    protected function configure(): void
    {
    }

    public function __construct(ConfigManager $configManager)
    {
        $this->configManager = $configManager;
        parent::__construct();
    }

    protected function execute(InputInterface $input, OutputInterface $output): int
    {
        $output->writeln("Traitement en cours...");
        $this->configManager->initConfig();
        $output->writeln("Traitement terminé");
        return Command::SUCCESS;
    }
}
