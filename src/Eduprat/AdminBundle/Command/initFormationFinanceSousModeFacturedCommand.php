<?php

namespace Eduprat\AdminBundle\Command;

use Doctrine\ORM\EntityManagerInterface;
use Eduprat\DomainBundle\Entity\Formation;
use Symfony\Component\Console\Attribute\AsCommand;
use Symfony\Component\Console\Command\Command;
use Symfony\Component\Console\Input\InputInterface;
use Symfony\Component\Console\Output\OutputInterface;
use Symfony\Component\VarDumper\VarDumper;

#[AsCommand(name: 'eduprat:init_formation_finance_sous_mode_factured', description: "Initialise le champ de facturation des sous mode de financement")]
class initFormationFinanceSousModeFacturedCommand extends Command
{
    private $formationRepo;

    /**
     * @var EntityManagerInterface
     */
    private $entityManager;


    public function __construct(EntityManagerInterface $entityManager)
    {
        parent::__construct();
        $this->entityManager = $entityManager;
    }

    protected function configure(): void
    {
    }

    protected function execute(InputInterface $input, OutputInterface $output): int
    {
        $this->formationRepo = $this->entityManager->getRepository(Formation::class);

        $formations = $this->formationRepo->findAll();

        $compt = 0;

        foreach ($formations as $formation) {
            $formation->refreshFinanceSousModeFactures();
            $formation->setOneFinanceSousModeFactured(false);
            $this->entityManager->persist($formation);

            if ($compt % 5 == 0) {
                VarDumper::dump($compt .'/'. count($formations));
            }
            $compt++;
        }
        $this->entityManager->flush();
        return Command::SUCCESS;
    }
}
