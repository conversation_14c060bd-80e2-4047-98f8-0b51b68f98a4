<?php
namespace Eduprat\AdminBundle\Command;

use Symfony\Component\Console\Attribute\AsCommand;
use Doctrine\ORM\EntityManagerInterface;
use Eduprat\DomainBundle\Entity\Formation;
use Symfony\Component\Console\Command\Command;
use Symfony\Component\Console\Input\InputInterface;
use Symfony\Component\Console\Output\OutputInterface;
#[AsCommand(name: 'eduprat:InitMissingAttestationFields')]
class InitMissingAttestationFieldsCommand extends Command
{
    /**
     * @var EntityManagerInterface
     */
    private $entityManager;

    public function __construct(EntityManagerInterface $entityManager)
    {
        $this->entityManager = $entityManager;
        parent::__construct();
    }
    protected function configure(): void
    {
    }

    protected function execute(InputInterface $input, OutputInterface $output): int
    {

        $formations = $this->findAllFormations();
        $count = count($formations);
        $compt = 0;
        dump($compt . "/" . $count);
        foreach($formations as $formation) {
            $formation->updateAttestationMissingDates();
            $compt++;
            if ($compt % 200 == 0) {
                $this->entityManager->flush();
                dump($compt . "/" . $count);
            }
        }
        $this->entityManager->flush();
        dump($compt . "/" . $count);
        return 0;
    }
   public function findAllFormations(): array
   {
       $repoFormation = $this->entityManager->getRepository(Formation::class);
       $qb = $repoFormation->createQueryBuilder('f');
       return $qb
           ->select('f')
           ->where('f.archived = false')
           ->andWhere('f.attestationMissingDate is null')
           ->getQuery()
           ->getResult()
           ;
   }
}
