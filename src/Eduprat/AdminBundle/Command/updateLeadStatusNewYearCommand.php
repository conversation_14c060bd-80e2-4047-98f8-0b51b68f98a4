<?php

namespace Eduprat\AdminBundle\Command;

use Doctrine\ORM\EntityManagerInterface;
use Eduprat\DomainBundle\Entity\Participant;
use Symfony\Component\Console\Attribute\AsCommand;
use Symfony\Component\Console\Command\Command;
use Symfony\Component\Console\Input\InputInterface;
use Symfony\Component\Console\Output\OutputInterface;
use Symfony\Component\VarDumper\VarDumper;

#[AsCommand(name: 'eduprat:update_participant_new_year', description: "Désactive le status de lead à la nouvelle année")]
class updateLeadStatusNewYearCommand extends Command
{
    /**
     * @var EntityManagerInterface
     */
    private $entityManager;

    public function __construct(EntityManagerInterface $entityManager)
    {
        parent::__construct();
        $this->entityManager = $entityManager;
    }

    protected function configure(): void
    {
    }

    protected function execute(InputInterface $input, OutputInterface $output): int
    {
        $this->participationRepo = $this->entityManager->getRepository(Participant::class);

        $queryBuilder = $this->entityManager
            ->createQueryBuilder()
            ->select('p')
            ->from(Participant::class, 'p');

        $participants = $queryBuilder
            ->where($queryBuilder->expr()->gte('p.leadStatus', ':leadStatus'))->setParameter(':leadStatus', "Actif")
            ->getQuery()
            ->getResult()
            ;

        $output->writeln("Participants : " . count($participants));

        /** @var Participation $participation */
        foreach($participants as $participant) {
            VarDumper::dump($participant->getFullname());
            $participant->setLeadStatus("Innactif");
            $this->entityManager->persist($participant);
            $this->entityManager->flush();
        }
        return Command::SUCCESS;
    }
}
