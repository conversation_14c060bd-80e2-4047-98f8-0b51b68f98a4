<?php

namespace Eduprat\AdminBundle\Command;

use Doctrine\ORM\EntityManagerInterface;
use Doctrine\ORM\Query\Expr\Join;
use Eduprat\DomainBundle\Entity\Participation;
use Eduprat\DomainBundle\Entity\ParticipationLog;
use Symfony\Component\Console\Attribute\AsCommand;
use Symfony\Component\Console\Command\Command;
use Symfony\Component\Console\Input\InputInterface;
use Symfony\Component\Console\Output\OutputInterface;

#[AsCommand(name: 'eduprat:correction:classe_virtuelle', description: "Applique des corrections à la base de données sur les participations liées aux classes virtuelles")]
class CorrectionParticipationClasseVirtuelleCommand extends Command
{
    /**
     * @var EntityManagerInterface
     */
    private $entityManager;

    public function __construct(EntityManagerInterface $entityManager)
    {
        $this->entityManager = $entityManager;
        parent::__construct();
    }

    protected function configure(): void
    {
    }

    protected function execute(InputInterface $input, OutputInterface $output): int
    {
        $repo = $this->entityManager->getRepository(Participation::class);
        $participations = $repo
            ->createQueryBuilder('p')
            ->innerJoin(ParticipationLog::class, 'pl', Join::WITH, 'pl.participation = p.id')
            ->where('pl.action = :cv')
            ->setParameter('cv', 'classe_virtuelle')
            ->getQuery()
            ->getResult();

        /** @var Participation $participation */
        foreach ($participations as $participation) {
            $minDate = null;
            $maxDate = null;
            dump($participation->getId());
            foreach ($participation->getParticipationLogs() as $pl) {
                if ($pl->getAction() == ParticipationLog::ACTION_CLASSE_VIRTUELLE) {
                    if ($minDate === null || $minDate > $pl->getStartDate()) {
                        $minDate = $pl->getStartDate();
                    }
                    if ($maxDate === null || $maxDate < $pl->getEndDate()) {
                        $maxDate = $pl->getEndDate();
                    }
                }
            }
            $participation->setStartedAtZoom($minDate);
            $participation->setFinishedAtZoom($maxDate);
            $this->entityManager->flush($participation);
        }
        return Command::SUCCESS;
    }
}
