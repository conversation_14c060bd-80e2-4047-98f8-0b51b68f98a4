<?php
namespace Eduprat\AdminBundle\Command;

use Symfony\Component\Console\Attribute\AsCommand;
use Doctrine\ORM\EntityManagerInterface;
use Eduprat\DomainBundle\Entity\Formation;
use Symfony\Component\Console\Command\Command;
use Symfony\Component\Console\Input\InputInterface;
use Symfony\Component\Console\Output\OutputInterface;
#[AsCommand(name: 'eduprat:midcourse_auto_paid_coordinator_command')]
class MidCourseAutoPaidCoordinatorCommand extends Command
{
    /**
     * @var EntityManagerInterface
     */
    private $entityManager;

    public function __construct(EntityManagerInterface $entityManager)
    {
        $this->entityManager = $entityManager;
        parent::__construct();
    }
    protected function configure(): void
    {
    }

    protected function execute(InputInterface $input, OutputInterface $output): int
    {

        $formations = $this->findAllPluriannual();
        foreach($formations as $formation) {
            if ($formation->isPluriannuelle()) {
                foreach ($formation->getCoordinators() as $coordinator) {
                    if ($coordinator->isPaid()) {
                        $coordinator->setIsPaidMidCourse(true);
                        $coordinator->setIsPaid(false);
                        $coordinator->setPaidMidCourseDate($coordinator->getPaidDate());
                        $coordinator->setPaidDate(null);
                    }
                }
            }
        }
        $this->entityManager->flush();
        return 0;
    }
   public function findAllPluriannual(): array
   {
       $formationRepo = $this->entityManager->getRepository(Formation::class);
       $qb = $formationRepo->createQueryBuilder('f');
       return $qb
           ->select('f')
           ->where($qb->expr()->neq('YEAR(f.openingDate)', 'YEAR(f.closingDate)'))
           ->getQuery()
           ->getResult()
           ;
   }
}
