<?php

declare(strict_types=1);

namespace Eduprat\AdminBundle\Command;

use Doctrine\DBAL\Connection;
use Symfony\Component\Console\Attribute\AsCommand;
use Symfony\Component\Console\Command\Command;
use Symfony\Component\Console\Input\InputArgument;
use Symfony\Component\Console\Input\InputInterface;
use Symfony\Component\Console\Output\OutputInterface;
use Symfony\Component\Console\Style\SymfonyStyle;
use Symfony\Contracts\Service\Attribute\Required;

#[AsCommand(name: 'eduprat:fix_speciality_name_in_programme', description: 'Corrige le nom d\'une spécialité dans les programmes')]
class FixSpecialityNameCommand extends Command
{
    private Connection $entityManager;

    #[Required]
    public function withEntityManager(Connection $entityManager): void
    {
        $this->entityManager = $entityManager;
    }

    protected function configure(): void
    {
        $this
            ->addArgument('oldName', InputArgument::REQUIRED, 'Le nom de la spécialité à corriger')
            ->addArgument('newName', InputArgument::REQUIRED, 'Le nouveau nom de la spécialité')
        ;

    }

    protected function execute(InputInterface $input, OutputInterface $output): int
    {
        $io = new SymfonyStyle($input, $output);

        $oldName = $input->getArgument('oldName');
        $newName = $input->getArgument('newName');
        $oldTextJsonEncoded = str_replace("'", "\\'", json_encode($oldName));
        $newTextJsonEncoded = str_replace("'", "\\'", json_encode($newName));

        $cleanOldName = str_replace("'", "\\'", $oldName);
        $cleanNewName = str_replace("'", "\\'", $newName);

        $this->fixProgramme($oldTextJsonEncoded, $newTextJsonEncoded, $io);

        $this->fixParticipant($cleanNewName, $cleanOldName, $io);

        return Command::SUCCESS;
    }

    public function fixParticipant(mixed $cleanNewName, mixed $cleanOldName, SymfonyStyle $io): void
    {
        $sql = "UPDATE participant
                SET speciality = '$cleanNewName',
                    toUpdateSib = 1
                WHERE speciality = '$cleanOldName'";
        $io->info($sql);
        $nbLines = $this->entityManager->executeStatement($sql);
        $io->success(sprintf('%s participants mis à jour', $nbLines));
    }

    public function fixProgramme(array|false|string $oldTextJsonEncoded, array|false|string $newTextJsonEncoded, SymfonyStyle $io): void
    {
        $sql = "UPDATE programme
                SET specialities = REPLACE(specialities, '$oldTextJsonEncoded', '$newTextJsonEncoded')
                WHERE specialities like '%$oldTextJsonEncoded%'";
        $io->info($sql);
        $nbLines = $this->entityManager->executeStatement($sql);
        $io->success(sprintf('%s programmes mis à jour', $nbLines));
    }
}
