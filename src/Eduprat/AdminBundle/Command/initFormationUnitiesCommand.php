<?php

namespace Eduprat\AdminBundle\Command;

use Doctrine\ORM\EntityManagerInterface;
use Eduprat\DomainBundle\Entity\Programme;
use Eduprat\DomainBundle\Entity\UnitySession;
use Eduprat\DomainBundle\Entity\UnitySessionDate;
use Symfony\Component\Console\Attribute\AsCommand;
use Symfony\Component\Console\Command\Command;
use Symfony\Component\Console\Input\InputInterface;
use Symfony\Component\Console\Output\OutputInterface;

#[AsCommand(name: 'eduprat:init_session_unities')]
class initFormationUnitiesCommand extends Command
{
    /**
     * @var EntityManagerInterface
     */
    private $entityManager;

    public function __construct(EntityManagerInterface $entityManager)
    {
        parent::__construct();
        $this->entityManager = $entityManager;
    }

    protected function configure(): void
    {
    }

    protected function execute(InputInterface $input, OutputInterface $output): int
    {
        $programmes = $this->entityManager
            ->createQueryBuilder()
            ->select('p')
            ->from(Programme::class, 'p')
            ->where('p.year = :year')->setParameter('year', "2023")
            ->getQuery()
            ->getResult()
            ;

        foreach ($programmes as $programme) {
            foreach ($programme->getFormations() as $formation) {
                foreach ($programme->getUnities() as $unityIndex => $unity) {
                    foreach($formation->getUnities() as $unityFormation) {
                        $formation->removeUnity($unityFormation);
                    }
                    $unitySession = new UnitySession();
                    $unitySession->setFormation($formation);
                    if ($unity->isOnSite() || $unity->isVirtuelle()) {
                        if ($formation->getStartDate()->format('d/m/Y') != $formation->getEndDate()->format('d/m/Y')) {
                            $unity->setNbDays(2);
                            $unitySessionDate = new UnitySessionDate();
                            $unitySessionDate->setStartDate(clone($formation->getStartDate()));
                            $unitySessionDate->setEndDate(clone($formation->getStartDate()));
                            $unitySessionDate->getEndDate()->setTime($formation->getEndDate()->format("H"), $formation->getEndDate()->format("i"), 0);
                            $unitySessionDate->setUnitySession($unitySession);
                            $unitySession->addUnitySessionDate($unitySessionDate);
                            $this->entityManager->persist($unitySessionDate);

                            if ($programme->getId() == 14925) {
                                $unity->setNbDays(3);
                                $unitySessionDate = new UnitySessionDate();
                                $unitySessionDate->setStartDate(clone($formation->getStartDate())->modify("+ 1 day"));
                                $unitySessionDate->setEndDate(clone($unitySessionDate->getStartDate()));
                                $unitySessionDate->getEndDate()->setTime($formation->getEndDate()->format("H"), $formation->getEndDate()->format("i"), 0);
                                $unitySessionDate->setUnitySession($unitySession);
                                $unitySession->addUnitySessionDate($unitySessionDate);
                                $this->entityManager->persist($unitySessionDate);
                            }

                            $unitySessionDate = new UnitySessionDate();
                            $unitySessionDate->setStartDate(clone($formation->getEndDate()));
                            $unitySessionDate->setEndDate(clone($formation->getEndDate()));
                            $unitySessionDate->getStartDate()->setTime($formation->getStartDate()->format("H"), $formation->getStartDate()->format("i"), 0);
                            $unitySessionDate->setUnitySession($unitySession);
                            $unitySession->addUnitySessionDate($unitySessionDate);
                            $this->entityManager->persist($unitySessionDate);
                        } else {
                            $unitySessionDate = new UnitySessionDate();
                            $unitySessionDate->setStartDate($formation->getStartDate());
                            $unitySessionDate->setEndDate($formation->getEndDate());
                            $unitySessionDate->setUnitySession($unitySession);
                            $unitySession->addUnitySessionDate($unitySessionDate);
                            $this->entityManager->persist($unitySessionDate);
                        }
                    } else {
                        $unitySession->setOpeningDate($unityIndex == 0 ? $formation->getOpeningDate() : $formation->getEndDate()->modify("+ 28 day"));
                        $unitySession->setClosingDate($unityIndex == 0 ? $formation->getStartDate() : $formation->getClosingDate());
                        if (!$programme->getReunionUnityPosition() && $unityIndex == 1) {
                            // pour les elearning qui n'ont pas de présentiel
                            $unitySession->setOpeningDate($formation->getStartDate());
                            $unitySession->setClosingDate($formation->getEndDate());
                        }
                    }
                    $this->entityManager->persist($unitySession);
                    $this->entityManager->flush();
                }
            }
        }
        return Command::SUCCESS;
    }
}
