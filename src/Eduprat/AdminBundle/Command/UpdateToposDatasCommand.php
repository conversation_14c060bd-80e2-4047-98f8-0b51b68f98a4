<?php

namespace Eduprat\AdminBundle\Command;

use Doctrine\ORM\EntityManager;
use Doctrine\ORM\EntityManagerInterface;
use Eduprat\AdminBundle\Services\EvaluationReporting;
use Eduprat\DomainBundle\Entity\Coordinator;
use Eduprat\DomainBundle\Entity\Formateur;
use Eduprat\DomainBundle\Entity\Programme;
use Eduprat\DomainBundle\Repository\ProgrammeRepository;
use Symfony\Component\Console\Attribute\AsCommand;
use Symfony\Component\Console\Command\Command;
use Symfony\Component\Console\Helper\ProgressBar;
use Symfony\Component\Console\Input\InputInterface;
use Symfony\Component\Console\Output\OutputInterface;

#[AsCommand(name: 'eduprat:update_topos', description: "Mets à jour les données concernant les topos")]
class UpdateToposDatasCommand extends Command
{
    /** @var ProgrammeRepository */
    private $repo;

    /** @var EvaluationReporting */
    private $evaluationReporting;

    /** @var EntityManager */
    private $entityManager;

    public function __construct(EntityManagerInterface $entityManager, EvaluationReporting $evaluationReporting)
    {
        parent::__construct();
        $this->entityManager = $entityManager;
        $this->evaluationReporting = $evaluationReporting;
    }

    protected function configure(): void
    {
    }

    protected function execute(InputInterface $input, OutputInterface $output): int
    {
        $this->repo = $this->entityManager->getRepository(Programme::class);
        $programmes = $this->repo->findIds();

        $progressBar = new ProgressBar($output, count($programmes));
        $progressBar->start();

        foreach (array_column($programmes, "id") as $index => $id) {
            /** @var Programme $programme */
            $programme = $this->repo->find($id);
            $result = $this->evaluationReporting->getProgrammeDetail($programme);
            $programme->setEvaluationFormerAnswersCount($result["ROLE_FORMER"]["count"]);
            $programme->setEvaluationFormerAnswersSum($result["ROLE_FORMER"]["sum"]);
            $programme->setEvaluationParticipantAnswersCount($result["ROLE_PARTICIPANT"]["count"]);
            $programme->setEvaluationParticipantAnswersSum($result["ROLE_PARTICIPANT"]["sum"]);
            $this->entityManager->persist($programme);

            foreach ($programme->getFormations() as $formation) {
                /** @var Coordinator $coordinator */
                foreach ($formation->getCoordinators() as $coordinator) {
                    $result = $this->evaluationReporting->getCoordinatorDetail($coordinator);
                    $coordinator->setEvaluationFormerAnswersCount($result["ROLE_FORMER"]["count"]);
                    $coordinator->setEvaluationFormerAnswersSum($result["ROLE_FORMER"]["sum"]);
                    $coordinator->setEvaluationParticipantAnswersCount($result["ROLE_PARTICIPANT"]["count"]);
                    $coordinator->setEvaluationParticipantAnswersSum($result["ROLE_PARTICIPANT"]["sum"]);
                    $this->entityManager->persist($coordinator);
                }

                /** @var Formateur $former */
                foreach ($formation->getFormateurs() as $former) {
                    $result = $this->evaluationReporting->getFormerDetail($former);
                    $former->setEvaluationCoordinatorAnswersCount($result["ROLE_COORDINATOR"]["count"]);
                    $former->setEvaluationCoordinatorAnswersSum($result["ROLE_COORDINATOR"]["sum"]);
                    $former->setEvaluationParticipantAnswersCount($result["ROLE_PARTICIPANT"]["count"]);
                    $former->setEvaluationParticipantAnswersSum($result["ROLE_PARTICIPANT"]["sum"]);
                    $this->entityManager->persist($former);
                }

            }

            if ($index % 10 === 0) {
                $this->entityManager->flush();
                $this->entityManager->clear();
            }

            $progressBar->advance();
        }

        $this->entityManager->flush();
        $this->entityManager->clear();

        $progressBar->finish();
        return Command::SUCCESS;
    }
}
