<?php

namespace Eduprat\AdminBundle\Command;

use Doctrine\ORM\EntityManagerInterface;
use Eduprat\AdminBundle\Entity\Person;
use Eduprat\DomainBundle\Entity\Participant;
use Eduprat\DomainBundle\Entity\Programme;
use Eduprat\DomainBundle\Services\SendinBlueManager;
use Symfony\Component\Console\Attribute\AsCommand;
use Symfony\Component\Console\Command\Command;
use Symfony\Component\Console\Input\InputInterface;
use Symfony\Component\Console\Output\OutputInterface;
use Symfony\Component\Console\Helper\ProgressBar;

#[AsCommand('eduprat:leads:disable')]
class DisableLeadsCommand extends Command
{
    /**
     * @var EntityManagerInterface
     */
    private $entityManager;


    public function __construct(EntityManagerInterface $entityManager)
    {
        parent::__construct();
        $this->entityManager = $entityManager;
    }

    /**
     * {@inheritdoc}
     */
    protected function configure(): void
    {
        $this
            ->setDescription('Passage de lead actif => innactif le premier jour de chaque année')
        ;
    }

    /**
     * {@inheritdoc}
     */
    protected function execute(InputInterface $input, OutputInterface $output): int
    {
        
        $leadsOn = $this->findAllLeadsOn();

        $progressBar = new ProgressBar($output, count($leadsOn));
        $progressBar->start();
        

        foreach ($leadsOn as $index => $lead) {
           $lead->setLeadStatus(Participant::LEAD_OFF_VALUE);

            if ($index % 10 === 0) {
                $this->entityManager->flush();
            }

            $progressBar->advance();
        }
        $this->entityManager->flush();

        $progressBar->finish();
        return Command::SUCCESS;
    }

    public function findAllLeadsOn(): array
   {
       $participantRepository = $this->entityManager->getRepository(Participant::class);
       $qb = $participantRepository->createQueryBuilder('p');
       return $qb
           ->select('p')
           ->where('p.leadStatus = :leadStatus')->setParameter('leadStatus', Participant::LEAD_ON_VALUE)
           ->getQuery()
           ->getResult()
           ;
   }

}
