Eduprat\AdminBundle\Entity\Person:
    properties:
        roles:
            - NotBlank:
                groups: ['Default']
        email:
            - NotBlank:
                groups: ['Default']
            - Email:
#                checkMX: true
                groups: ['Default']
        password:
            - Expression:
                expression: "this.getId() != null or value !== ''"
                groups: ['ROLE_WEBMASTER', 'ROLE_COORDINATOR', 'ROLE_SUPERVISOR']
        plainPassword:
            - NotBlank:
                groups: ['password_edit']
        firstname:
            - NotBlank:
                groups: ['Default']
        lastname:
            - NotBlank:
                groups: ['Default']
        zipCode:
            - Length:
                min: 5
                max: 5
                exactMessage: global.zipCode
                groups: ['ROLE_FORMER', 'ROLE_FORMER_PHARMACIE', 'ROLE_COORDINATOR', 'ROLE_SUPERVISOR']
            - Regex:
                pattern: '/^[0-9]*$/'
                match:   true
                message: global.zipCode
                groups: ['ROLE_FORMER', 'ROLE_FORMER_PHARMACIE', 'ROLE_COORDINATOR', 'ROLE_SUPERVISOR']
        rpps:
            - Length:
                min: 11
                max: 11
                exactMessage: global.rpps
                groups: ['ROLE_FORMER', 'ROLE_FORMER_PHARMACIE']
            - Regex:
                pattern: '/^[0-9]*$/'
                match:   true
                message: global.rpps
                groups: ['ROLE_FORMER', 'ROLE_FORMER_PHARMACIE']
        adeli:
            - Length:
                min: 9
                max: 9
                exactMessage: global.adeli
                groups: ['ROLE_FORMER', 'ROLE_FORMER_PHARMACIE']
            - Regex:
                pattern: '/^[A-Z0-9]*$/'
                match:   true
                message: global.adeli
                groups: ['ROLE_FORMER', 'ROLE_FORMER_PHARMACIE']
        phone:
            - Length:
                min: 10
                max: 10
                exactMessage: global.phone
            - Regex:
                pattern: '/0[1-9]{1}[0-9]{8}/'
                match:   true
                message: global.phone
                groups: ['Default']
        siret:
            - Length:
                min: 14
                max: 14
                groups: ['ROLE_FORMER', 'ROLE_FORMER_PHARMACIE', 'ROLE_COORDINATOR', 'ROLE_SUPERVISOR']
            - Regex:
                pattern: '/^[0-9]*$/'
                match:   true
                groups: ['ROLE_FORMER', 'ROLE_FORMER_PHARMACIE', 'ROLE_COORDINATOR', 'ROLE_SUPERVISOR']
        edupratFormer:
            - EqualTo:
                value: false
                groups: ['ROLE_WEBMASTER', 'ROLE_COORDINATOR', 'ROLE_SUPERVISOR']
        supervisor:
            - NotNull:
                groups: ['ROLE_COORDINATOR']
        cvFile:
            - File:
                maxSize: 2048k
                mimeTypes: ['image/jpeg', 'application/pdf', 'application/x-pdf']
                groups: ['cv']
        dliFile:
            - File:
                maxSize: 2048k
                mimeTypes: ['image/jpeg', 'application/pdf', 'application/x-pdf']
                groups: ['dli']
Eduprat\DomainBundle\Entity\Coordinator:
    properties:
        honorary:
            - Type:
                type: float
        restaurationHonorary:
            - Type:
                type: float
Eduprat\DomainBundle\Entity\Programme:
    properties:
        title:
            - NotBlank: ~
        reference:
            - NotBlank: ~
        # zipCode:
        #     - Length:
        #         min: 5
        #         max: 5
        #         exactMessage: global.zipCode
        #     - Regex:
        #         pattern: '/^[0-9]*$/'
        #         match:   true
        #         message: global.zipCode
        # startDate:
        #     - NotBlank: ~
        #     - DateTime: ~
        # endDate:
        #     - NotBlank: ~
        #     - DateTime: ~
Eduprat\DomainBundle\Entity\Formation:
    properties:
        sessionNumber:
            - Regex:
                  pattern: '/^[^ ]*$/'
                  htmlPattern: false
                  match: true
                  message: "Le numéro de session ne doit pas contenir d'espace"
        financeModes:
            - NotBlank: ~
        financeSousModes:
            - NotBlank: ~
        factureFormerFile:
            - File:
                uploadFormSizeErrorMessage: formation.imageFile_maxSize
                mimeTypes: ['application/pdf','application/x-pdf']
        zipCode:
            - Length:
                min: 5
                max: 5
                exactMessage: global.zipCode
            - Regex:
                pattern: '/^[0-9]*$/'
                match:   true
                message: global.zipCode
        zoomLink:
            - Url:
                  requireTld: true
Eduprat\DomainBundle\Entity\FinanceSousMode:
    properties:
        zipCode:
            - Length:
                min: 5
                max: 5
                exactMessage: global.zipCode
            - Regex:
                pattern: '/^[0-9]*$/'
                match:   true
                message: global.zipCode
        rpsZip:
            - Length:
                min: 5
                max: 5
                exactMessage: global.zipCode
            - Regex:
                pattern: '/^[0-9]*$/'
                match:   true
                message: global.zipCode
        rpsPhone:
            - Length:
                min: 10
                max: 10
                exactMessage: global.phone
            - Regex:
                pattern: '/0[1-9]{1}[0-9]{8}/'
                match:   true
                message: global.phone
        rpsMail:
            - Email:
#                checkMX: true
                groups: ['Default']

Eduprat\DomainBundle\Entity\Participant:
    properties:
        civility:
            - Choice:
                callback: [ Eduprat\DomainBundle\Form\ParticipantType, getCivilities ]
                message: "Civilité : {{ value }} n'est pas une valeur valide"
        category:
            - NotBlank :
                groups: ['Default']
                message: "Catégorie Professionnelle : ne doit pas être vide"
            - Choice:
                callback: [ Eduprat\DomainBundle\Form\ProgrammeType, getCategories ]
                message: "Catégorie Professionnelle : {{ value }} n'est pas une valeur valide"
        speciality:
            - NotBlank :
                groups: ['Default']
                message: "Spécialité : ne doit pas être vide"
            - Choice:
                callback: [ Eduprat\DomainBundle\Form\ProgrammeType, getFlatSpecialities ]
                message: "Spécialité : {{ value }} n'est pas une valeur valide"
        zipCode:
            - Length:
                min: 5
                max: 5
                exactMessage: global.zipCode
            - Regex:
                pattern: '/^[0-9]*$/'
                match:   true
                message: global.zipCode
        email:
            - Email: ~
        rpps:
            - Length:
                min: 11
                max: 11
                exactMessage: global.rpps
            - Regex:
                pattern: '/^[0-9]*$/'
                match:   true
                message: global.rpps
        adeli:
            - Length:
                min: 9
                max: 9
                exactMessage: global.adeli
            - Regex:
                pattern: '/^[A-Z0-9]*$/'
                match:   true
                message: global.adeli

Eduprat\AdminBundle\Entity\ParticipantSearch:
    properties:
        zipCode:
            - Length:
                  max: 5
                  exactMessage: global.zipCode
            - Regex:
                  pattern: '/^[0-9]*$/'
                  match:   true
                  message: global.zipCode
        rpps:
            - Length:
                  max: 11
                  exactMessage: global.rpps
            - Regex:
                  pattern: '/^[0-9]*$/'
                  match:   true
                  message: global.rpps
        adeli:
            - Length:
                  max: 9
                  exactMessage: global.adeli
            - Regex:
                  pattern: '/^[A-Z0-9]*$/'
                  match:   true
                  message: global.adeli
