<?php

namespace Eduprat\AdminBundle\Controller;

use Symfony\Component\ExpressionLanguage\Expression;
use Symfony\Component\Security\Http\Attribute\IsGranted;
use Symfony\Component\HttpFoundation\Response;
use Symfony\Component\HttpFoundation\RedirectResponse;
use Doctrine\DBAL\Exception\ForeignKeyConstraintViolationException;
use Doctrine\ORM\EntityManagerInterface;
use Eduprat\AdminBundle\Exception\NeedRedirectException;
use Eduprat\AdminBundle\Form\SelectFormationCreateType;
use Eduprat\AdminBundle\Http\CsvFileResponse;
use Eduprat\AdminBundle\Model\SelectFormationCreate;
use Eduprat\AdminBundle\Services\CoordinatorHonorary;
use Eduprat\AdminBundle\Services\EvaluationCoordinatorByCoordinatorManager;
use Eduprat\AdminBundle\Services\EvaluationFormerByCoordinatorManager;
use Eduprat\AdminBundle\Services\EvaluationProgrammeManager;
use Eduprat\AdminBundle\Services\CsvBilanExport;
use Eduprat\AdminBundle\Entity\Person;
use Eduprat\AdminBundle\Services\ProgrammeBuilderInterface;
use Eduprat\AdminBundle\Services\ProgrammeVfcBuilder;
use Eduprat\AdminBundle\Services\ProgrammeTcsBuilder;
use Eduprat\AuditBundle\Services\ParticipationAccessManager;
use Eduprat\DomainBundle\Controller\EdupratController;
use Eduprat\DomainBundle\Entity\AbstractProgrammeFile;
use Eduprat\DomainBundle\Entity\DocumentsPedagogiquesFiles;
use Eduprat\DomainBundle\Entity\Formateur;
use Eduprat\DomainBundle\Entity\Formation;
use Eduprat\DomainBundle\Entity\Programme;
use Eduprat\DomainBundle\Entity\Coordinator;
use Eduprat\DomainBundle\Entity\ProgrammeSearch;
use Eduprat\DomainBundle\Form\FormationType;
use Eduprat\DomainBundle\Form\ProgrammeType;
use Eduprat\DomainBundle\Form\ProgrammeSearchType;
use Eduprat\DomainBundle\Entity\EvaluationFormationAnswer;
use Eduprat\DomainBundle\Entity\EvaluationCoordinator;
use Eduprat\DomainBundle\Entity\EvaluationFormerByCoordinator;
use Eduprat\DomainBundle\Entity\EvaluationProgramme;
use Eduprat\AuditBundle\Services\EvaluationFormationManager;
use Eduprat\DomainBundle\Entity\Competence;
use Eduprat\DomainBundle\Entity\Connaissance;
use Eduprat\DomainBundle\Entity\PriseEnCharge;
use Eduprat\DomainBundle\Entity\Tag;
use Eduprat\DomainBundle\Entity\TopoProgrammeFiles;
use Eduprat\DomainBundle\Entity\ToolProgrammeFiles;
use Eduprat\DomainBundle\Services\EmailSender;
use Eduprat\DomainBundle\Services\ProgrammeAssocieManager;
use Symfony\Component\Form\FormError;
use Symfony\Component\Form\FormInterface;
use Symfony\Component\HttpFoundation\File\UploadedFile;
use Symfony\Component\HttpFoundation\Request;
use Symfony\Component\Form\Extension\Core\Type\SubmitType;
use Symfony\Component\HttpFoundation\JsonResponse;
use Symfony\Component\Routing\Attribute\Route;
use Symfony\Component\Form\Extension\Core\Type\FormType;
use Symfony\Component\Form\FormFactoryInterface;
use Symfony\Component\Form\FormBuilderInterface;
use Symfony\Component\HttpKernel\Exception\NotFoundHttpException;
use Symfony\Contracts\Translation\TranslatorInterface;
use Vich\UploaderBundle\Mapping\PropertyMappingFactory;
use Vich\UploaderBundle\Storage\StorageInterface;
use Symfony\Component\HttpFoundation\BinaryFileResponse;
use Symfony\Component\HttpFoundation\ResponseHeaderBag;
use Symfony\Component\Validator\Constraints\File;
use Vich\UploaderBundle\Form\Type\VichFileType;
use Eduprat\AdminBundle\Services\ProgrammeBuilder;

/**
 * Class FormationController
 */
#[Route(path: '/formation')]
#[IsGranted(new Expression("is_granted('ROLE_COORDINATOR_LBI') or is_granted('ROLE_FORMER')"))]
class FormationController extends EdupratController
{

    /**
     * Lists all Programme entities.
     *
     * @return Response
     */
    #[Route(path: '/{page}/{year}', methods: ['GET'], defaults: ['page' => '1'], name: 'admin_programme_index', requirements: ['year' => '^(|\d{4})$', 'page' => '^\d+$', 'programme' => '^\d+$'])]
    public function index(string $page, Request $request, EntityManagerInterface $em, ParticipationAccessManager $participationAccessManager, string $year = null): Response
    {
        $search = new ProgrammeSearch();

        $search->handleRequest($request);

        if ($year !== null ) {
            $search->setYear($year);
        }

        $nbPerPage = 10;

        $programmesRepository = $em->getRepository(Programme::class);
        $count = $programmesRepository->countSearchResults($search, $this->getUser());

        $programmes = $programmesRepository->findSearchResults($search, $page, $nbPerPage, $this->getUser());

        $npages = ceil($count / $nbPerPage);
        $current = intval($page);
        $max = 10;
        $inf = max(1, intval($current - (($max == 1 ? 0 : $max - 1) / 2)));
        $sup = min($inf + ($max - 1), $npages);
        $pageRange = range($inf, $sup);
        $pagination = array(
            'nb' => $count,
            'page' => $page,
            'npages' => $npages,
            'page_range' => $pageRange,
            'records' => $programmes,
        );

        $budgetCRs = array();

        $evaluationCoordinatorByCoordinatorisCompleted = array();
        $evaluationProgrammeByCoordinatorisCompleted = array();
        $evaluationFormerByCoordinator = array();

        // foreach ($programmes as $programme) {
        //     foreach ($programme->getCoordinators() as $coordinator) {
        //         $budgetCRs[$programme->getId()][$coordinator->getId()] = $coordinatorHonoraryService->calculTotalHonorary($programme, $coordinator, false, true, false);
        //     }

        //     $budgetCRs[$programme->getId()]['displayPDF'] = $coordinatorHonoraryService->displayCoordinatorsHonoraryByProgramme($programme);

        //     if($programme->getCoordinators()) {
        //         $coordinators = $programme->getCoordinators();

        //         foreach ($coordinators as $key => $coordinator) {
        //             $evaluationCoordinatorByCoordinatorisCompleted[$programme->getId()][$coordinator->getId()] = $formManagerFactory->getEvaluationCoordinatorByCoordinatorManager($programme, $coordinator)->isCompleted();

        //             $evaluationProgrammeByCoordinatorisCompleted[$programme->getId()][$coordinator->getId()] = $formManagerFactory->getEvaluationProgrammeByCoordinatorManager($programme, $coordinator)->isCompleted();

        //             foreach($programme->getFormateursPersons() as $former) {
        //                 $evaluationFormerByCoordinator[$programme->getId()][$coordinator->getId()][$former->getId()] = $formManagerFactory->getEvaluationFormerByCoordinatorManager($programme, $former, $coordinator)->isCompleted();
        //             }
        //         }
        //     }
        // }

        $searchForm = $this->createForm(ProgrammeSearchType::class, $search, array(
            'action' => $this->generateUrl('admin_programme_dosearch'),
            'method' => 'POST',
        ));

        return $this->render('admin/programme/index.html.twig', array_merge($pagination, array(
            'search' => $search->getParams(),
            'search_form' => $searchForm->createView(),
            'open' => $request->query->get('open'),
            'budgetCRs' => $budgetCRs,
            'evaluationCoordinatorByCoordinatorisCompleted' => $evaluationCoordinatorByCoordinatorisCompleted,
            'evaluationFormerByCoordinator' => $evaluationFormerByCoordinator,
            'evaluationProgrammeByCoordinatorisCompleted' => $evaluationProgrammeByCoordinatorisCompleted,
            'accessManager' => $participationAccessManager,
            'year' => $search->getYear()
        )));
    }

    /**
     * @param Request $request
     */
    #[Route(path: '/', methods: ['POST'], name: 'admin_programme_dosearch')]
    public function doSearch(Request $request): RedirectResponse {
        $search = new ProgrammeSearch();
        $form = $this->createForm(ProgrammeSearchType::class, $search);
        $form->handleRequest($request);

        // $programmesRepository = $em->getRepository(Programme::class);

        // if ($search->getCity()) {
        //     $search->setCity($programmesRepository->findOneById($search->getCity())->getCity());
        // }

        // if ($search->getFinanceMode()) {
        //     $search->setFinanceMode($search->getFinanceMode()->getId());
        // }

        if ($form->isSubmitted() && $form->isValid()) {
            return $this->redirectToRoute('admin_programme_index', $search->getParams());
        }
        return $this->redirectToRoute('admin_programme_index');
    }

    /**
     * Lists all Programme entities.
     */
    #[Route(path: '/sessionList/{programme}', methods: ['GET'], defaults: ['programme' => null], name: 'admin_programme_session_list', requirements: ['programme' => '^\d+$'])]
    public function formationSessionList(string $programme, EntityManagerInterface $em): Response
    {
        $formationsRepository = $em->getRepository(Formation::class);

        $formations = $formationsRepository->find30ByProgramme($programme, $this->getUser());

        return $this->render('admin/programme/session_list.html.twig', array(
            'formations' => $formations,
            'idProgramme' => $programme,
            'nbOfFormation' => count($formations)
        ));
    }

    #[Route(path: '/create/select_type', name: 'admin_programme_create_select_type')]
    #[IsGranted('ROLE_WEBMASTER')]
    public function selectCreate(Request $request)
    {
        $selectFormationCreate = new SelectFormationCreate();
        $selectFormationCreateForm = $this->createForm(SelectFormationCreateType::class, $selectFormationCreate);

        $selectFormationCreateForm->handleRequest($request);
        if ($selectFormationCreateForm->isSubmitted() && $selectFormationCreateForm->isValid()) {
            return $this->redirectToRoute('admin_programme_create_guide', [
                'formationType' => $selectFormationCreate->getFormationType(),
                'presence' => $selectFormationCreate->getPresence(),
            ]);
        }
        return $this->render('admin/programme/select_create.html.twig', [
            'selectFormationCreate' => $selectFormationCreateForm,
            'combinaisons' => ProgrammeBuilder::getKeys(),
        ]);
    }


    #[Route(path: '/create', name: 'admin_programme_create')]
    #[Route(path: '/create/{formationType}/{presence}', name: 'admin_programme_create_guide')]
    #[IsGranted('ROLE_WEBMASTER')]
    public function create(Request $request, EntityManagerInterface $entityManager, StorageInterface $storage, PropertyMappingFactory $propertyMappingFactory, ProgrammeAssocieManager $programmeAssocieManager, ?string $formationType = null, ?string $presence = null) {
        if ($request->query->has("copy")) {
            $baseProgramme = $entityManager->getRepository(Programme::class)->find($request->query->get('copy'));
            if ($baseProgramme === null) {
                throw new NotFoundHttpException();
            }
            $programme = clone $baseProgramme;

            foreach($baseProgramme->getPrisesEnCharge() as $priseEnCharge) {
                $programme->addPrisesEnCharge($priseEnCharge);
            }
            foreach($baseProgramme->getTags() as $tag) {
                $programme->addTag($tag);
            }
            foreach($baseProgramme->getConnaissances() as $connaissance) {
                $newConnaissance = new Connaissance();
                $newConnaissance->setName($connaissance->getName());
                $programme->addConnaissance($newConnaissance);
            }
            foreach($baseProgramme->getCompetences() as $competence) {
                $newCompetence = new Competence();
                $newCompetence->setName($competence->getName());
                $programme->addCompetence($newCompetence);
            }
            if (!$baseProgramme->isVfc() && !$baseProgramme->isTcs()) {
                foreach($baseProgramme->getUnities() as $unity) {
                    $unityClone = clone($unity);
                    $unityClone->afterClone();
                    $programme->addUnity($unityClone);
                }
            }

            $programme->setTitle(sprintf("%s - COPIE", $programme->getTitle()));
        } else {
            $builder = $this->getProgrammeBuilder($formationType);
            $programme = $builder->buildProgramme($presence);

            $peRepo = $entityManager->getRepository(PriseEnCharge::class);

            $personnelle = $peRepo->findOneBy(['name' => 'Personnelle']);
            $dpc = $peRepo->findOneBy(['name' => 'DPC']);

            $personnelle != [] ? $programme->addPrisesEnCharge($personnelle) : "";
            $dpc != [] ? $programme->addPrisesEnCharge($dpc) : "";
        }

        $form = $this->createForm(ProgrammeType::class, $programme, array(
            "copy" => $request->query->has("copy")
        ));
        $form->handleRequest($request);

        $this->checkDuplicatedPicture($form, $programme, $request, $storage);

        if ($form->isSubmitted() && $form->isValid()) {

            $tags = $programme->getTempTags();

            $tags = explode(";", $tags);
            unset($tags[0]);


            if ($tags != []) {
                foreach($tags as $tag) {
                    $tagEntity = new Tag($tag);
                    $entityManager->persist($tagEntity);
                    $programme->addTag($tagEntity);
                }
            }

            $this->saveDuplicatedPicture($form, $programme, $request, $storage, $propertyMappingFactory);
            $this->saveDefaultAdditionalInfos($form, $programme, $storage, $propertyMappingFactory);
            $programme->updateDurations();
            $programmeAssocieManager->synchroAssociation($programme);
            $entityManager->persist($programme);
            $entityManager->flush();

            $this->flashMessages->addSuccess('formation.create.success');
            return $this->redirectToRoute('admin_programme_index', array("year" => $programme->getYear()));
        }
        return $this->render('admin/programme/create.html.twig', array(
            'form' => $form,
            'copy' => $request->query->has("copy"),
            'isGuidedFormation' => ProgrammeBuilder::isGuidedFormation($programme)
        ));
    }

    /**
     * Displays a form to edit an existing Programme entity.
     *
     * @param Request $request
     * @param Programme $programme
     * @return RedirectResponse|Response
     */
    #[Route(path: '/edit/{id}', methods: ['GET', 'POST'], name: 'admin_programme_edit')]
    #[IsGranted('ROLE_WEBMASTER')]
    public function edit(Request $request, Programme $programme, StorageInterface $storage, PropertyMappingFactory $propertyMappingFactory, FormFactoryInterface $formFactory, ProgrammeAssocieManager $programmeAssocieManager, EntityManagerInterface $entityManager)
    {
        $session = $request->getSession();
        $programmeAssocieClone = $programme->getProgrammesAssocies() ? clone($programme->getProgrammesAssocies()) : null;
        $form = $this->createForm(ProgrammeType::class, $programme, array(
            "copy" => $request->query->has("copy")
        ));
        $form->handleRequest($request);

        if (!$form->isSubmitted() && $request->headers->get('referer') != $request->getUri()) {
            $session->set('programme-edit-redirect', $request->headers->get('referer'));
        }

        $this->checkDuplicatedPicture($form, $programme, $request, $storage);

        if ($form->isSubmitted() && $form->isValid()) {
            $tags = $programme->getTempTags();

            $tags = explode(";", $tags);
            unset($tags[0]);

            if ($tags != []) {
                foreach($tags as $tag) {
                    $tagEntity = new Tag($tag);
                    $entityManager->persist($tagEntity);
                    $programme->addTag($tagEntity);
                }
            }

            $this->saveDuplicatedPicture($form, $programme, $request, $storage, $propertyMappingFactory);
            $this->saveDefaultAdditionalInfos($form, $programme, $storage, $propertyMappingFactory);
            $programme->updateDurations();

            $programmeAssocieManager->synchroAssociation($programme, $programmeAssocieClone);
            $entityManager->persist($programme);
            $entityManager->flush();
            // double flush pour sauvegarder les entités ajoutées pendant les event doctrine
            $entityManager->flush();

            $programmeEditRedirect = $session->get('programme-edit-redirect');
            $session->remove('programme-edit-redirect');

            $this->flashMessages->addSuccess('formation.edit.success');
            if ($programmeEditRedirect) {
                return $this->redirect($programmeEditRedirect);
            }
            return $this->redirectToRoute('admin_programme_index');
        }

        try {
            list($fichiersTopo, $forms_topo_views, $topoFileNew, $form_topo_new_view) =
                $this->manageFilesForm($programme, $request, $formFactory, TopoProgrammeFiles::class, $entityManager);
            list($fichiersTopoTool, $forms_topo_tool_views, $topoToolFileNew, $form_topo_tool_new_view) =
                $this->manageFilesForm($programme, $request, $formFactory, ToolProgrammeFiles::class, $entityManager, 'toolFile');
            list($fichiersDocsPedagogiques, $forms_docs_pedagogiques_views, $docsPedagogiquesFileNew, $form_docs_pedagogique_new_view) =
                $this->manageFilesForm($programme, $request, $formFactory, DocumentsPedagogiquesFiles::class, $entityManager, 'docsPedagogiques');
        } catch (NeedRedirectException $e) {
            return $this->redirect($e->getGenerateUrl());
        }

        return $this->render('admin/programme/create.html.twig', array(
            'programme' => $programme,
            'form' => $form,

            'forms_topo' => $forms_topo_views,
            'fichiersTopo' => $fichiersTopo,
            'topoFileNew' => $topoFileNew,
            'forms_topo_new' => $form_topo_new_view->createView(),

            'forms_topo_tool' => $forms_topo_tool_views,
            'fichiersTopoTool' => $fichiersTopoTool,
            'topoToolFileNew' => $topoToolFileNew,
            'forms_topo_tool_new' => $form_topo_tool_new_view->createView(),

            'forms_docs_pedagogiques' => $forms_docs_pedagogiques_views,
            'fichiersDocsPedagogiques' => $fichiersDocsPedagogiques,
            'docsPedagogiquesFileNew' => $docsPedagogiquesFileNew,
            'forms_docs_pedagogique_new' => $form_docs_pedagogique_new_view->createView(),

            'isGuidedFormation' => ProgrammeBuilder::isGuidedFormation($programme)
        ));
    }

    /**
     * @param FormInterface $form
     * @param Programme $programme
     * @param Request $request
     * @param StorageInterface $storage
     */
    public function checkDuplicatedPicture(FormInterface $form, Programme $programme, Request $request, StorageInterface $storage)
    {
        if ($form->isSubmitted() && $request->query->has("copy")) {
            $programme->setPicture(null);
        }

        if ($form->isSubmitted() && $form->get('pictureFrom')->getData() && is_null($form->get('pictureFile')->getData()) && is_null($programme->getPicture())) {
            $programme->setPicture($form->get('pictureFrom')->getData());
            $path = str_replace("//", "/", $filename = $storage->resolvePath($programme, "pictureFile"));
            if (!$path || !file_exists($path)) {
                $error = new FormError("L'image sélectionnée n'existe pas");
                $form->get('pictureFile')->addError($error);
            }
        }
    }

    /**
     * On bidouille pour dupliquer la picture
     * @param FormInterface $form
     * @param Programme $programme
     * @param Request $request
     * @param StorageInterface $storage
     * @param PropertyMappingFactory $propertyMappingFactory
     */
    public function saveDefaultAdditionalInfos(FormInterface $form, Programme $programme, StorageInterface $storage, PropertyMappingFactory $propertyMappingFactory)
    {
        if ($form->get('firstAdditionalInfosPictureFrom')->getData() && is_null($form->get('firstAdditionalInfosPictureFile')->getData()) && is_null($programme->getFirstAdditionalInfosPicture())) {
            $programme->setFirstAdditionalInfosPicture($form->get('firstAdditionalInfosPictureFrom')->getData());
            $path = str_replace("//", "/", $storage->resolvePath($programme, "firstAdditionalInfosPictureFile"));
            $mapping = $propertyMappingFactory->fromField($programme, "firstAdditionalInfosPictureFile");
            $uploadedFile = new UploadedFile($path, uniqid(), null, null, true);
            $programme->setFirstAdditionalInfosPictureFile($uploadedFile);
            $name = $mapping->getNamer()->name($programme, $mapping);
            $dir = $mapping->getUploadDestination();
            $programme->setFirstAdditionalInfosPicture($name);
            $programme->setFirstAdditionalInfosPictureFile(null);
            copy($path, $dir . DIRECTORY_SEPARATOR . $name);
        }

        if ($form->get('secondAdditionalInfosPictureFrom')->getData() && is_null($form->get('secondAdditionalInfosPictureFile')->getData()) && is_null($programme->getSecondAdditionalInfosPicture())) {
            $programme->setSecondAdditionalInfosPicture($form->get('secondAdditionalInfosPictureFrom')->getData());
            $path = str_replace("//", "/", $storage->resolvePath($programme, "secondAdditionalInfosPictureFile"));
            $mapping = $propertyMappingFactory->fromField($programme, "secondAdditionalInfosPictureFile");
            $uploadedFile = new UploadedFile($path, uniqid(), null, null, true);
            $programme->setSecondAdditionalInfosPictureFile($uploadedFile);
            $name = $mapping->getNamer()->name($programme, $mapping);
            $dir = $mapping->getUploadDestination();
            $programme->setSecondAdditionalInfosPicture($name);
            $programme->setSecondAdditionalInfosPictureFile(null);
            copy($path, $dir . DIRECTORY_SEPARATOR . $name);
        }
    }

    /**
     * On bidouille pour dupliquer la picture
     * @param FormInterface $form
     * @param Programme $programme
     * @param Request $request
     * @param StorageInterface $storage
     * @param PropertyMappingFactory $propertyMappingFactory
     */
    public function saveDuplicatedPicture(FormInterface $form, Programme $programme, Request $request, StorageInterface $storage, PropertyMappingFactory $propertyMappingFactory)
    {
        if ($form->isSubmitted() && $request->query->has("copy")) {
            $programme->setPicture(null);
        }

        if ($form->get('pictureFrom')->getData() && is_null($form->get('pictureFile')->getData()) && is_null($programme->getPicture())) {
            $programme->setPicture($form->get('pictureFrom')->getData());
            $path = str_replace("//", "/", $filename = $storage->resolvePath($programme, "pictureFile"));
            $mapping = $propertyMappingFactory->fromField($programme, "pictureFile");
            $uploadedFile = new UploadedFile($path, uniqid(), null, null, true);
            $programme->setPictureFile($uploadedFile);
            $name = $mapping->getNamer()->name($programme, $mapping);
            $dir = $mapping->getUploadDestination();
            $programme->setPicture($name);
            $programme->setPictureFile(null);
            copy($path, $dir . DIRECTORY_SEPARATOR . $name);
        }
    }

    /**
     * Displays a form to edit an existing Programme entity.
     *
     * @param Request $request
     * @param Formation $formation
     * @param Person $person
     * @param $type
     * @param CoordinatorHonorary $coordinatorHonoraryService
     * @return RedirectResponse|Response
     */
    #[Route(path: '/{formation}/preview-honorary/{person}/{type}', methods: ['GET', 'POST'], name: 'admin_formation_preview_honorary')]
    #[IsGranted('ROLE_WEBMASTER')]
    public function previewHonorary(Request $request, Formation $formation, Person $person, $type, CoordinatorHonorary $coordinatorHonoraryService): JsonResponse
    {
        $form = $this->createForm(FormationType::class, $formation);
        $form->handleRequest($request);

        if ($form->isSubmitted() &&  $form->isValid()) {
            if($formation->getCoordinators()->count() <= 1) {
                foreach($formation->getParticipations() as $participation) {
                    $participation->setCoordinator(null);
                }
            }
        }

        $selectedCoordinator = null;
        foreach($formation->getCoordinators() as $coordinator) {
            if ($coordinator->getPerson() === $person) {
                $selectedCoordinator = $coordinator;
            }
        }

        if ($type === "honorary") {
            return new JsonResponse(array(
                "honorary" => $coordinatorHonoraryService->calculTotalHonorary($formation, $selectedCoordinator),
                "honoraryn1" => $formation->isPluriAnnuelle() ? $coordinatorHonoraryService->calculTotalHonorary($formation, $selectedCoordinator, false, false, false, true) : 0
            ));
        } else if ($type === "cost") {
            return new JsonResponse(array(
                "cost" => $selectedCoordinator->getFormationCost()
            ));
        }

        throw new NotFoundHttpException();
    }

    /**
     * Deletes a Programme entity.
     *
     * @param Request $request
     * @param Programme $programme
     * @return RedirectResponse|Response
     */
    #[Route(path: '/delete/{id}', methods: ['GET', 'POST'], name: 'admin_programme_delete')]
    #[IsGranted('ROLE_WEBMASTER')]
    public function delete(Request $request, Programme $programme, EntityManagerInterface $em)
    {
        $session = $request->getSession();
        $sessionKey = "programme-delete-redirect-page";
        $form = $this->createDeleteForm($programme);
        $form->handleRequest($request);
        $redirectedPage = 1;

        if ($request->query->has('page')) {
            $session->set($sessionKey, $request->query->get('page'));
        } else if ($session->has($sessionKey)) {
            $redirectedPage = $session->get($sessionKey);
            $session->remove($sessionKey);
        }

        if ($form->isSubmitted() && $form->isValid()) {
            try {
                /** @var Formation $formation */
                foreach ($programme->getAllFormations() as $formation) {
                    foreach ($formation->getParticipations(true) as $participation) {
                        $em->remove($participation);
                    }
                    $em->remove($formation);
                }
                $em->remove($programme);
                $em->flush();
            } catch (\Exception $e) {
                if($e instanceOf ForeignKeyConstraintViolationException) {
                    $this->flashMessages->addError('programme.delete.constraint_error');
                    return $this->redirectToRoute('admin_programme_index', array("page" => $redirectedPage));
                }
            }
            $this->flashMessages->addSuccess('programme.delete.success');
            return $this->redirectToRoute('admin_programme_index', array("page" => $redirectedPage));
        }

        return $this->render('admin/programme/delete.html.twig', array(
            'programme' => $programme,
            'form' => $form,
        ));
    }

    /**
     * @param Programme $programme
     */
    #[Route(path: '/exportParticipants/{programme}', name: 'admin_programme_participant_export')]
    #[IsGranted('ROLE_WEBMASTER')]
    public function exportParticipants(Programme $programme, CsvBilanExport $csvBilanExport): BinaryFileResponse
    {
        $projectDir = $this->getParameter('kernel.project_dir');
        $tmpName = "participants_" . $programme->getTitle() . "_" . $programme->getReference();

        $fileTmp = $projectDir . "/uploads/formations/participants/".sprintf('%s.tmp', $tmpName);
        $file = $projectDir . "/uploads/formations/participants/".sprintf('%s.csv', $tmpName);
        $fileLog = $projectDir . "/uploads/formations/participants/".sprintf('%s.log', $tmpName);

        if($file && file_exists($file)) {
            unlink($file);
        }

        touch($file);

        $csvBilanExport->exportFormationParticipants($programme, $fileTmp);

        rename($fileTmp, $file);

        $fileName = $tmpName . '.csv';
        $response = new BinaryFileResponse($file);
        $response->setContentDisposition(
            ResponseHeaderBag::DISPOSITION_ATTACHMENT,
            $fileName);

        return $response;
    }


    /**
     * @param Request $request
     * @param Programme $programme
     * @return RedirectResponse|Response
     */
    #[Route(path: '/{id}/evaluation', name: 'admin_programme_evaluation')]
    #[IsGranted('ROLE_WEBMASTER')]
    public function evaluation(Programme $programme, EntityManagerInterface $em): Response
    {
        /** @var EvaluationFormationAnswer[] $evaluations */
        $evaluations = $em->getRepository(EvaluationFormationAnswer::class)->findByProgramme($programme);

        $repartitions = [];

        for ($i = 1; $i <= EvaluationFormationManager::NB_QUESTION; $i++) {
            $repartitions[$i] = array(
                "count" => 0,
                "total" => 0,
                "avg" => 0
            );
        }

        foreach ($evaluations as $evaluation) {
            $repartitions[$evaluation->getQuestion()]["count"]++;
            $repartitions[$evaluation->getQuestion()]["total"] += $evaluation->getAnswer();
        }

        foreach ($repartitions as &$repartition) {
            if ($repartition["count"] > 0) {
                $repartition["avg"] = $repartition["total"] / $repartition["count"];
            }
        }

        $coordinators = $programme->getCoordinators();

        $vars = array();
        foreach ($coordinators as $coordinator) {
            $vars = $this->evaluationProgramme($programme, $em);
        }

        return $this->render('admin/programme/evaluation.html.twig', array(
            'evaluation' => $repartitions,
            'programme' => $programme,
            'evaluationByCoordinators' => $vars['repartitions']
        ));
    }

    /**
     * @param Programme $programme
     * @param CsvBilanExport $csvExport
     * @return RedirectResponse|Response
     */
    #[Route(path: '/{id}/export-email-history', methods: ['GET'], name: 'admin_programme_export_emails')]
    #[IsGranted('ROLE_COORDINATOR')]
    public function exportMailerHistory(Programme $programme, CsvBilanExport $csvExport): CsvFileResponse
    {
        $data = $csvExport->exportMailHistoryProgramme($programme);
        return new CsvFileResponse($data, sprintf("export_emails_%s.csv", $programme->getTitle()));
    }

    /**
     * @param Request $request
     * @param Programme $programme
     * @return RedirectResponse|Response
     */
    #[Route(path: '/{id}/evaluation-formateur', name: 'admin_programme_evaluation_former')]
    #[IsGranted('ROLE_WEBMASTER')]
    public function evaluationFormer(Request $request, Formateur $formateur, EmailSender $emailSender, TranslatorInterface $translator): RedirectResponse
    {
        $emailSender->sendFormerProgrammeBilan($formateur);
        $this->addFlash('success', $translator->trans('evaluationGlobal.mail.success', array("%mail%" => $formateur->getPerson()->getEmail())));
        return $this->redirect($request->headers->get('referer'));
    }

    /**
     * Creates a form to delete a Programme entity.
     */
    private function createDeleteForm(Programme $programme): FormInterface
    {
        return $this->createFormBuilder()
            ->setAction($this->generateUrl('admin_programme_delete', array('id' => $programme->getId())))
            ->setMethod(Request::METHOD_POST)
            ->getForm()
            ;
    }

    public function evaluationProgramme(Programme $programme, EntityManagerInterface $em)
    {
        /** @var EvaluationProgramme[] $evaluations */
        $evaluations = $em->getRepository(EvaluationProgramme::class)->findByProgramme($programme);

        $repartitions = [];

        for ($i = 1; $i <= EvaluationProgrammeManager::NB_QUESTION; $i++) {
            $repartitions[$i] = array(
                "count" => 0,
                "total" => 0,
                "avg" => 0
            );
        }

        foreach ($evaluations as $evaluation) {
            $repartitions[$evaluation->getQuestion()]["count"]++;
            $repartitions[$evaluation->getQuestion()]["total"] += (int)$evaluation->getAnswer();
        }

        foreach ($repartitions as &$repartition) {
            if ($repartition["count"] > 0) {
                $repartition["avg"] = $repartition["total"] / $repartition["count"];
            }
        }

        return array('repartitions' => $repartitions);
    }

    public function evaluationProgrammeByCoordinator(Programme $programme, Coordinator $coordinator, EntityManagerInterface $em)
    {
        /** @var EvaluationProgramme[] $evaluations */
        $evaluations = $em->getRepository(EvaluationProgramme::class)->findByProgrammeCoordinator($programme, $coordinator);

        $repartitions = [];

        for ($i = 1; $i <= EvaluationProgrammeManager::NB_QUESTION; $i++) {
            $repartitions[$i] = array(
                "count" => 0,
                "total" => 0,
                "avg" => 0
            );
        }

        foreach ($evaluations as $evaluation) {
            $repartitions[$evaluation->getQuestion()]["count"]++;
            $repartitions[$evaluation->getQuestion()]["total"] += (int)$evaluation->getAnswer();
        }

        foreach ($repartitions as &$repartition) {
            if ($repartition["count"] > 0) {
                $repartition["avg"] = $repartition["total"] / $repartition["count"];
            }
        }

        $participants = array();
        foreach($programme->getFormations() as $formation) {
           $participants[$formation->getId()] = $formation->getParticipants()->count();
        }

        return array('repartitions' => $repartitions, 'participants' => $participants);
    }

    /**
      * @param Request   $request
      * @param Programme $programme
      * @param Coordinator $coodinator
      */
     #[Route(path: '/{programme}/evaluation-programme/coordinator/{coordinator}', methods: ['GET', 'POST'], name: 'evaluation_programme_by_coordinator_show')]
     #[IsGranted('ROLE_COORDINATOR')]
     public function evaluationProgrammeByCoordinatorShow(Programme $programme, Coordinator $coordinator, EntityManagerInterface $em): Response
     {

        $vars = $this->evaluationProgrammeByCoordinator($programme, $coordinator, $em);

         // $this->denyAccessUnlessCanShowPerson($person);

         return $this->render('admin/evaluation/programme-show.html.twig', array(
             'evaluation' => $vars['repartitions'],
             'coordinator' => $coordinator,
             'programme' => $programme,
             'participants' => $vars['participants']
         ));
     }

     /**
     * @param Request   $request
     * @param Programme $programme
     * @param Coordinator $coodinator
     */
    #[Route(path: '/{programme}/evaluation-coordinator/coordinator/{coordinator}', methods: ['GET', 'POST'], name: 'evaluation_coordinator_by_coordinator_show')]
    #[IsGranted('ROLE_COORDINATOR')]
    public function evaluationCoordinatorByCoordinatorShow(Programme $programme, Coordinator $coordinator, EntityManagerInterface $em): Response
    {
        // $this->denyAccessUnlessCanShowPerson($person);

        /** @var EvaluationProgramme[] $evaluations */
        $evaluations = $em->getRepository(EvaluationCoordinator::class)->findByProgrammeCoordinator($programme, $coordinator);

        $repartitions = [];

        for ($i = 1; $i <= EvaluationCoordinatorByCoordinatorManager::NB_QUESTION-1; $i++) {
            $repartitions[$i] = array(
                "count" => 0,
                "total" => 0,
                "avg" => 0,
                "version" => 0
            );
        }

        foreach ($evaluations as $evaluation) {
            if($evaluation->getQuestion() != 5) {
                $repartitions[$evaluation->getQuestion()]["count"]++;
                $repartitions[$evaluation->getQuestion()]["total"] += (int)$evaluation->getAnswer();
                $repartitions[$evaluation->getQuestion()]["version"] += $evaluation->getVersion();
            }
            else {
                $commentaire = $evaluation->getAnswer();
            }
        }

        foreach ($repartitions as &$repartition) {
            if ($repartition["count"] > 0) {
                $repartition["avg"] = $repartition["total"] / $repartition["count"];
            }
        }

        $participants = array();
        foreach($programme->getFormations() as $formation) {
            $participants[$formation->getId()] = $formation->getParticipants()->count();
        }

        $repartitionsFormers = $this->evaluationFormersByCoordinator($programme, $coordinator, $em);

        return $this->render('admin/evaluation/coordinator-show.html.twig', array(
            'evaluation' => $repartitions,
            'coordinator' => $coordinator,
            'programme' => $programme,
            'commentaire' => $commentaire,
            'participants' => $participants,
            'evaluationsFormers' => $repartitionsFormers
        ));
    }

    public function evaluationFormersByCoordinator(Programme $programme, Coordinator $coordinator, EntityManagerInterface $em)
    {
        /** @var EvaluationProgramme[] $evaluations */
        $evaluations = $em->getRepository(EvaluationFormerByCoordinator::class)->findByProgrammeCoordinator($programme, $coordinator);

        $repartitions = [];

        for ($i = 1; $i <= EvaluationFormerByCoordinatorManager::NB_QUESTION; $i++) {
            $repartitions[$i] = array(
                "count" => 0,
                "total" => 0,
                "avg" => 0
            );
        }

        foreach ($evaluations as $evaluation) {
            $repartitions[$evaluation->getQuestion()]["count"]++;
            $repartitions[$evaluation->getQuestion()]["total"] += (int)$evaluation->getAnswer();
        }

        foreach ($repartitions as &$repartition) {
            if ($repartition["count"] > 0) {
                $repartition["avg"] = $repartition["total"] / $repartition["count"];
            }
        }

        return $repartitions;
    }

    public function evaluationFormerByCoordinator(Programme $programme, Person $former, Coordinator $coordinator, EntityManagerInterface $em)
    {
        /** @var EvaluationProgramme[] $evaluations */
        $evaluations = $em->getRepository(EvaluationFormerByCoordinator::class)->findByFormerProgrammeCoordinator($former, $programme, $coordinator);

        $repartitions = [];

        for ($i = 1; $i <= EvaluationFormerByCoordinatorManager::NB_QUESTION; $i++) {
            $repartitions[$i] = array(
                "count" => 0,
                "total" => 0,
                "avg" => 0
            );
        }

        foreach ($evaluations as $evaluation) {
            $repartitions[$evaluation->getQuestion()]["count"]++;
            $repartitions[$evaluation->getQuestion()]["total"] += (int)$evaluation->getAnswer();
        }

        foreach ($repartitions as &$repartition) {
            if ($repartition["count"] > 0) {
                $repartition["avg"] = $repartition["total"] / $repartition["count"];
            }
        }

        return $repartitions;
    }

     /**
     * @param Request   $request
     * @param Programme $programme
     * @param Person $former
     * @param Coordinator $coodinator
     */
    #[Route(path: '/{programme}/evaluation/former/{former}/coordinator/{coordinator}', methods: ['GET', 'POST'], name: 'evaluation_former_by_coordinator_show')]
    #[IsGranted('ROLE_COORDINATOR')]
    public function evaluationFormerByCoordinatorShow(Programme $programme, Person $former, Coordinator $coordinator, EntityManagerInterface $entityManager): Response
    {
        // $this->denyAccessUnlessCanShowPerson($person);

        $repartitions = $this->evaluationFormerByCoordinator($programme, $former, $coordinator, $entityManager);

        return $this->render('admin/evaluation/former-show.html.twig', array(
            'evaluation' => $repartitions,
            'coordinator' => $coordinator,
            'programme' => $programme,
            'former' => $former
        ));
    }

    /**
     * @param Request $request
     * @param Programme $programme
     * @param Person $former
     * @param Coordinator $coodinator
     * @return JsonResponse
     */
    #[Route(path: '/autocomplete', name: 'admin_programme_autocomplete', methods: ['POST'])]
    #[IsGranted('ROLE_COORDINATOR')]
    public function autocompleteProgramme(Request $request, EntityManagerInterface $em): JsonResponse
    {
        $repo = $em->getRepository(Programme::class);
        /** @var Programme $programme */
        $programme = $repo->findAutocompletionReference($request->request->get('title'));

        if (is_null($programme)) {
            throw new NotFoundHttpException();
        }

        return new JsonResponse(array(
            "title" => $programme->getTitle(),
            "startHour" => $programme->getStartDate()->format("G"),
            "startMinute" => (string) intval($programme->getStartDate()->format("i")),
            "endHour" => $programme->getEndDate()->format("G"),
            "endMinute" => (string) intval($programme->getEndDate()->format("i")),
            "resume" => $programme->getResume(),
            "objectives" => $programme->getObjectives(),
            "objectivesActalians" => $programme->getObjectivesActalians(),
            "picture" => $programme->getPicture(),
            "nationalOrientation" => $programme->getNationalOrientation(),
            "categories" => $programme->getCategories(),
            "specialities" => $programme->getSpecialities(),
            "exercicesMode" => $programme->getExercisesMode(),
            "durationPresentielle" => $programme->getDurationPresentielle(),
            "durationNotPresentielle" => $programme->getDurationNotPresentielle(),
            "durationNotPresentielleActalians" => $programme->getDurationNotPresentielleActalians(),
            "certifying" => $programme->isCertifying(),
        ));
    }

    /**
     * @param Request $request
     * @param Programme $programme
     * @param Person $former
     * @param Coordinator $coodinator
     * @return JsonResponse
     */
    #[Route(path: '/duplicate-check', name: 'admin_programme_duplicate_check', methods: ['POST'])]
    #[IsGranted('ROLE_COORDINATOR')]
    public function preventDuplicate(Request $request, EntityManagerInterface $em): JsonResponse
    {
        $repo = $em->getRepository(Programme::class);
        $programmes = $repo->findPreventDuplicate($request->request->get('title'), $request->request->get('reference'));
        return new JsonResponse(array(
            "duplicated" => count($programmes) > 0
        ));
    }

    public function drawUploadForm(Request $request, AbstractProgrammeFile $topoFiles, $fieldName, $formFactory, $prefixForm) {
        $name = $prefixForm ?? $fieldName;
        $genForm = $formFactory->createNamedBuilder($name.'_'.$topoFiles->getId(), FormType::class, $topoFiles);
        $this->drawUploadFormField($genForm, $fieldName);

            if($topoFiles->getId()) {
                $genForm->add('submit', SubmitType::class, array(
                    'label' => 'admin.global.send'
                ));
            }

        $form = $genForm->getForm();

        $form->handleRequest($request);

        return $form;
    }

    public function drawUploadFormField(FormBuilderInterface $builder, $fieldName, $allow_delete = true, $maxSize='128M') {
        $builder->add($fieldName, VichFileType::class, array(
            'label' => false,
            'required' => false,
            'download_uri' => false,
            'allow_delete' => $allow_delete,
            'error_bubbling' => true,
            'constraints' => new File(
                array(
                    'mimeTypes' => array(
                        'application/pdf', 'application/x-pdf'
                    ),
                    'mimeTypesMessage' => 'formation.mimeTypesPDF',
                    'maxSize' => $maxSize
                )
            )
        ));
    }

    /**
     * @param Programme $programme
     * @param Request $request
     * @param FormFactoryInterface $formFactory
     * @param $classNameFile
     * @param $fieldName
     * @return array
     * @throws NeedRedirectException
     */
    public function manageFilesForm(Programme $programme, Request $request, FormFactoryInterface $formFactory, $classNameFile, EntityManagerInterface $entityManager, $prefixForm = null): array
    {
        $fichiersTopo = [];
        $forms_topo_views = [];
        $fieldName = 'topoFile';

        $topoFileNew = new $classNameFile();
        $topoFileNew->setProgramme($programme);
        $form_topo_new_view = $this->drawUploadForm($request, $topoFileNew, $fieldName, $formFactory, $prefixForm);

        if ($form_topo_new_view->isSubmitted()) {
            if ($form_topo_new_view->isValid()
            ) {
                $filename = null;
                foreach ($request->files as $file) {
                    $filename = $file[$fieldName]['file']->getClientOriginalName();
                }

                if ($filename) {
                    $topoFileNew->setTopoOriginalName($filename);
                }

                $entityManager->persist($topoFileNew);
                $entityManager->flush();

                $this->flashMessages->addSuccess('admin.formation.upload.success');

                throw new NeedRedirectException($this->generateUrl('admin_programme_edit', ['id' => $programme->getId()]));
            } else {
                $this->flashMessages->addError('admin.formation.upload.error');
            }
        }

        $topoFilesRepository = $entityManager->getRepository($classNameFile);
        $topoFiles = $topoFilesRepository->findBy(array(
            'programme' => $programme
        ));

        if (!$topoFiles) {
            $topoFile = new $classNameFile();
            $topoFile->setProgramme($programme);
            $topoFiles = array($topoFile);
        } else {
            $fichiersTopo = $topoFiles;
        }

        foreach ($topoFiles as $topoFile) {
            $form_topo_views = $this->drawUploadForm($request, $topoFile, $fieldName, $formFactory, $prefixForm);

            if ($form_topo_views->isSubmitted() && $form_topo_views->isValid()) {

                $toDelete = $form_topo_views->get($fieldName)->has('delete') && $form_topo_views->get($fieldName)->get('delete')->getData();

                if (!$toDelete && !is_null($topoFile->getTopo()) && !is_null($request->files->get(0)[$fieldName]['file'])) {
                    $filename = null;
                    foreach ($request->files as $file) {
                        $filename = $file[$fieldName]['file']->getClientOriginalName();
                    }

                    if ($filename) {
                        $topoFile->setTopoOriginalName($filename);
                    }

                    $entityManager->persist($topoFile);
                } else if ($toDelete) {
                    $entityManager->remove($topoFile);
                }

                $entityManager->flush();

                $this->flashMessages->addSuccess('admin.formation.upload.delete_success');
                throw new NeedRedirectException($this->generateUrl('admin_programme_edit', ['id' => $programme->getId()]));
            }

            $forms_topo_views[$topoFile->getId()] = $form_topo_views->createView();
        }
        return array($fichiersTopo, $forms_topo_views, $topoFileNew, $form_topo_new_view);
    }

    public function getProgrammeBuilder($formationType): ProgrammeBuilderInterface
    {
        if ($formationType == ProgrammeBuilder::VFC_FORMATION_TYPE) {
            return new ProgrammeVfcBuilder();
        }
        if ($formationType == ProgrammeBuilder::TCS_FORMATION_TYPE) {
            return new ProgrammeTcsBuilder();
        }
        return new ProgrammeBuilder();
    }
}
