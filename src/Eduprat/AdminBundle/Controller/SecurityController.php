<?php

namespace Eduprat\AdminBundle\Controller;

use Ed<PERSON><PERSON>\DomainBundle\Controller\EdupratController;
use Symfony\Component\Routing\Attribute\Route;
use Symfony\Component\HttpFoundation\Response;
use Symfony\Component\Routing\RouterInterface;
use Symfony\Component\Security\Http\Authentication\AuthenticationUtils;

class SecurityController extends EdupratController
{
    #[Route(path: '/login', name: 'alienor_user_login')]
    public function login(AuthenticationUtils $authenticationUtils, RouterInterface $router): Response {
        $response = new Response();

        // Récupère l'erreur d'authentification s'il y en a une
        $error = $authenticationUtils->getLastAuthenticationError();
        
        if ($error) {
            $response->setStatusCode(Response::HTTP_UNAUTHORIZED);
        }

        return $this->render('admin/security/login.html.twig', array(
            // dernier nom entré par l'utilisateur
            'last_username' => $authenticationUtils->getLastUsername(),
            'error' => $error,
        ), $response);
    }

}