<?php

namespace Eduprat\AdminBundle\Services;

use Doctrine\Common\Collections\Criteria;
use Doctrine\Common\Collections\Order;
use Doctrine\ORM\EntityManagerInterface;
use Eduprat\AdminBundle\Entity\LeadSearch;
use Eduprat\AdminBundle\Entity\ParticipantSearch;
use Eduprat\AdminBundle\Entity\Person;
use Eduprat\AuditBundle\Services\CourseManager;
use Eduprat\CrmBundle\Model\AttestationComptabiliteSearch;
use Eduprat\CrmBundle\Model\ComptabiliteSearch;
use Eduprat\CrmBundle\Services\ComptabiliteService;
use Eduprat\DomainBundle\Entity\Audit;
use Eduprat\DomainBundle\Entity\EvaluationGlobalAnswer;
use Eduprat\DomainBundle\Entity\FinanceSousMode;
use Eduprat\DomainBundle\Entity\Formateur;
use Eduprat\DomainBundle\Entity\Formation;
use Eduprat\DomainBundle\Entity\FormationAudit;
use Eduprat\DomainBundle\Entity\FormationCongres;
use Eduprat\DomainBundle\Entity\FormationPresentielle;
use Eduprat\DomainBundle\Entity\FormationSedd;
use Eduprat\DomainBundle\Entity\Invoice;
use Eduprat\DomainBundle\Entity\LogoPartenaire;
use Eduprat\DomainBundle\Entity\LogoPartenaireUsage;
use Eduprat\DomainBundle\Entity\MailerHistory;
use Eduprat\DomainBundle\Entity\Participant;
use Eduprat\DomainBundle\Entity\Participation;
use Eduprat\DomainBundle\Entity\ParticipationHistory;
use Eduprat\DomainBundle\Entity\ParticipationLog;
use Eduprat\DomainBundle\Entity\Programme;
use Eduprat\DomainBundle\Form\ProgrammeType;
use Eduprat\DomainBundle\Model\SessionSearch;
use Eduprat\DomainBundle\Services\AdressesService;
use Eduprat\DomainBundle\Services\SearchHandler;
use Eduprat\DomainBundle\Services\StatsCalculator;
use Symfony\Contracts\Translation\TranslatorInterface;
use Symfony\Component\Asset\Packages;
use Vich\UploaderBundle\Templating\Helper\UploaderHelper;

class CsvBilanExport {

    /**
     * @var EntityManagerInterface
     */
    private $entityManager;

    /**
     * @var StatsCalculator
     */
    private $statsCalculator;

    /**
     * @var AdressesService
     */
    private $adressesService;

    /**
     * @var ComptabiliteService
     */
    private $comptabiliteService;

    /**
     * @var TranslatorInterface
     */
    private $translator;

    /**
     * @var Packages
     */
    private $packages;

    /**
     * @var UploaderHelper
     */
    private $uploaderHelper;

    /**
     * @var Regions
     */
    private $regions;

    /**
     * @var SearchHandler
     */
    private  $searchHandler;

    /**
     * @var CourseManager
     */
    private  $courseManager;

    public CONST MIN_TIME_BY_MODULE = [
        "video_presession" => 0,
        "form_presession" => 35,
        "prerestitution" => 20 ,
        "etutorat_1" => 5,
        "fiche_action_1" => 0,
        "video_postsession" => 0,
        "topos" => 0,
        "form_postsession" => 20,
        "tool_box" => 0,
        "etutorat_2" => 2,
        "restitution" => 20,
        "fiche_action_2" => 10,
        "synthese" => 6,
        "progression" => 0,
        "etutorat_3" => 2,
        "end" => 0,
        "classe_virtuelle" => 180,
    ];

    private \DateTime $moduleProgressionUpdateDate;

    public function __construct(EntityManagerInterface $entityManager, StatsCalculator $statsCalculator, AdressesService $adressesService, TranslatorInterface $translator, Regions $regions, Packages $packages, UploaderHelper $uploaderHelper, SearchHandler $searchHandler, ComptabiliteService $comptabiliteService, CourseManager $courseManager, $moduleProgressionUpdateDate) {
        $this->entityManager = $entityManager;
        $this->statsCalculator = $statsCalculator;
        $this->adressesService = $adressesService;
        $this->translator = $translator;
        $this->regions = $regions;
        $this->searchHandler = $searchHandler;
        $this->packages = $packages;
        $this->uploaderHelper = $uploaderHelper;
        $this->comptabiliteService = $comptabiliteService;
        $this->courseManager = $courseManager;
        $this->moduleProgressionUpdateDate = new \DateTime($moduleProgressionUpdateDate);
    }

    public function export($year) {
        $em = $this->entityManager;
        $s = $this->statsCalculator;

        $closed = array(Formation::STATUS_CLOSED => true);
        $current = array(Formation::STATUS_OPENED => true);
        $future = array(Formation::STATUS_FUTURE => true);
        $closedAndpc = array(Formation::STATUS_CLOSED_ANDPC => true);
        $currentAndpc = array(Formation::STATUS_OPENED_ANDPC => true);
        $futureAndpc = array(Formation::STATUS_FUTURE_ANDPC => true);
        $delimiter = array('------------------------------------------------------------------------------------------------------------------------------');
        $space = array(array("\n\n\n\n\n"));

        $formationAuditPerSubject = $s->getFormationCountPerSubject($year, array(), false, [FormationAudit::class]);
        $formationPresentiellePerSubject = $s->getFormationCountPerSubject($year, array(), false, [FormationPresentielle::class]);
        $formationCmsPerSubject = $s->getFormationCountPerSubject($year, array(), true);

        $formationAuditPerSubjectRows = [];
        foreach ($formationAuditPerSubject as $key => $value) {
            $formationAuditPerSubjectRows[] = array('Nombre de formations Réseau EDUPRAT - en Audit - Sujet : "' . $key . '"', $value);
        }

        $formationPresentiellePerSubjectRows = [];
        foreach ($formationPresentiellePerSubject as $key => $value) {
            $formationPresentiellePerSubjectRows[] = array('Nombre de formations Réseau EDUPRAT - en Presentielle - Sujet : "' . $key . '"', $value);
        }

        $formationCmsPerSubjectRows = [];
        foreach ($formationCmsPerSubject as $key => $value) {
            $formationCmsPerSubjectRows[] = array('Nombre de formations Réseau EDUPRAT - en Cms - Sujet : "' . $key . '"', $value);
        }

        $medecinsCurrentSpeciality = $s->getParticipantCountPerSpeciality($year, $current, true);
        $medecinsCurrentSpecialityAudit = $s->getParticipantCountPerSpeciality($year, $current, true, [FormationAudit::class]);
        $medecinsCurrentSpecialityPresentielle = $s->getParticipantCountPerSpeciality($year, $current, true, [FormationPresentielle::class]);
        $othersCurrentSpeciality = $s->getParticipantCountPerSpeciality($year, $current, false);
        $othersCurrentSpecialityAudit = $s->getParticipantCountPerSpeciality($year, $current, false, [FormationAudit::class]);
        $othersCurrentSpecialityPresentielle = $s->getParticipantCountPerSpeciality($year, $current, false, [FormationPresentielle::class]);
        $medecinsClosedSpeciality = $s->getParticipantCountPerSpeciality($year, $closed, true);
        $medecinsClosedSpecialityAudit = $s->getParticipantCountPerSpeciality($year, $closed, true, [FormationAudit::class]);
        $medecinsClosedSpecialityPresentielle = $s->getParticipantCountPerSpeciality($year, $closed, true, [FormationPresentielle::class]);
        $othersClosedSpeciality = $s->getParticipantCountPerSpeciality($year, $closed, false);
        $othersClosedSpecialityAudit = $s->getParticipantCountPerSpeciality($year, $closed, false, [FormationAudit::class]);
        $othersClosedSpecialityPresentielle = $s->getParticipantCountPerSpeciality($year, $closed, false, [FormationPresentielle::class]);
        $medecinsFutureSpeciality = $s->getParticipantCountPerSpeciality($year, $future, true);
        $medecinsFutureSpecialityAudit = $s->getParticipantCountPerSpeciality($year, $future, true, [FormationAudit::class]);
        $medecinsFutureSpecialityPresentielle = $s->getParticipantCountPerSpeciality($year, $future, true, [FormationPresentielle::class]);
        $othersFutureSpeciality = $s->getParticipantCountPerSpeciality($year, $future, false);
        $othersFutureSpecialityAudit = $s->getParticipantCountPerSpeciality($year, $future, false, [FormationAudit::class]);
        $othersFutureSpecialityPresentielle = $s->getParticipantCountPerSpeciality($year, $future, false, [FormationPresentielle::class]);

        $medecinsCurrentSpecialityRows = [];
        foreach ($medecinsCurrentSpeciality as $key => $value) {
            $medecinsCurrentSpecialityRows[] = array('Nombre de médecins en formation - Spécialité : \'' . $key . '\'', $value);
        }

        $medecinsCurrentSpecialityAuditRows = [];
        foreach ($medecinsCurrentSpecialityAudit as $key => $value) {
            $medecinsCurrentSpecialityAuditRows[] = array('Nombre de médecins en formation - en Audit - Spécialité : \'' . $key . '\'', $value);
        }

        $medecinsCurrentSpecialityPresentielleRows = [];
        foreach ($medecinsCurrentSpecialityPresentielle as $key => $value) {
            $medecinsCurrentSpecialityPresentielleRows[] = array('Nombre de médecins en formation - en Presentielle - Spécialité : \'' . $key . '\'', $value);
        }

        $othersCurrentSpecialityRows = [];
        foreach ($othersCurrentSpeciality as $key => $value) {
            $othersCurrentSpecialityRows[] = array('Nombre de paramédicaux en formation - Spécialité : \'' . $key . '\'', $value);
        }

        $othersCurrentSpecialityAuditRows = [];
        foreach ($othersCurrentSpecialityAudit as $key => $value) {
            $othersCurrentSpecialityAuditRows[] = array('Nombre de paramédicaux en formation - en Audit - Spécialité : \'' . $key . '\'', $value);
        }

        $othersCurrentSpecialityPresentielleRows = [];
        foreach ($othersCurrentSpecialityPresentielle as $key => $value) {
            $othersCurrentSpecialityPresentielleRows[] = array('Nombre de paramédicaux en formation - en Presentielle - Spécialité : \'' . $key . '\'', $value);
        }

        $medecinsClosedSpecialityRows = [];
        foreach ($medecinsClosedSpeciality as $key => $value) {
            $medecinsClosedSpecialityRows[] = array('Nombre de médecins formés - Spécialité : \'' . $key . '\'', $value);
        }

        $medecinsClosedSpecialityAuditRows = [];
        foreach ($medecinsClosedSpecialityAudit as $key => $value) {
            $medecinsClosedSpecialityAuditRows[] = array('Nombre de médecins formés - en Audit - Spécialité : \'' . $key . '\'', $value);
        }

        $medecinsClosedSpecialityPresentielleRows = [];
        foreach ($medecinsClosedSpecialityPresentielle as $key => $value) {
            $medecinsClosedSpecialityPresentielleRows[] = array('Nombre de médecins formés - en Presentielle - Spécialité : \'' . $key . '\'', $value);
        }

        $othersClosedSpecialityRows = [];
        foreach ($othersClosedSpeciality as $key => $value) {
            $othersClosedSpecialityRows[] = array('Nombre de paramédicaux formés - Spécialité : \'' . $key . '\'', $value);
        }

        $othersClosedSpecialityAuditRows = [];
        foreach ($othersClosedSpecialityAudit as $key => $value) {
            $othersClosedSpecialityAuditRows[] = array('Nombre de paramédicaux formés - en Audit - Spécialité : \'' . $key . '\'', $value);
        }

        $othersClosedSpecialityPresentielleRows = [];
        foreach ($othersClosedSpecialityPresentielle as $key => $value) {
            $othersClosedSpecialityPresentielleRows[] = array('Nombre de paramédicaux formés - en Presentielle - Spécialité : \'' . $key . '\'', $value);
        }

        $medecinsFutureSpecialityRows = [];
        foreach ($medecinsFutureSpeciality as $key => $value) {
            $medecinsFutureSpecialityRows[] = array('Nombre de médecins prévisionnel - Spécialité : \'' . $key . '\'', $value);
        }

        $medecinsFutureSpecialityAuditRows = [];
        foreach ($medecinsFutureSpecialityAudit as $key => $value) {
            $medecinsFutureSpecialityAuditRows[] = array('Nombre de médecins prévisionnel - en Audit - Spécialité : \'' . $key . '\'', $value);
        }

        $medecinsFutureSpecialityPresentielleRows = [];
        foreach ($medecinsFutureSpecialityPresentielle as $key => $value) {
            $medecinsFutureSpecialityPresentielleRows[] = array('Nombre de médecins prévisionnel - en Presentielle - Spécialité : \'' . $key . '\'', $value);
        }

        $othersFutureSpecialityRows = [];
        foreach ($othersFutureSpeciality as $key => $value) {
            $othersFutureSpecialityRows[] = array('Nombre de paramédicaux prévisionnel - Spécialité : \'' . $key . '\'', $value);
        }

        $othersFutureSpecialityAuditRows = [];
        foreach ($othersFutureSpecialityAudit as $key => $value) {
            $othersFutureSpecialityAuditRows[] = array('Nombre de paramédicaux prévisionnel - en Audit - Spécialité : \'' . $key . '\'', $value);
        }

        $othersFutureSpecialityPresentielleRows = [];
        foreach ($othersFutureSpecialityPresentielle as $key => $value) {
            $othersFutureSpecialityPresentielleRows[] = array('Nombre de paramédicaux prévisionnel - en Presentielle - Spécialité : \'' . $key . '\'', $value);
        }

        $avgParticipationsCountPerCategory = $s->getAvgParticipantCountPerCategory($year, array(), false);

        $avgParticipationsCountPerCategoryRows = [];
        foreach ($avgParticipationsCountPerCategory as $key => $value) {
            $avgParticipationsCountPerCategoryRows[] = array('Nombre moyen de stagiaires par formation - Autres PS - Catégorie : \'' . $key . '\'', $value);
        }

        $tableCa = array(
            $delimiter,
            array('TABLEAU DE BORD - CA EDUPRAT GLOBAL (Réseau Eduprat, CMS, SET, SEDD, Congrès)'),
            array(''),
            array("CA total (sur l'année)", $s->getTotalCa($year)),
            array("CA total (sur l'année) - En cours", $s->getTotalCa($year, $currentAndpc)),
            array("CA total (sur l'année) - Prévisionnel", $s->getTotalCa($year, $futureAndpc)),
            array("CA total (sur l'année) - Réalisé", $s->getTotalCa($year, $closedAndpc)),
            array(''),
            array("CA Réseau EDUPRAT (sur l'année) - total", $s->getTotalCa($year, array(), false, [FormationAudit::class, FormationPresentielle::class])),
            array("CA CMS (sur l'année) - total", $s->getTotalCa($year, array(), true)),
            array("CA SEDD (sur l'année) - total", $s->getTotalCa($year, array(), false, [FormationSedd::class])),
            array("CA Congrès (sur l'année) - total", $s->getTotalCa($year, array(), false, [FormationCongres::class])),
            array(''),
            array("CA Réseau EDUPRAT (sur l'année) - total - En cours", $s->getTotalCa($year, $currentAndpc, false, [FormationAudit::class, FormationPresentielle::class])),
            array("CA Réseau EDUPRAT (sur l'année) - total - Prévisionnel", $s->getTotalCa($year, $futureAndpc, false, [FormationAudit::class, FormationPresentielle::class])),
            array("CA Réseau EDUPRAT (sur l'année) - total - Réalisé", $s->getTotalCa($year, $closedAndpc, false, [FormationAudit::class, FormationPresentielle::class])),
            array(''),
            array("CA CMS (sur l'année) - total - En cours", $s->getTotalCa($year, $currentAndpc, true)),
            array("CA CMS (sur l'année) - total - Prévisionnel", $s->getTotalCa($year, $futureAndpc, true)),
            array("CA CMS (sur l'année) - total - Réalisé", $s->getTotalCa($year, $closedAndpc, true)),
            array(''),
            array("CA SEDD (sur l'année) - total - En cours", $s->getTotalCa($year, $currentAndpc, false, [FormationSedd::class])),
            array("CA SEDD (sur l'année) - total - Prévisionnel", $s->getTotalCa($year, $futureAndpc, false, [FormationSedd::class])),
            array("CA SEDD (sur l'année) - total - Réalisé", $s->getTotalCa($year, $closedAndpc, false, [FormationSedd::class])),
            array(''),
            array("CA Congrès (sur l'année) - total - En cours", $s->getTotalCa($year, $currentAndpc, false, [FormationCongres::class])),
            array("CA Congrès (sur l'année) - total - Prévisionnel", $s->getTotalCa($year, $futureAndpc, false, [FormationCongres::class])),
            array("CA Congrès (sur l'année) - total - Réalisé", $s->getTotalCa($year, $closedAndpc, false, [FormationCongres::class])),
            $delimiter
        );

        $tableCount = array(
            $delimiter,
            array('TABLEAU DE BORD - NOMBRE DE FORMATIONS  GLOBALES (Réseau Eduprat, CMS, SET, SEDD, Congrès)'),
            array(''),
            array('Nombre total de Formations', $s->getFormationCount($year)),
            array(''),
            array('Nombre de Formations Réseau Eduprat - Total', $s->getFormationCount($year, array(), false, [FormationAudit::class, FormationPresentielle::class])),
            array(''),
            array('Nombre de formations Réseau EDUPRAT - en Audit', $s->getFormationCount($year, array(), false, [FormationAudit::class])),
            array('Nombre de formations Réseau EDUPRAT - en Audit - En cours', $s->getFormationCount($year, $current, false, [FormationAudit::class])),
            array('Nombre de formations Réseau EDUPRAT - en Audit - Prévisionnel', $s->getFormationCount($year, $future, false, [FormationAudit::class])),
            array('Nombre de formations Réseau EDUPRAT - en Audit - Réalisé', $s->getFormationCount($year, $closed, false, [FormationAudit::class])),
            array(''),
            array('Nombre de formations Réseau EDUPRAT - en Présentiel', $s->getFormationCount($year, array(), false, [FormationPresentielle::class])),
            array('Nombre de formations Réseau EDUPRAT - en Présentiel - En cours', $s->getFormationCount($year, $current, false, [FormationPresentielle::class])),
            array('Nombre de formations Réseau EDUPRAT - en Présentiel - Prévisionnel', $s->getFormationCount($year, $future, false, [FormationPresentielle::class])),
            array('Nombre de formations Réseau EDUPRAT - en Présentiel - Réalisé', $s->getFormationCount($year, $closed, false, [FormationPresentielle::class])),
            array(''),
            array('Nombre de formations CMS', $s->getFormationCount($year, array(), true)),
            array('Nombre de formations CMS - En cours', $s->getFormationCount($year, $current, true)),
            array('Nombre de formations CMS - Prévisionnel', $s->getFormationCount($year, $future, true)),
            array('Nombre de formations CMS - Réalisé', $s->getFormationCount($year, $closed, true)),
            array(''),
            array('Nombre de formations SEDD', $s->getFormationCount($year, array(), false, [FormationSedd::class])),
            array('Nombre de formations SEDD - En cours', $s->getFormationCount($year, $current, false, [FormationSedd::class])),
            array('Nombre de formations SEDD - Prévisionnel', $s->getFormationCount($year, $future, false, [FormationSedd::class])),
            array('Nombre de formations SEDD - Réalisé', $s->getFormationCount($year, $closed, false, [FormationSedd::class])),
            array(''),
            array('Nombre de formations Congrès', $s->getFormationCount($year, array(), false, [FormationCongres::class])),
            array('Nombre de formations Congrès - En cours', $s->getFormationCount($year, $current, false, [FormationCongres::class])),
            array('Nombre de formations Congrès - Prévisionnel', $s->getFormationCount($year, $future, false, [FormationCongres::class])),
            array('Nombre de formations Congrès - Réalisé', $s->getFormationCount($year, array(), false, [FormationCongres::class])),
        );

        $tableCount = array_merge($tableCount, array(array('')), $formationAuditPerSubjectRows, array(array('')),$formationPresentiellePerSubjectRows, array(array('')),$formationCmsPerSubjectRows);
        $tableCount[] = $delimiter;

        $tableParticipant = array(
            $delimiter,
            array('TABLEAU DE BORD - LES STAGIAIRES'),
            array(''),
            array('Nombre total de PS en cours de formation', $s->getParticipantCount($year, $current)),
            array('Nombre total de PS formés', $s->getParticipantCount($year, $closed)),
            array(''),
            array('Nombre total de PS en cours de formation - Médecins', $s->getParticipantCount($year, $current, true)),
            array('Nombre total de PS en cours de formation - Autres', $s->getParticipantCount($year, $current, false)),
            array(''),
            array('Nombre total de PS formés - Médecins', $s->getParticipantCount($year, $closed, true)),
            array('Nombre total de PS formés - Autres', $s->getParticipantCount($year, $closed, false)),
            array(''),
            array('Nombre total de PS en cours de formation - Médecins - Audit', $s->getParticipantCount($year, $current, true, [FormationAudit::class])),
            array('Nombre total de PS en cours de formation - Autres - Audit', $s->getParticipantCount($year, $current, false, [FormationAudit::class])),
            array(''),
            array('Nombre total de PS en cours de formation - Médecins - Présentielle', $s->getParticipantCount($year, $current, true, [FormationPresentielle::class])),
            array('Nombre total de PS en cours de formation - Autres - Présentielle', $s->getParticipantCount($year, $current, false, [FormationPresentielle::class])),
            array(''),
            array('Nombre total de PS formés - Médecins - Audit', $s->getParticipantCount($year, $closed, true, [FormationAudit::class])),
            array('Nombre total de PS formés - Autres - Audit', $s->getParticipantCount($year, $closed, false, [FormationAudit::class])),
            array(''),
            array('Nombre total de PS formés - Médecins - Présentielle', $s->getParticipantCount($year, $closed, true, [FormationPresentielle::class])),
            array('Nombre total de PS formés - Autres - Présentielle', $s->getParticipantCount($year, $closed, false, [FormationPresentielle::class])),
            array(''),
            array('Nombre moyen de stagiaires par formation', $s->getAvgParticipantCount($year)),
            array('Nombre moyen de stagiaires par formation - Médecins', $s->getAvgParticipantCount($year, array(), true)),
            array('Nombre moyen de stagiaires par formation - Autres PS', $s->getAvgParticipantCount($year, array(), false)),
        );

        $tableParticipant = array_merge(
            $tableParticipant, array(array('')),
            $avgParticipationsCountPerCategoryRows, array(array('')),
            $medecinsCurrentSpecialityRows, array(array('')),
            $medecinsCurrentSpecialityAuditRows, array(array('')),
            $medecinsCurrentSpecialityPresentielleRows, array(array('')),
            $othersCurrentSpecialityRows, array(array('')),
            $othersCurrentSpecialityAuditRows, array(array('')),
            $othersCurrentSpecialityPresentielleRows, array(array('')),
            $medecinsClosedSpecialityRows, array(array('')),
            $medecinsClosedSpecialityAuditRows, array(array('')),
            $medecinsClosedSpecialityPresentielleRows, array(array('')),
            $othersClosedSpecialityRows, array(array('')),
            $othersClosedSpecialityAuditRows, array(array('')),
            $othersClosedSpecialityPresentielleRows, array(array('')),
            $medecinsFutureSpecialityRows, array(array('')),
            $medecinsFutureSpecialityAuditRows, array(array('')),
            $medecinsFutureSpecialityPresentielleRows, array(array('')),
            $othersFutureSpecialityRows, array(array('')),
            $othersFutureSpecialityAuditRows, array(array('')),
            $othersFutureSpecialityPresentielleRows
        );
        $tableParticipant[] = $delimiter;

        $tableHonorary = array(
            $delimiter,
            array('TABLEAU DE BORD - COMMISSIONS EDUPRAT GLOBAL'),
            array(''),
            array('Total des honoraires des CR', $s->getTotalHonoraryCr($year)),
            array(''),
            array("Total des honoraires prévisionnels (sur l'année)", $s->getTotalHonoraryCr($year, array(), false)),
            array("Total des honoraires dûs (sur l'année)", $s->getTotalHonoraryCr($year, array(), true)),
            $delimiter
        );

        return $data = array_merge($tableCa, $space, $tableCount, $space, $tableParticipant, $space, $tableHonorary);
    }

    public function exportCoordinator($tmpName, \DateTime $start, \DateTime $end, $supervisor = null) {
        $em = $this->entityManager;
        $participationRepository = $em->getRepository(Participation::class);

        $file = fopen($tmpName, 'w');
        $d = ',';
        $e = '"';
        $csvHeaderFlag = false;

        $participationsIds = $participationRepository->findByCoordinatorsTotalMedecin($start, $end);

        $csv = array();

        foreach ($participationsIds as $id) {

            $participation = $participationRepository->find($id["id"]);

            if($participation->getFormation()->getCoordinators()->count() > 1) {
                $coordinator = $participation->getCoordinator();
            }
            else {
                $coordinator = $participation->getFormation()->getCoordinators();
                $coordinator = $coordinator->first();
            }


            if($coordinator) {

                $coordinator = $coordinator->getPerson();

                if (!is_null($supervisor) && $coordinator->getSupervisor() && $coordinator->getSupervisor()->getId() !== (int) $supervisor) {
                    continue;
                }

                $cId = $coordinator->getId();
                $pStartDate = $participation->getFormation()->getStartDate();
                $cLastname = $coordinator->getLastname();
                $cFirstname = $coordinator->getFirstname();

                if(!isset($csv[$cId])) {
                    $csv[$cId] = array();
                }

                if(!isset($csv[$cId][$cLastname.' '.$cFirstname])) {
                    $csv[$cId][$cLastname.' '.$cFirstname] = array();
                }

                $year = $pStartDate->format('Y');
                $month = $pStartDate->format('m');

                if(!isset($csv[$cId][$cLastname.' '.$cFirstname][$year])) {
                    $csv[$cId][$cLastname.' '.$cFirstname][$year] = array();
                }

                if(!isset($csv[$cId][$cLastname.' '.$cFirstname][$year][$month])) {
                    $csv[$cId][$cLastname.' '.$cFirstname][$year][$month] = array('nombre' => 0, 'total' => 0);
                }

                if(!isset($csv[$cId][$cLastname.' '.$cFirstname][$year]['Total'])) {
                    $csv[$cId][$cLastname.' '.$cFirstname][$year]['Total'] = array('nombre' => 0, 'total' => 0);
                }

                $csv[$cId][$cLastname.' '.$cFirstname][$year][$month]['nombre'] += 1;
                $csv[$cId][$cLastname.' '.$cFirstname][$year][$month]['total'] += 760;

                $csv[$cId][$cLastname.' '.$cFirstname][$year]['Total']['nombre'] += 1;
                $csv[$cId][$cLastname.' '.$cFirstname][$year]['Total']['total'] += 760;
            }
        }

        uasort ($csv , array($this, 'myComparison'));

        foreach ($csv as $id => $coordinatorValue) {
            if(!$csvHeaderFlag) {
                $csvHeader = array('Coordinateur', 'Mois/Année', 'Nombre de Médecins (8h)', 'Total (€)');

                fputcsv($file, $csvHeader, $d, $e);
                fputcsv($file, array('','','',''), $d, $e);
                $csvHeaderFlag = true;
            }
            foreach($coordinatorValue as $name => $yearData) {
                $nameFlag = false;
                foreach ($yearData as $year => $monthData) {
                    $total = array();
                    $i = 0;
                    $len = count($monthData);
                    ksort($monthData, SORT_NATURAL);
                    foreach ($monthData as $month => $value) {
                        if($month == 'Total') {
                            $total = $value;
                        }
                        else {
                            if(!$nameFlag) {
                                $data = array($name, $month.'/'.$year);
                                $nameFlag = true;
                            }
                            else {
                                $data = array('', $month.'/'.$year);
                            }
                            $data = array_merge($data, $value);
                            fputcsv($file, $data, $d, $e);
                        }

                        if ($i == $len - 1) {
                            $data = array('', 'Total '.$year);
                            $data = array_merge($data, $total);
                            $total = array();
                            fputcsv($file, $data, $d, $e);
                            fputcsv($file, array('', '','','',''), $d, $e);
                        }

                        $i++;
                    }
                }
            }
        }

        fclose($file);
    }

    public function exportCollaborator($tmpName, \DateTime $start, \DateTime $end) {
        $em = $this->entityManager;
        $personRepository = $em->getRepository(Person::class);

        $file = fopen($tmpName, 'w');
        $d = ';';
        $e = '"';

        $persons = $personRepository->findByPeriod($start, $end);

        $csv = array();

        uasort ($csv , array($this, 'myComparison'));

        $csvHeader = array('Nom', 'Prénom', 'Rôle', 'Adresse mail', 'Profession', 'Téléphone', 'Adresse 1', 'Adresse 2', 'Code postal', 'Ville', 'N°SIRET', 'Nombre de formations', 'Note des participants', 'Note des formateurs', 'Note des coordinateurs', 'Moyenne de l\'ensemble des note');
        array_walk($csvHeader, function(&$value, $key) {
            try {
                $value = mb_convert_encoding($value, "Windows-1252", "UTF-8");
            } catch (\Exception $e) {}
        });

        fputcsv($file, $csvHeader, $d, $e);
        fputcsv($file, array('','','','','','','','','','','','','','','',''), $d, $e);

        foreach($persons as $p) {

            $role = $p->isFormer() || $p->isCoordinator() ? $p->getRoleIdentifier() : false;

            if($role) {
                $elements = $p->isFormer()? $p->getFormersFormationDonesByPeriod($start, $end) : $p->getCoordinatorsFormationDonesByPeriod($start, $end);

                $nbFormations = count($elements);

                $evalParticipantSum = 0;
                $evalParticipantCount = 0;
                $evalFormerSum = 0;
                $evalFormerCount = 0;
                $evalCoordinateurSum = 0;
                $evalCoordinateurCount = 0;

                foreach($elements as $element) {
                    $evalParticipantSum += $element->getEvaluationParticipantAnswersSum();
                    $evalParticipantCount += $element->getEvaluationParticipantAnswersCount();
                    if($p->isFormer()) {
                        $evalCoordinateurSum += $element->getEvaluationCoordinatorAnswersSum();
                        $evalCoordinateurCount += $element->getEvaluationFormerAnswersCount();
                    } else {
                        $evalFormerSum += $element->getEvaluationFormerAnswersSum();
                        $evalFormerCount += $element->getEvaluationFormerAnswersCount();
                    }
                }

                $evalParticipantRating = $evalParticipantSum && $evalParticipantCount !== 0 ? $evalParticipantSum / $evalParticipantCount : 0;
                $evalFormerRating = $evalFormerSum && $evalFormerCount !== 0 ? $evalFormerSum / $evalFormerCount : 0;
                $evalCoordinateurRating = $evalCoordinateurSum && $evalCoordinateurCount !== 0 ? $evalCoordinateurSum / $evalCoordinateurCount : 0;

                $totalRating = $evalParticipantRating || $evalFormerRating || $evalCoordinateurRating !== 0 ? ($evalParticipantSum + $evalFormerSum + $evalCoordinateurSum) / ($evalParticipantCount + $evalFormerCount + $evalCoordinateurCount) : 0;

                $data = array($p->getFirstName(), $p->getLastName(), $role, $p->getEmail(), $p->getJob(), $p->getPhone(), $p->getAddress(), $p->getAddress2(), $p->getZipCode(), $p->getCity(), $p->getSiret(), $nbFormations, round($evalParticipantRating, 2), round($evalFormerRating, 2), round($evalCoordinateurRating, 2), round($totalRating, 2));
                array_walk($data, function(&$value, $key) {
                    try {
                        $value = mb_convert_encoding($value, "Windows-1252", "UTF-8");
                    } catch (\Exception $e) {}
                });
                fputcsv($file, $data, $d, $e);
            }
        }

        fclose($file);
    }

    public function exportSatisfactionComms($tmpName, \DateTime $start, \DateTime $end) {
        $em = $this->entityManager;
        $evaluationGlobalAnswer = $em->getRepository(EvaluationGlobalAnswer::class);
        $participationRepository = $em->getRepository(Participation::class);

        $file = fopen($tmpName, 'w');
        $d = ';';
        $e = '"';

        $globalAnswers = $evaluationGlobalAnswer->findByPeriod($start, $end);


        $csv = array();

        uasort ($csv , array($this, 'myComparison'));

        $csvHeader = array(
            'Prénom',
            'Nom',
            'Rôle',
            'Catégorie professionnelle',
            'Spécialité',
            'Programme',
            'Date de réunion',
            "Référence",
            "N° de session",
            "Présence",
            "Coordinateur",
            "Date de réponse",
            'Commentaire'
        );
        array_walk($csvHeader, function(&$value, $key) {
            try {
                $value = mb_convert_encoding($value, "Windows-1252", "UTF-8");
            } catch (\Exception $e) {}
        });

        fputcsv($file, $csvHeader, $d, $e);
        fputcsv($file, array('','','','', '','','',''), $d, $e);

        foreach($globalAnswers as $answer) {
            if ($answer->getQuestion() === "comment_1") {
                $p = $answer->getPerson();
                $formation = $answer->getFormation();
                $commentaire = str_replace(CHR(13).CHR(10),"",$answer->getAnswer());
                $commentaire = str_replace(";", ",", $commentaire);

                $firstName = !is_null($p->getFirstName()) ? $p->getFirstName() : $p->getParticipant()->getFirstName();
                $lastName = !is_null($p->getLastName()) ? $p->getLastName() : $p->getParticipant()->getLastName();
                $role = !is_null($p->getRoleIdentifier()) ? $p->getRoleIdentifier() : "participant";

                $category = $p->getParticipant() ? $p->getParticipant()->getCategory() : "";
                $speciality = $p->getParticipant() ? $p->getParticipant()->getSpeciality() : "";
                $presence = $formation->getProgramme()->getPresence();

                $participations = $participationRepository->findByPersonFormation($formation, $p);

                $coordinator = "";
                if (count($participations) >= 1 && $participations[0]->getFormation()->getCoordinators()->count()) {
                    $coordinator = $participations[0]->getCoordinator() ?? $participations[0]->getFormation()->getCoordinators()->first();
                    if ($coordinator) {
                        $coordinator = $coordinator->getPerson()->getFullname();
                    }
                }

                $answerDate = $answer->getCreatedAt()->format("d/m/Y");

                $data = array($firstName, $lastName, $role, $category, $speciality, $formation->getProgramme()->getTitle(), date_format($formation->getStartDate(), "Y-m-d"), $formation->getProgramme()->getReference(), $formation->getSessionNumber(), $presence, $coordinator, $answerDate, $commentaire);
                array_walk($data, function(&$value, $key) {
                    try {
                        $value = mb_convert_encoding($value, "Windows-1252", "UTF-8");
                    } catch (\Exception $e) {}
                });
                fputcsv($file, $data, $d, $e);
            }
        }

        fclose($file);
    }

    public function exportEtutoratComms($tmpName, \DateTime $start, \DateTime $end) {
        $em = $this->entityManager;
        $participationRepository = $em->getRepository(Participation::class);

        $file = fopen($tmpName, 'w');
        $d = ';';
        $e = '"';

        $participations = $participationRepository->findByPeriodEtutoratComms($start, $end);

        $csv = array();

        uasort ($csv , array($this, 'myComparison'));

        $csvHeader = array(
            'Prénom',
            'Nom',
            '"E-tutorat Etape 1',
            '"E-tutorat Etape 3',
            '"E-tutorat Etape 4',
            'Catégorie professionnelle',
            'Spécialité',
            'Programme',
            'Date de réunion',
            "Référence",
            "N° de session",
            "Présence",
            "Coordinateur",
        );
        array_walk($csvHeader, function(&$value, $key) {
            try {
                $value = mb_convert_encoding($value, "Windows-1252", "UTF-8");
            } catch (\Exception $e) {}
        });

        fputcsv($file, $csvHeader, $d, $e);
        fputcsv($file, array('','','','', '','','',''), $d, $e);

        foreach($participations as $participation) {
            $p = $participation->getParticipant();
            $formation = $participation->getFormation();

            $firstName = !is_null($p->getFirstName()) ? $p->getFirstName() : $p->getParticipant()->getFirstName();
            $lastName = !is_null($p->getLastName()) ? $p->getLastName() : $p->getParticipant()->getLastName();
            $presence = $formation->getProgramme()->getPresence();

            $coordinator = $participation->getCoordinator() ? $participation->getCoordinator()->getPerson()->getFullname() :  "";


            $data = array($firstName, $lastName, $participation->getEtutoratAttentes(), $participation->getEtutorat2Message(), $participation->getEtutorat3Message(), $p->getCategory(), $p->getSpeciality(), $formation->getProgramme()->getTitle(), date_format($formation->getStartDate(), "Y-m-d"), $formation->getProgramme()->getReference(), $formation->getSessionNumber(), $presence, $coordinator);
            array_walk($data, function(&$value, $key) {
                try {
                    $value = mb_convert_encoding($value, "Windows-1252", "UTF-8");
                } catch (\Exception $e) {}
            });
            fputcsv($file, $data, $d, $e);
        }

        fclose($file);
    }

    public function exportSupervisor($tmpName, $start, $end) {
        $em = $this->entityManager;
        $participationRepository = $em->getRepository(Participation::class);

        $file = fopen($tmpName, 'w');
        $d = ',';
        $e = '"';
        $csvHeaderFlag = false;

        $participationsIds = $participationRepository->findByCoordinatorsTotalMedecin($start, $end);

        $csv = array();

        foreach ($participationsIds as $id) {

            $participation = $participationRepository->find($id["id"]);

            if($participation->getFormation()->getCoordinators()->count() > 1) {
                $coordinator = $participation->getCoordinator();
            }
            else {
                $coordinator = $participation->getFormation()->getCoordinators();
                $coordinator = $coordinator->first();
            }

            if($coordinator) {
                $supervisor = $coordinator->getPerson()->getSupervisor();

                $sId = $supervisor->getId();
                $pStartDate = $participation->getFormation()->getStartDate();
                $sLastname = $supervisor->getLastname();
                $sFirstname = $supervisor->getFirstname();

                if(!isset($csv[$sId])) {
                    $csv[$sId] = array();
                }

                if(!isset($csv[$sId][$sLastname.' '.$sFirstname])) {
                    $csv[$sId][$sLastname.' '.$sFirstname] = array();
                }

                $year = $pStartDate->format('Y');
                $month = $pStartDate->format('m');

                if(!isset($csv[$sId][$sLastname.' '.$sFirstname][$year])) {
                    $csv[$sId][$sLastname.' '.$sFirstname][$year] = array();
                }

                if(!isset($csv[$sId][$sLastname.' '.$sFirstname][$year][$month])) {
                    $csv[$sId][$sLastname.' '.$sFirstname][$year][$month] = array('nombre' => 0, 'total' => 0);
                }

                if(!isset($csv[$sId][$sLastname.' '.$sFirstname][$year]['Total'])) {
                    $csv[$sId][$sLastname.' '.$sFirstname][$year]['Total'] = array('nombre' => 0, 'total' => 0);
                }

                $csv[$sId][$sLastname.' '.$sFirstname][$year][$month]['nombre'] += 1;
                $csv[$sId][$sLastname.' '.$sFirstname][$year][$month]['total'] += 760;

                $csv[$sId][$sLastname.' '.$sFirstname][$year]['Total']['nombre'] += 1;
                $csv[$sId][$sLastname.' '.$sFirstname][$year]['Total']['total'] += 760;
            }
        }

        uasort ($csv , array($this, 'myComparison'));

        foreach ($csv as $id => $supervisorValue) {
            if (!$csvHeaderFlag) {
                $csvHeader = array('Superviseur', 'Mois/Année', 'Nombre de Médecins (8h)', 'Total (€)');

                fputcsv($file, $csvHeader, $d, $e);
                fputcsv($file, array('','','',''), $d, $e);
                $csvHeaderFlag = true;
            }
            foreach ($supervisorValue as $name => $yearData) {
                $nameFlag = false;
                foreach ($yearData as $year => $monthData) {
                    $total = array();
                    $i = 0;
                    $len = count($monthData);
                    ksort($monthData, SORT_NATURAL);
                    foreach ($monthData as $month => $value) {
                        if ($month == 'Total') {
                            $total = $value;
                        } else {
                            if (!$nameFlag) {
                                $data = array($name, $month.'/'.$year);
                                $nameFlag = true;
                            } else {
                                $data = array('', $month.'/'.$year);
                            }
                            $data = array_merge($data, $value);
                            fputcsv($file, $data, $d, $e);
                        }

                        if ($i == $len - 1) {
                            $data = array('', 'Total '.$year);
                            $data = array_merge($data, $total);
                            $total = array();
                            fputcsv($file, $data, $d, $e);
                            fputcsv($file, array('','','',''), $d, $e);
                        }

                        $i++;
                    }
                }
            }
        }

        fclose($file);
    }


    public function exportCarto($tmpName) {
        $em = $this->entityManager;
        $participantRepository = $em->getRepository(Participant::class);

        $file = fopen($tmpName, 'w');
        $d = ';';
        $e = '"';

        $participantIds = $participantRepository->findParticipantsIds();

        $csv = array();

        $keys = array(
            "id",
            "Nom Prénom",
            "Adresse",
            "CP",
            "Ville",
            "Spécialités",
            "latitude",
            "longitude",
            "tel",
            "email",
            "Heures 2017",
            "Heures 2018",
            "Heures 2019",
            "Heures 2020",
            "Heures 2021",
            "Theme formation 1",
            "Date formation 1",
            "Theme formation 2",
            "Date formation 2",
            "Theme formation 3",
            "Date formation 3",
            "Theme formation 4",
            "Date formation 4",
            "Theme formation 5",
            "Date formation 5",
            "Theme formation 6",
            "Date formation 6",
            "Theme formation 7",
            "Date formation 7",
            "Theme formation 8",
            "Date formation 8",
            "Theme formation 9",
            "Date formation 9",
            "Theme formation 10",
            "Date formation 10",
            "Theme formation 11",
            "Date formation 11",
            "Theme formation 12",
            "Date formation 12",
            "Theme formation 13",
            "Date formation 13",
            "Theme formation 14",
            "Date formation 14",
            "Theme formation 15",
            "Date formation 15",
            "Theme formation 16",
            "Date formation 16",
            "Theme formation 17",
            "Date formation 17",
            "Theme formation 18",
            "Date formation 18",
            "Theme formation 19",
            "Date formation 19",
            "Theme formation 20",
            "Date formation 20",
            "code_RPPS",
            "Nombre de formation 2017",
            "Nombre de formation 2018",
            "Nombre de formation 2019",
            "Nombre de formation 2020",
            "Nombre de formation 2021",
            "Nombre de formation total",
            "Num_ADELI",
        );

        array_walk($keys, function(&$value, $key) {
            $value = iconv('UTF-8', 'Windows-1252', $value);
        });
        fputcsv($file, $keys, $d, $e);

        $needFlush = false;

        foreach ($participantIds as $k => $id) {
            /** @var Participant $p */
            $p = $participantRepository->find($id["id"]);

            if (!$p instanceof Participant) {
                continue;
            }

            if (is_null($p->getLatitude()) && is_null($p->getLongitude())) {
                $address = sprintf("%s %s %s", $p->getAddress(), ltrim($p->getZipCode(), '0'), $p->getCity());
                $result = $this->adressesService->getGeocodeAdresse($address);

                if (count($result["adresse"])) {
                    $coordonnees = explode(",", $result["adresse"][0]["coordonnees"]);
                    $lat = $coordonnees[0];
                    $lon = $coordonnees[1];
                    $p->setLatitude($lat);
                    $p->setLongitude($lon);
                    $em->persist($p);
                    $needFlush = true;
                }
            }

            $a = array(
                $p->getId(),
                $p->getLastname() . " " . $p->getFirstname(),
                $p->getAddress(),
                $p->getZipCode(),
                $p->getCity(),
                $p->getSpeciality(),
                $p->getLatitude() ? $p->getLatitude() : "",
                $p->getLongitude() ? $p->getLongitude() : "",
                $p->getPhone(),
                $p->getEmail(),
            );

            $yearsHours = array(
                "2017" => 0,
                "2018" => 0,
                "2019" => 0,
                "2020" => 0,
                "2021" => 0,
            );

            $yearsCount = array(
                "2017" => 0,
                "2018" => 0,
                "2019" => 0,
                "2020" => 0,
                "2021" => 0,
            );

            $participations = $p->getParticipations();

            foreach ($participations as $participation) {
                $yearsHours[$participation->getFormation()->getStartDate()->format("Y")] += $participation->getNbHour();
                $yearsCount[$participation->getFormation()->getStartDate()->format("Y")]++;
            }

            foreach ($yearsHours as $year => $value) {
                $a = array_merge($a, array(
                    $value
                ));
            }

            $participations = $participations->slice(0, 20);

            for ($i = 0; $i < 20; $i++) {
                if (isset($participations[$i])) {
                    $f = $participations[$i]->getFormation()->getProgramme();
                    $a = array_merge($a, array(
                        $f->getTitle(),
                        $participations[$i]->getFormation()->getStartDate()->format("d/m/Y")
                    ));

                } else {
                    $a = array_merge($a, array(
                        "",
                        ""
                    ));
                }
            }

            $a = array_merge($a, array(
               $p->getRpps()
            ));

            foreach ($yearsCount as $year => $value) {
                $a = array_merge($a, array(
                    $value
                ));
            }

            $a = array_merge($a, array(
                array_sum(array_values($yearsCount))
            ));

            $a = array_merge($a, array(
                $p->getAdeli()
            ));

            $a = array_combine($keys, $a);

            $csv[] = $a;

            if ($k % 100 === 0) {
                var_dump($k);
                foreach ($csv as $id => $item) {
                    array_walk($item, function(&$value, $key) {
                        $value = iconv('UTF-8', 'Windows-1252', $value);
                    });
                    fputcsv($file, $item, $d, $e);
                }
                $csv = array();
                if ($needFlush) {
                    $em->flush();
                    $needFlush = false;
                }
                $em->clear();
            }
        }

        foreach ($csv as $id => $item) {
            array_walk($item, function(&$value, $key) {
                $value = iconv('UTF-8', 'Windows-1252', $value);
            });
            fputcsv($file, $item, $d, $e);
        }

        fclose($file);
    }


    public function exportParticipantSendinblue($tmpName, Person $person, $parameters) {

        $em = $this->entityManager;

        /** @var ParticipantSearch $participantSearch */
        $participantSearch = $this->searchHandler->handleArray(new ParticipantSearch(), $parameters);
        $participantSearch->setUserFilters($person);
        $participantRepository = $em->getRepository(Participant::class);

        $file = fopen($tmpName, 'w');
        $d = ';';
        $e = '"';

        /** @var Person[] $csvPersons */
        $csvPersons = $participantRepository->findSearchResultsCsv($participantSearch);

        fputcsv($file, ["email"], $d, $e);

        /** @var Participant $csvPerson */
        foreach($csvPersons as $csvPerson) {

            fputcsv($file, [$csvPerson->getEmail()], $d, $e);
        }

        fclose($file);
    }

    public function exportParticipationMissingModules($tmpName, Person $person, $parameters, CourseManager $courseManager, ComptabiliteService $comptabiliteService) {
        $em = $this->entityManager;

        /** @var ComptabiliteSearch $comptabiliteSearch */
        $comptabiliteSearch = $this->searchHandler->handleArray(new ComptabiliteSearch(), $parameters);
        $comptabiliteSearch->setUserFilters($person);

        $file = fopen($tmpName, 'w');
        $d = ';';
        $e = '"';

        $missingModules = $comptabiliteService->getMissingParticipationModules($comptabiliteSearch, isCsv: true);

        $csvHeader = array(
            "Nom",
            "Prenom",
            "Email",
            "Module manquant",
            "Etape",
            "Reference",
            "Session",
            "Type de session",
            "Coordinateur",
            "Echeance",
        );

        array_walk($csvHeader, function(&$value, $key) {
            $value = mb_convert_encoding($value, "Windows-1252", "UTF-8");
        });

        fputcsv($file, $csvHeader, $d, $e);

        foreach($missingModules as $missingModule) {
            $participation = $missingModule["participation"];
            $sessionType = $participation->getFormation()->isElearning() ? $this->translator->trans('admin.formation.type.formation_elearning') : $this->translator->trans('admin.formation.type.'.$participation->getFormation()->getDisplayType());
            $data = array(
                "Nom" => $participation->getParticipant()->getLastname(),
                "Prenom" => $participation->getParticipant()->getFirstname(),
                "Email" => $participation->getParticipant()->getEmail(),
                "Module manquant" => $participation->getNextModule(),
                "Etape" => $courseManager->getModuleStep($participation->getNextModule()),
                "Reference" => $participation->getFormation()->getProgramme()->getReference(),
                "Session" => $participation->getFormation()->getSessionNumber(),
                "Type de session" => $sessionType,
                "Coordinateur" => $participation->getAssociatedCoordinator() ? $participation->getAssociatedCoordinator()->getPerson()->getInvertedFullname() : "",
                "Echeance" => $missingModule["dateModuleManquantExpiration"]?->format('d/m/Y'),
            );

            array_walk($data, function(&$value, $key) {
                $value = mb_convert_encoding((string)$value, "Windows-1252", "UTF-8");
            });

            fputcsv($file, $data, $d, $e);
        }

        fclose($file);
    }

    public function exportParticipantionMissingAttestations($tmpName, Person $person, $parameters, $parcoursV3MigrationDate) {
        $em = $this->entityManager;

        /** @var AttestationComptabiliteSearch $attestationSearch */
        $attestationSearch = $this->searchHandler->handleArray(new AttestationComptabiliteSearch(), $parameters);
        $attestationSearch->setUserFilters($person);
        $participantionRepository = $em->getRepository(Participation::class);

        $file = fopen($tmpName, 'w');
        $d = ';';
        $e = '"';
        $csvHeaderFlag = false;

        $missingAttestations = $participantionRepository->getMissingAttestationFilesQuery($attestationSearch, $parcoursV3MigrationDate)->getQuery()->getResult();

        var_dump(count($missingAttestations));

        $csvHeader = array(
            "Nom",
            "Prenom",
            "Email",
            "Reference",
            "Session",
            "Presence",
            "Gestion",
            "Coordinateur",
            "Attestation N",
            "Attestation N+1",
            "Echeance N",
            "Echeance N+1",
            "Parcours pre termine",
            "Parcours post termine",
        );

        array_walk($csvHeader, function(&$value, $key) {
            $value = mb_convert_encoding($value, "Windows-1252", "UTF-8");
        });

        fputcsv($file, $csvHeader, $d, $e);
        foreach($missingAttestations as $missingAttestation) {
            $formation = $missingAttestation->getFormation();
            $course = $this->courseManager->getCourseDetail($missingAttestation);
            $parcoursPreTermine = $this->courseManager->isCoursePreCompleted($missingAttestation) ? "oui" : "non";
            $missingN = !$missingAttestation->getAttestationHonneur() && $formation->isMissingAttestation() ? "manquante" : "ok";
            $echeanceN = $formation->getAttestationEcheanceN() ? $formation->getAttestationEcheanceN()->format('d/m/Y') : "";
            $missingN1 = "";
            $echeanceN1 = "";
            if ($formation->isPluriannuelle() && $formation->shouldDisplayAttestationN1()) {
                $missingN1 = !$missingAttestation->getAttestationHonneurN1() && $formation->isMissingAttestationN1() ? "manquante" : "ok";
                $echeanceN1 = $formation->getAttestationEcheanceN1() ? $formation->getAttestationEcheanceN1()->format('d/m/Y') : "";
            }
            if ($this->courseManager->hasModule($course, "synthese")) {
                $parcoursTermine = $missingAttestation->isStepCompleted("synthese") ? "oui" :  "non";
            } else {
                if ($missingAttestation->getFormation()->getOpeningDate() >= $parcoursV3MigrationDate) {
                    $parcoursTermine = $missingAttestation->isStepCompleted("end") ? "oui" :  "non";
                } else {
                    $parcoursTermine = $missingAttestation->getCompletedForms() ? "oui" :  "non";
                }
            }

            $data = array(
                "Nom" => $missingAttestation->getParticipant()->getLastname(),
                "Prenom" => $missingAttestation->getParticipant()->getFirstname(),
                "Email" => $missingAttestation->getParticipant()->getEmail(),
                "Reference" => $formation->getProgramme()->getReference(),
                "Session" => $formation->getSessionNumber(),
                "Presence" => $formation->getProgramme()->getPresence(),
                "Gestion" => count($formation->getCoordinators()) > 1 ? "Multi CR" : "Individuelle",
                "Coordinateur" => $missingAttestation->getAssociatedCoordinator() ? $missingAttestation->getAssociatedCoordinator()->getPerson()->getInvertedFullname() : "",
                "Attestation pre" => $missingN,
                "Attestation post" => $missingN1,
                "Echeance pre" => $echeanceN,
                "Echeance post" => $echeanceN1,
                "Parcours pre termine" => $parcoursPreTermine,
                "Parcours post termine" => $parcoursTermine,
            );

            array_walk($data, function(&$value, $key) {
                $value = mb_convert_encoding($value, "Windows-1252", "UTF-8");
            });

            fputcsv($file, $data, $d, $e);
        }

        fclose($file);
    }

    public function exportParticipant($tmpName, Person $person, $parameters) {
        $em = $this->entityManager;

        /** @var ParticipantSearch $participantSearch */
        $participantSearch = $this->searchHandler->handleArray(new ParticipantSearch(), $parameters);
        $participantSearch->setUserFilters($person);
        $participantSearch->regionZipCodes = $this->regions->getDepsByRegion($participantSearch->region);
        $participantRepository = $em->getRepository(Participant::class);

        $file = fopen($tmpName, 'w');
        $d = ';';
        $e = '"';
        $csvHeaderFlag = false;

        /** @var Person[] $csvPersons */
        $csvPersons = $participantRepository->findSearchResultsCsv($participantSearch);

        var_dump(count($csvPersons));

        /** @var Participant $csvPerson */
        foreach($csvPersons as $csvPerson) {
            if(!$csvHeaderFlag) {
                $csvHeader = array();
                foreach($csvPerson->__toArray(true) as $key => $value) {
                    $csvHeader[] = $this->translator->trans('admin.participant.'.$key);
                }
                fputcsv($file, $csvHeader, $d, $e);
                $csvHeaderFlag = true;
            }
            $data = $csvPerson->__toArray(true);

            if($data['created']) {
                $data['created'] = $this->translator->trans('admin.global.yes');
            }
            else {
                $data['created'] = $this->translator->trans('admin.global.no');
            }
            if (isset($data['zipCode'])) {
                $regions = [];
                foreach($this->regions->getRegions([$data['zipCode']]) as $regionName => $regionNum) {
                    $regions[] = $regionName;
                }
                $data['region'] = implode(', ', $regions);
            }

            $gdprAgreementHistory = $csvPerson->getGdprAgreementHistory();

            if ($gdprAgreementHistory) {
                $gdprAgreementEmailLast = null;
                $gdprAgreementEmailLastDate = null;
                $gdprAgreementEmailHistory = array();

                $gdprAgreementPostLast = null;
                $gdprAgreementPostLastDate = null;
                $gdprAgreementPostHistory = array();

                $gdprAgreementCallLast = null;
                $gdprAgreementCallLastDate = null;
                $gdprAgreementCallHistory = array();


                foreach ($gdprAgreementHistory as $item) {
                    if (isset($item["email"]) && $gdprAgreementEmailLast !== $item["email"]) {
                        $gdprAgreementEmailHistory[] = array("date" => $item["date"], "state" => $item["email"]);
                        $gdprAgreementEmailLast = $item["email"];
                    }

                    if (isset($item["post"]) && $gdprAgreementPostLast !== $item["post"]) {
                        $gdprAgreementPostHistory[] = array("date" => $item["date"], "state" => $item["post"]);
                        $gdprAgreementPostLast = $item["post"];
                    }

                    if (isset($item["call"]) && $gdprAgreementCallLast !== $item["call"]) {
                        $gdprAgreementCallHistory[] = array("date" => $item["date"], "state" => $item["call"]);
                        $gdprAgreementCallLast = $item["call"];
                    }

                    if (isset($item["notification"])) {
                        $data['gdprAgreementNotify'] = "OK le " . (new \DateTime($item["date"]))->format("d/m/Y H:i:s");
                    }
                }

                if (count($gdprAgreementEmailHistory)) {
                    $data['gdprAgreementEmailExportDate'] = $gdprAgreementEmailHistory[count($gdprAgreementEmailHistory) - 1]["date"];
                    array_pop($gdprAgreementEmailHistory);
                }
                if (count($gdprAgreementPostHistory)) {
                    $data['gdprAgreementPostExportDate'] = $gdprAgreementPostHistory[count($gdprAgreementPostHistory) - 1]["date"];
                    array_pop($gdprAgreementPostHistory);
                }
                if (count($gdprAgreementCallHistory)) {
                    $data['gdprAgreementCallExportDate'] = $gdprAgreementCallHistory[count($gdprAgreementCallHistory) - 1]["date"];
                    array_pop($gdprAgreementCallHistory);
                }

                $closure = function ($item) {
                    return ($item["state"] ? "Autorisé" : "Interdit") . " " . (new \DateTime($item["date"]))->format("d/m/Y H:i:s");
                };

                $data['gdprAgreementEmailExportHistory'] = join("\n", array_map($closure, array_reverse($gdprAgreementEmailHistory)));
                $data['gdprAgreementPostExportHistory'] = join("\n", array_map($closure, array_reverse($gdprAgreementPostHistory)));
                $data['gdprAgreementCallExportHistory'] = join("\n", array_map($closure, array_reverse($gdprAgreementCallHistory)));
            }


            if ($data['gdprAgreementEmailExport'] === true) {
                $data['gdprAgreementEmailExport'] = 'Autorisé';

            }
            elseif ($data['gdprAgreementEmailExport'] === false) {
                $data['gdprAgreementEmailExport'] = 'Interdit';
            }
            else {
                $data['gdprAgreementEmailExport'] = 'Inconnu';
            }

            if ($data['gdprAgreementPostExport'] === true) {
                $data['gdprAgreementPostExport'] = 'Autorisé';
            }
            elseif ($data['gdprAgreementPostExport'] === false) {
                $data['gdprAgreementPostExport'] = 'Interdit';
            }
            else {
                $data['gdprAgreementPostExport'] = 'Inconnu';
            }

            if ($data['gdprAgreementCallExport'] === true) {
                $data['gdprAgreementCallExport'] = 'Autorisé';
            }
            elseif ($data['gdprAgreementCallExport'] === false) {
                $data['gdprAgreementCallExport'] = 'Interdit';
            }
            else {
                $data['gdprAgreementCallExport'] = 'Inconnu';
            }
            fputcsv($file, $data, $d, $e);
        }

        fclose($file);
    }

    public function exportParticipationWithoutCoordinator($tmpName, $start, $end) {
        $em = $this->entityManager;

        $participationRepository = $em->getRepository(Participation::class);

        $file = fopen($tmpName, 'w');
        $d = ';';
        $e = '"';

        $csvHeader = array(
            "Référence",
            "Session",
            "Titre de la formation",
            "Nom participant",
            "Prénom participant",
            "Adresse",
            "Code postal",
            "Ville",
            "Dernier Coordinateur",
        );

        array_walk($csvHeader, function(&$value, $key) {
            $value = mb_convert_encoding($value, "Windows-1252", "UTF-8");
        });

        fputcsv($file, $csvHeader, $d, $e);

        /** @var Participation[] $participations */
        $participations = $participationRepository->findWithoutCoordinator($start, $end);

        $criteria = Criteria::create()->orderBy(['createdAt' => Order::Descending]);

        /** @var Participation $participation */
        foreach($participations as $participation) {
            $all = $participation->getParticipant()->getParticipations()->matching($criteria);
            /** @var Person|null $lastCoordinator */
            $lastCoordinator = null;
            if (count($all) > 1) {
                foreach ($all as $item) {
                    if ($item->getCoordinator() || $item->getFormation()->getCoordinators()->count() === 1) {
                        $lastCoordinator = $item->getCoordinator() ? $item->getCoordinator()->getPerson() : $item->getFormation()->getCoordinators()->first()->getPerson();
                        break;
                    }
                }
            }

            $data = array(
                "Référence" => $participation->getFormation()->getProgramme()->getReference(),
                "Session" => $participation->getFormation()->getSessionNumber(),
                "Titre de la formation" => $participation->getFormation()->getProgramme()->getTitle(),
                "Nom participant" => $participation->getParticipant()->getLastname(),
                "Prénom participant" => $participation->getParticipant()->getFirstname(),
                "Adresse" => $participation->getParticipant()->getAddress(),
                "Code postal" => $participation->getParticipant()->getZipCode(),
                "Ville" => $participation->getParticipant()->getCity(),
                "Dernier Coordinateur" => $lastCoordinator ? $lastCoordinator->getInvertedFullname() : "",
            );

            array_walk($data, function(&$value, $key) {
                $value = mb_convert_encoding($value, "Windows-1252", "UTF-8");
            });

            fputcsv($file, $data, $d, $e);
        }

        fclose($file);
    }

    public function exportFormationParticipants(Programme $programme, $tmpName) {

        $file = fopen($tmpName, 'w');
        $d = ';';
        $e = '"';
        $csvHeaderFlag = false;

        foreach($programme->getFormations() as $formation) {
            foreach($formation->getParticipations() as $participation) {
                $csvPerson = $participation->getParticipant();
                if(!$csvHeaderFlag) {
                    $csvHeader = array();
                    foreach($csvPerson->__toArray(true) as $key => $value) {
                        $csvHeader[] = $this->translator->trans('admin.participant.'.$key);
                    }
                    $csvHeader[] = "Mode de financement";
                    $csvHeader[] = "Sous mode de financement";
                    $csvHeader[] = "Numero de Session";
                    fputcsv($file, $csvHeader, $d, $e);
                    $csvHeaderFlag = true;
                }
                $data = $csvPerson->__toArray(true);

                $financeSousMode = $participation->getFinanceSousMode();

                if(!is_null($financeSousMode)) {
                    $financeMode = $financeSousMode->getFinanceMode()->getName();
                    $financeSousMode = $financeSousMode->getName();
                } else {
                    $financeMode = " ";
                    $financeSousMode = " ";
                }

                $data["Modes de financement"] = $financeMode;
                $data["Sous modes de financement"] = $financeSousMode;
                $data["NumeroSession"] = $formation->getSessionNumber();

                if($data['created']) {
                    $data['created'] = $this->translator->trans('admin.global.yes');
                }
                else {
                    $data['created'] = $this->translator->trans('admin.global.no');
                }
                if (isset($data['zipCode'])) {
                    $regions = [];
                    foreach($this->regions->getRegions([$data['zipCode']]) as $regionName => $regionNum) {
                        $regions[] = $regionName;
                    }
                    $data['region'] = implode(', ', $regions);
                }

                $gdprAgreementHistory = $csvPerson->getGdprAgreementHistory();

                if ($gdprAgreementHistory) {
                    $gdprAgreementEmailLast = null;
                    $gdprAgreementEmailLastDate = null;
                    $gdprAgreementEmailHistory = array();

                    $gdprAgreementPostLast = null;
                    $gdprAgreementPostLastDate = null;
                    $gdprAgreementPostHistory = array();

                    $gdprAgreementCallLast = null;
                    $gdprAgreementCallLastDate = null;
                    $gdprAgreementCallHistory = array();


                    foreach ($gdprAgreementHistory as $item) {
                        if (isset($item["email"]) && $gdprAgreementEmailLast !== $item["email"]) {
                            $gdprAgreementEmailHistory[] = array("date" => $item["date"], "state" => $item["email"]);
                            $gdprAgreementEmailLast = $item["email"];
                        }

                        if (isset($item["post"]) && $gdprAgreementPostLast !== $item["post"]) {
                            $gdprAgreementPostHistory[] = array("date" => $item["date"], "state" => $item["post"]);
                            $gdprAgreementPostLast = $item["post"];
                        }

                        if (isset($item["call"]) && $gdprAgreementCallLast !== $item["call"]) {
                            $gdprAgreementCallHistory[] = array("date" => $item["date"], "state" => $item["call"]);
                            $gdprAgreementCallLast = $item["call"];
                        }

                        if (isset($item["notification"])) {
                            $data['gdprAgreementNotify'] = "OK le " . (new \DateTime($item["date"]))->format("d/m/Y H:i:s");
                        }
                    }

                    if (count($gdprAgreementEmailHistory)) {
                        $data['gdprAgreementEmailExportDate'] = $gdprAgreementEmailHistory[count($gdprAgreementEmailHistory) - 1]["date"];
                        array_pop($gdprAgreementEmailHistory);
                    }
                    if (count($gdprAgreementPostHistory)) {
                        $data['gdprAgreementPostExportDate'] = $gdprAgreementPostHistory[count($gdprAgreementPostHistory) - 1]["date"];
                        array_pop($gdprAgreementPostHistory);
                    }
                    if (count($gdprAgreementCallHistory)) {
                        $data['gdprAgreementCallExportDate'] = $gdprAgreementCallHistory[count($gdprAgreementCallHistory) - 1]["date"];
                        array_pop($gdprAgreementCallHistory);
                    }

                    $closure = function ($item) {
                        return ($item["state"] ? "Autorisé" : "Interdit") . " " . (new \DateTime($item["date"]))->format("d/m/Y H:i:s");
                    };

                    $data['gdprAgreementEmailExportHistory'] = join("\n", array_map($closure, array_reverse($gdprAgreementEmailHistory)));
                    $data['gdprAgreementPostExportHistory'] = join("\n", array_map($closure, array_reverse($gdprAgreementPostHistory)));
                    $data['gdprAgreementCallExportHistory'] = join("\n", array_map($closure, array_reverse($gdprAgreementCallHistory)));
                }


                if ($data['gdprAgreementEmailExport'] === true) {
                    $data['gdprAgreementEmailExport'] = 'Autorisé';

                }
                elseif ($data['gdprAgreementEmailExport'] === false) {
                    $data['gdprAgreementEmailExport'] = 'Interdit';
                }
                else {
                    $data['gdprAgreementEmailExport'] = 'Inconnu';
                }

                if ($data['gdprAgreementPostExport'] === true) {
                    $data['gdprAgreementPostExport'] = 'Autorisé';
                }
                elseif ($data['gdprAgreementPostExport'] === false) {
                    $data['gdprAgreementPostExport'] = 'Interdit';
                }
                else {
                    $data['gdprAgreementPostExport'] = 'Inconnu';
                }

                if ($data['gdprAgreementCallExport'] === true) {
                    $data['gdprAgreementCallExport'] = 'Autorisé';
                }
                elseif ($data['gdprAgreementCallExport'] === false) {
                    $data['gdprAgreementCallExport'] = 'Interdit';
                }
                else {
                    $data['gdprAgreementCallExport'] = 'Inconnu';
                }
                fputcsv($file, $data, $d, $e);
            }
         }
        fclose($file);
    }

    public function exportParticipantAnalysis($tmpName, Person $person, $parameters) {
        $em = $this->entityManager;

        /** @var ParticipantSearch $participantSearch */
        $participantSearch = $this->searchHandler->handleArray(new ParticipantSearch(), $parameters);
        $participantSearch->setUserFilters($person);
        $participantSearch->isProspect = "false";
        $participantSearch->isActif = "true";
        $participantRepository = $em->getRepository(Participant::class);

        $file = fopen($tmpName, 'w');
        $d = ';';
        $e = '"';
        $csvHeaderFlag = false;

        /** @var Participant[] $csvParticipants */
        $csvParticipants = $participantRepository->findSearchResultsCsv($participantSearch, $parameters["sortBy"], $parameters["order"], true);

        var_dump(count($csvParticipants));

        /** @var Participant $participant */
        foreach($csvParticipants as $participant) {
            if(!$csvHeaderFlag) {
                $csvHeader = array();
                foreach($participant->__toArrayAnalysis() as $key => $value) {
                    $csvHeader[] = $this->translator->trans('admin.participant.'.$key);
                }

                $stats = $participant->getFormationsStats(true);

                foreach ($stats as $year => $formationsStat) {
                    $csvHeader[] = "Formations " . $year;
                }

                foreach (ProgrammeType::getPresences() as $presence) {
                    foreach ($stats as $year => $formationsStat) {
                        foreach ($formationsStat["presences"] as $fpresence => $count) {
                            if ($fpresence === $presence) {
                                $csvHeader[] = $presence . " " . $year;
                            }
                        }
                    }
                }

                foreach ($stats as $year => $formationsStat) {
                    $csvHeader[] = "Total Heures " . $year;
                }

                array_walk($csvHeader, function(&$value, $key) {
                    $value = mb_convert_encoding($value, "Windows-1252", "UTF-8");
                });

                fputcsv($file, $csvHeader, $d, $e);
                $csvHeaderFlag = true;
            }
            $data = $participant->__toArrayAnalysis();
            $stats = $participant->getFormationsStats(true);

            foreach ($stats as $year => $formationsStat) {
                $data['formations_' . $year] = $formationsStat["formations"];
            }

            foreach (ProgrammeType::getPresences() as $presence) {
                foreach ($stats as $year => $formationsStat) {
                    foreach ($formationsStat["presences"] as $fpresence => $count) {
                        if ($fpresence === $presence) {
                            $data['formations_' . $presence . '_' .$year] = $count;
                        }
                     }
                }
            }

            foreach ($stats as $year => $formationsStat) {
                $data['hours_' . $year] = $formationsStat["hours"];
            }

            array_walk($data, function(&$value, $key) {
                $value = mb_convert_encoding($value, "Windows-1252", "UTF-8");
            });

            fputcsv($file, $data, $d, $e);
        }

        fclose($file);
    }

    public function exportSessions($tmpName, \DateTime $start, \DateTime $end) {
        $em = $this->entityManager;
        $formationRepository = $em->getRepository(Formation::class);

        $file = fopen($tmpName, 'w');
        $d = ';';
        $e = '"';

        $search = new SessionSearch();
        $search->querySearchStartDate = $start;
        $search->querySearchEndDate = $end;
        $search->archived = "non";
        $formations = $formationRepository->findSearchResults($search, 1, 100000);

        $formations = array_reverse($formations);

        $csv = array();

        uasort ($csv , array($this, 'myComparison'));

        $csvHeader = array(
            "Date de début de la réunion",
            "Titre du programme",
            "Présence",
            "Coordinateur(s)",
            "Formateur(s)",
            "Adresse 1",
            "Adresse 2",
            "Code postal",
            "Ville",
            "Type de session",
            "Référence",
            "N° de session",
            "Date d'ouverture",
            "Date de clôture",
            "Questionnaire rattaché",
            "Titre du questionnaire associé",
            "Clôturée",
            "Avec topo",
            "Nombre de participants",
            "CA",
            "CA N+1",
            "Partenariat",
            "OTS",
            "Journée/Congrès",
        );

        array_walk($csvHeader, function(&$value, $key) {
            $value = mb_convert_encoding($value, "Windows-1252", "UTF-8");
        });

        fputcsv($file, $csvHeader, $d, $e);
        fputcsv($file, array('','','','','', '','','','','','','','', '','','','','',''), $d, $e);

        /** @var Formation $formation */
        foreach($formations as $formation) {
            $sessionType = $formation->isElearning() ? $this->translator->trans('admin.formation.type.formation_elearning') : $this->translator->trans('admin.formation.type.'.$formation->getDisplayType());
            $data = array(
                $formation->getStartDate()->format("d/m/Y H:i:s"),
                $formation->getProgramme()->getTitle(),
                $formation->getProgramme()->getPresence(),
                join(", ", $formation->getCoordinatorname()),
                join(", ", $formation->getFormateursNames()),
                $formation->getAddress(),
                $formation->getAddress2(),
                $formation->getZipCode(),
                $formation->getCity(),
                $sessionType,
                $formation->getProgramme()->getReference(),
                $formation->getSessionNumber(),
                $formation->getOpeningDate() ? $formation->getOpeningDate()->format("d/m/Y") : "",
                $formation->getClosingDate() ? $formation->getClosingDate()->format("d/m/Y") : "",
                $formation->hasLinkedForm() ? "Oui" : "Non",
                $formation->hasLinkedForm() ? $formation->getForm()->getLabel() : "",
                $formation->getClosed() ? "Oui" : "Non",
                $formation->getTopoFiles()->count() ? "Oui" : "Non",
                $formation->getParticipantCount(),
                str_replace(".", ",", (string)$formation->getCaTotal()),
                str_replace(".", ",", (string)$formation->getCaTotal(true)),
                $formation->getPartenariat(),
                $formation->getOts(),
                $formation->getFormatPeriod(),
            );

            array_walk($data, function(&$value, $key) {
                try {
                    $value = mb_convert_encoding($value, "Windows-1252", "UTF-8");
                } catch (\Exception $e) {}
            });

            fputcsv($file, $data, $d, $e);
        }

        fclose($file);
    }

    public function exportSessionsFullJson(\DateTime $start, \DateTime $end, $dateType) {
        $em = $this->entityManager;
        $formationRepository = $em->getRepository(Formation::class);


        $search = new SessionSearch();
        $search->archived = "non";
        $search->status = "future";
        if ($dateType == "creation") {
            $search->startCreationDate = $start;
            $search->endCreationDate = $end;
        } else {
            $search->startUpdateDate = $start;
            $search->endUpdateDate = $end;
        }
        $formations = $formationRepository->findSearchResults($search, 1, 100000);

        $formations = array_reverse($formations);
        $formationsToArray = [];

        /** @var Formation $formation */
        foreach($formations as $formation) {

            $pictureFile = "";
            if($formation->getProgramme()->getPictureFile()) {
                $pictureFile = $this->packages->getUrl($this->uploaderHelper->asset($formation->getProgramme(), 'pictureFile', Programme::class));
            }

            $coordinators = $formation ->getCoordinators();

            $coordinatorsArray = [];
            $specialitesArray = [];
            $publicsArray = [];

            foreach ($coordinators as $coordinator) {
                array_push($coordinatorsArray, ["name" => $coordinator->getPerson()->getInvertedFullname(), "id" => $coordinator->getPerson()->getId()]);
            }
            $thematique = $formation->hasLinkedForm() ? ["name" => $formation->getForm()->getCategory()->getName(), "id" => $formation->getForm()->getCategory()->getId()] : "";
            $sessionType = $formation->isElearning() ? $this->translator->trans('admin.formation.type.formation_elearning') : $this->translator->trans('admin.formation.type.'.$formation->getDisplayType());

            $formationToArray =
            [
                "Date de début de la réunion" => $formation->getStartDate()->format("d/m/Y"),
                "Heure de debut de la réunion" => $formation->getStartDate()->format("H:i:s"),
                "Date de fin de la réunion" => $formation->getEndDate()->format("d/m/Y"),
                "Heure de fin de la réunion" => $formation->getEndDate()->format("H:i:s"),
                "Titre du programme" => $formation->getProgramme()->getTitle(),
                "Image associé au programme" => $pictureFile,
                "Présence" => $formation->getProgramme()->getPresence(),
                "Coordinateur(s)" => $coordinatorsArray,
                "Formateur(s)" => join(", ", $formation->getFormateursNames()),
                "Publics" => $formation->getProgramme()->getCategories() ? join(", ", $formation->getProgramme()->getCategories()) : "",
                "Spécialités" => $formation->getProgramme()->getSpecialities() ? join(", ", $formation->getProgramme()->getSpecialities()) : "",
                "Résumé de la formation" => str_replace(array("\r\n", "\n\r", "\n", "\r"), ' ', $formation->getProgramme()->getResume()),
                "Objectif et méthodologie" => str_replace(array("\r\n", "\n\r", "\n", "\r"), ' ', $formation->getProgramme()->getObjectives()),
                "Nombre d'heures non présentielles" => $formation->getProgramme()->getDurationNotPresentielle() ?? 0,
                "Nombre d'heures présentielles" => $formation->getProgramme()->getDurationPresentielle() ?? 0,
                "Adresse 1" => $formation->getAddress(),
                "Adresse 2" => $formation->getAddress2(),
                "Code postal" => $formation->getZipCode(),
                "Ville" => $formation->getCity(),
                "Type de session" => $sessionType,
                "Référence" => $formation->getProgramme()->getReference(),
                "N° de session" => $formation->getSessionNumber(),
                "Date d'ouverture" => $formation->getOpeningDate() ? $formation->getOpeningDate()->format("d/m/Y") : "",
                "Date de clôture" => $formation->getClosingDate() ? $formation->getClosingDate()->format("d/m/Y") : "",
                "Questionnaire rattaché" => $formation->hasLinkedForm() ? "Oui" : "Non",
                "Titre du questionnaire associé" =>$formation->hasLinkedForm() ? $formation->getForm()->getLabel() : "",
                "Thématique du questionnaire" => $thematique,
                "Clôturée" => $formation->getClosed() ? "Oui" : "Non",
                "Avec topo" => $formation->getTopoFiles()->count() ? "Oui" : "Non",
                "Nombre de participants" => $formation->getParticipantCount(),
                "CA" => str_replace(".", ",", (string)$formation->getCaTotal()),
                "CA N+1" => str_replace(".", ",", (string)$formation->getCaTotal(true)),
            ];

            $formationsToArray[] = $formationToArray;

        }
        $jsonFormationsToArray = json_encode($formationsToArray, JSON_UNESCAPED_UNICODE);

        return $jsonFormationsToArray;
    }

    public function exportSessionsFull($tmpName, \DateTime $start, \DateTime $end, $dateType) {
        $em = $this->entityManager;
        $formationRepository = $em->getRepository(Formation::class);

        $file = fopen($tmpName, 'w');
        $d = ';';
        $e = '"';

        $search = new SessionSearch();
        $search->archived = "non";
        $search->querySearchStartDate = $start;
        $search->querySearchEndDate = $end;
        $formations = $formationRepository->findSearchResults($search, 1, 100000);

        $formations = array_reverse($formations);

        $csv = array();

        uasort ($csv , array($this, 'myComparison'));

        $csvHeader = array(
            "Date de début de la réunion",
            "Heure de debut de la réunion",
            "Date de fin de la réunion",
            "Heure de fin de la réunion",
            "Titre du programme",
            "Image associé au programme",
            "Présence",
            "Coordinateur(s)",
            "Formateur(s)",
            "Publics",
            "Spécialités",
            "Résumé de la formation",
            "Objectif et méthodologie",
            "Nombre d'heures non présentielles",
            "Nombre d'heures présentielles",
            "Adresse 1",
            "Adresse 2",
            "Code postal",
            "Ville",
            "Type de session",
            "Référence",
            "N° de session",
            "Date d'ouverture",
            "Date de clôture",
            "Questionnaire rattaché",
            "Titre du questionnaire associé",
            "Thématique du questionnaire",
            "Clôturée",
            "Avec topo",
            "Nombre de participants",
            "CA",
            "CA N+1",
        );

        array_walk($csvHeader, function(&$value, $key) {
            $value = mb_convert_encoding($value, "Windows-1252", "UTF-8");
        });

        fputcsv($file, $csvHeader, $d, $e);
        fputcsv($file, array('','','','','','','','','','','','','','','','','','','','','','','','','','',''), $d, $e);

        /** @var Formation $formation */
        foreach($formations as $formation) {

            $pictureFile = "";
            if($formation->getProgramme()->getPictureFile()) {
                $pictureFile = $this->packages->getUrl($this->uploaderHelper->asset($formation->getProgramme(), 'pictureFile', Programme::class));
            }

            $coordinators = $formation ->getCoordinators();
            $coordinatorsArray = [];
            foreach ($coordinators as $coordinator) {
                array_push($coordinatorsArray, $coordinator->getPerson()->getInvertedFullname());
            }
            $sessionType = $formation->isElearning() ? $this->translator->trans('admin.formation.type.formation_elearning') : $this->translator->trans('admin.formation.type.'.$formation->getDisplayType());

            $data = array(
                $formation->getStartDate()->format("d/m/Y"),
                $formation->getStartDate()->format("H:i:s"),
                $formation->getEndDate()->format("d/m/Y"),
                $formation->getEndDate()->format("H:i:s"),
                $formation->getProgramme()->getTitle(),
                $pictureFile,
                $formation->getProgramme()->getPresence(),
                join(", ", $coordinatorsArray),
                join(", ", $formation->getFormateursNames()),
                $formation->getProgramme()->getCategories() ? join(", ", $formation->getProgramme()->getCategories()) : "",
                $formation->getProgramme()->getSpecialities() ? join(", ", $formation->getProgramme()->getSpecialities()) : "",
                str_replace(array("\r\n", "\n\r", "\n", "\r"), ' ', $formation->getProgramme()->getResume()),
                str_replace(array("\r\n", "\n\r", "\n", "\r"), ' ', $formation->getProgramme()->getObjectives()),
                $formation->getProgramme()->getDurationNotPresentielle() ?? 0,
                $formation->getProgramme()->getDurationPresentielle() ?? 0,
                $formation->getAddress(),
                $formation->getAddress2(),
                $formation->getZipCode(),
                $formation->getCity(),
                $sessionType,
                $formation->getProgramme()->getReference(),
                $formation->getSessionNumber(),
                $formation->getOpeningDate() ? $formation->getOpeningDate()->format("d/m/Y") : "",
                $formation->getClosingDate() ? $formation->getClosingDate()->format("d/m/Y") : "",
                $formation->hasLinkedForm() ? "Oui" : "Non",
                $formation->hasLinkedForm() ? $formation->getForm()->getLabel() : "",
                $formation->hasLinkedForm() ? $formation->getForm()->getCategory()->getName() : "",
                $formation->getClosed() ? "Oui" : "Non",
                $formation->getTopoFiles()->count() ? "Oui" : "Non",
                $formation->getParticipantCount(),
                str_replace(".", ",", (string)$formation->getCaTotal()),
                str_replace(".", ",", (string)$formation->getCaTotal(true)),
            );

            array_walk($data, function(&$value, $key) {
                try {
                    $value = mb_convert_encoding($value, "Windows-1252", "UTF-8");
                } catch (\Exception $e) {}
            });

            fputcsv($file, $data, $d, $e);
        }

        fclose($file);
    }

    public function exportInvoices($tmpName, \DateTime $start, \DateTime $end) {
        $em = $this->entityManager;
        $formationRepository = $em->getRepository(Formation::class);

        $file = fopen($tmpName, 'w');
        $d = ';';
        $e = '"';

        $search = new SessionSearch();
        $search->querySearchStartDate = $start;
        $search->querySearchEndDate = $end;
        $search->archived = "non";
        $formations = $formationRepository->findSearchResults($search, 1, 100000);

        $formations = array_reverse($formations);

        $csv = array();

        uasort ($csv , array($this, 'myComparison'));

        $csvHeader = array(
            "Nom de la formation",
            "Référence",
            "N° de Session",
            "Présence",
            "Numéro(s) de facture",
            "Date de réunion",
//            "identifiant sous-mode de financement",
            "Nom",
            "Prénom",
            "CA généré par le CR",
            "Marge (CA - coût)",
            "Commission taux de base",
            "Commissions comptabilisées",
            "Ecart commissions",
            "Avances formation",
            // N+1
            "CA généré par le CR N+1",
            "Marge (CA - coût) N+1",
            "Commission taux de base N+1",
            "Commissions comptabilisées N+1",
            "Ecart commissions N+1",
            "Avances formation N+1",
        );

        array_walk($csvHeader, function(&$value, $key) {
            $value = mb_convert_encoding($value, "Windows-1252", "UTF-8");
        });

        fputcsv($file, $csvHeader, $d, $e);

        /** @var Formation $formation */
        foreach($formations as $formation) {
            $isPluriannuelle = $formation->isPluriAnnuelle();
            $presence = $formation->getProgramme()->getPresence();
            $presence = $presence === Programme::PRESENCE_ELEARNING ? 'EL' : ($presence === Programme::PRESENCE_SITE ? 'Sur Site' : 'CV');
            foreach ($formation->getCoordinators() as $coordinator) {
                $data = array(
                    $formation->getProgramme()->getTitle(),
                    $formation->getProgramme()->getReference(),
                    $formation->getSessionNumber(),
                    $presence,
                    join(", ", $formation->getInvoices()->map(function(Invoice $invoice) { return $invoice->getNumber(); })->toArray()),
                    $formation->getStartDate()->format("d/m/Y H:i:s"),
//                        $financeSousMode->getIdentifiant(),
                    $coordinator->getPerson()->getLastname(),
                    $coordinator->getPerson()->getFirstname(),
                    // N
                    str_replace(".", ",", (string) round($coordinator->getCaTotal(), 2)),
                    str_replace(".", ",", (string) round($coordinator->getMarges(), 2)),
                    str_replace(".", ",", (string) round($coordinator->getCommissionTheorique(), 2)),
                    str_replace(".", ",", (string) round($coordinator->getCalculatedHonorary(), 2)),
                    str_replace(".", ",", (string) round($coordinator->getCommissionTheorique() - $coordinator->getCalculatedHonorary(), 2)),
                    str_replace(".", ",", (string) round($coordinator->getAvancesCost(), 2)),
                    // N+1
                    $isPluriannuelle ? str_replace(".", ",", (string) round($coordinator->getCaTotal(true), 2)) : '',
                    $isPluriannuelle ? str_replace(".", ",", (string) round($coordinator->getMarges(true), 2)) : '',
                    $isPluriannuelle ? str_replace(".", ",", (string) round($coordinator->getCommissionTheorique(true), 2)) : '',
                    $isPluriannuelle ? str_replace(".", ",", (string) round($coordinator->getCalculatedHonorary(true), 2)) : '',
                    $isPluriannuelle ? str_replace(".", ",", (string) round($coordinator->getCommissionTheorique(true) - $coordinator->getCalculatedHonorary(true), 2)) : '',
                    $isPluriannuelle ? str_replace(".", ",", (string) round($coordinator->getAvancesCost(true), 2)) : '',
                );

                array_walk($data, function(&$value, $key) {
                    try {
                        $value = mb_convert_encoding($value, "Windows-1252", "UTF-8");
                    } catch (\Exception $e) {}
                });

                fputcsv($file, $data, $d, $e);
            }
        }

        fclose($file);
    }

    public function exportInvoicesDetail($tmpName, \DateTime $start, \DateTime $end) {
        $em = $this->entityManager;
        $formationRepository = $em->getRepository(Formation::class);

        $file = fopen($tmpName, 'w');
        $d = ';';
        $e = '"';

        $search = new SessionSearch();
        $search->querySearchStartDate = $start;
        $search->querySearchEndDate = $end;
        $formations = $formationRepository->findSearchResults($search, 1, 100000);

        $formations = array_reverse($formations);

        $csv = array();

        uasort ($csv , array($this, 'myComparison'));

        $csvHeader = array(
            "Référence",
            "N° de Session",
            "Date de clôture",
            "Coordinateur",
            "Status du coordinateur",
            "CA généré par le CR",
            "Commissions comptabilisées",
            "Orateur",
            "Status de l'orateur",
            "Montant orateur",
            "Montant retrocession",
            "Montant indemnite kilometrique",
            "Badge",
            "Cout materiel",
            "Divers",
            "Avances formation",
        );

        array_walk($csvHeader, function(&$value, $key) {
            $value = mb_convert_encoding($value, "Windows-1252", "UTF-8");
        });

        fputcsv($file, $csvHeader, $d, $e);

        /** @var Formation $formation */
        foreach($formations as $formation) {
            $reference = $formation->getProgramme()->getReference();
            $session = $formation->getSessionNumber();
            $closingDate = $formation->getClosingDate()->format("d/m/Y H:i:s");
            foreach ($formation->getCoordinators() as $coordinator) {
                $name = $coordinator->getPerson()->getFullName();
                $status = $coordinator->getPerson()->getStatus();
                $ca = round($coordinator->getCaTotal(), 2);
                $commission = round($coordinator->getCalculatedHonorary(), 2);
                $retrocession = round($coordinator->getSurchargedCostRetrocessions(), 2);
                $indemniteKm = round($coordinator->getSurchargedCostKilometres(), 2);
                $badge = round($coordinator->getSurchargedCostBadges(), 2);
                $materiel = round($coordinator->getSurchargedCostMateriel(), 2);
                $divers = round($coordinator->getSurchargedCostDiversFormation(), 2);
                $avances = round($coordinator->getAvancesCost(), 2);

                foreach ($formation->getFormateurs() as $formateur) {
                    $data = array(
                        $reference,
                        $session,
                        $closingDate,
                        $name,
                        $status,
                        $ca,
                        $commission,
                        $formateur->getPerson()->getFullName(),
                        $formateur->getPerson()->getStatus(),
                        round($coordinator->getSurchargedCostByFormer($formateur), 2),
                        $retrocession,
                        $indemniteKm,
                        $badge,
                        $materiel,
                        $divers,
                        $avances,
                    );

                    $ca = 0;
                    $commission = 0;
                    $retrocession = 0;
                    $indemniteKm = 0;
                    $badge = 0;
                    $materiel = 0;
                    $divers = 0;
                    $avances = 0;

                    array_walk($data, function(&$value, $key) {
                        try {
                            $value = mb_convert_encoding($value, "Windows-1252", "UTF-8");
                        } catch (\Exception $e) {}
                    });

                    fputcsv($file, $data, $d, $e);
                }

            }
        }

        fclose($file);
    }


    public function exportFormerWithSiret($tmpName, \DateTime $start, \DateTime $end) {
        $em = $this->entityManager;
        $formationRepository = $em->getRepository(Formation::class);

        $file = fopen($tmpName, 'w');
        $d = ';';
        $e = '"';

        $search = new SessionSearch();
        $search->querySearchStartDate = $start;
        $search->querySearchEndDate = $end;
        $search->withFormer = "with_siret";
        $search->archived = "non";
        $formations = $formationRepository->findSearchResults($search, 1, 100000);

        $formations = array_reverse($formations);

        $csv = array();

        uasort ($csv , array($this, 'myComparison'));

        $csvHeader = array(
            "Date de réunion",
            "Titre",
            "Référence",
            "Session",
            "NOM Prénom CR",
            "NOM Prénom Formateur",
            "Honoraires",
        );

        array_walk($csvHeader, function(&$value, $key) {
            $value = mb_convert_encoding($value, "Windows-1252", "UTF-8");
        });

        fputcsv($file, $csvHeader, $d, $e);

        /** @var Formation $formation */
        foreach($formations as $formation) {

            /** @var Formateur $former */
            foreach ($formation->getFormateurs() as $former) {

                if (!$former->getPerson()->isLiberal()) {
                    continue;
                }

                $coordinators = array();
                foreach ($formation->getCoordinators() as $coordinator) {
                    $name = $coordinator->getPerson()->getInvertedFullnameTitleCase();
                    if ($coordinator->isInitiator()) {
                        $coordinators = array($name);
                        break;
                    }
                    $coordinators[] = $name;
                }

                $data = array(
                    $formation->getStartDate()->format("d/m/Y H:i:s"),
                    $formation->getProgramme()->getTitle(),
                    $formation->getProgramme()->getReference(),
                    $formation->getSessionNumber(),
                    implode(", ", $coordinators),
                    $former->getPerson()->getInvertedFullnameTitleCase(),
                    $former->getHonorary(),
                );

                array_walk($data, function(&$value, $key) {
                    try {
                        $value = mb_convert_encoding($value, "Windows-1252", "UTF-8");
                    } catch (\Exception $e) {}
                });

                fputcsv($file, $data, $d, $e);
            }
        }

        fclose($file);
    }


    public function exportSuiviLead($tmpName, Person $person, $parameters) {
        $em = $this->entityManager;

        /** @var LeadSearch $leadSearch */
        $leadSearch = $this->searchHandler->handleArray(new LeadSearch(), $parameters);
        $leadSearch->setUserFilters($person);
        $participantRepository = $em->getRepository(Participant::class);

        $file = fopen($tmpName, 'w');
        $d = ';';
        $e = '"';
        $csvHeaderFlag = false;


        /** @var Participant[] $csvPersons */
        $csvPersons = $participantRepository->findLeadSearchResultsCsv($leadSearch);
        $csvPersonLeadHistories = [];

        var_dump(count($csvPersons));
        
        $start = null;
        $end = null;
        if (isset($parameters["start"])) {
            $start = new \DateTime($parameters["start"]) ?? null;
        }
        if (isset($parameters["end"])) {
            $end = new \DateTime($parameters["end"]) ?? null;
        }
        foreach($csvPersons as $csvPerson) {
            $personLeadHistories = $csvPerson->getLeadHistoriesBetweenDates($start, $end);
            if (count($personLeadHistories) > 0) { 
                $csvPersonLeadHistories = array_merge($csvPersonLeadHistories, $personLeadHistories->toArray());
            }
        }

        if ($start || $end) {
            // Suppression des PS qui n'ont pas une date de réception du lead dans la tranche demandée
            foreach($csvPersons as $index => $csvPerson) {
                if (($start && $csvPerson->getLeadCreationDate() < $start) || ($end && $csvPerson->getLeadCreationDate() > $end)) {
                    unset($csvPersons[$index]);
                }
            }
        }
        
        $csvPersons = array_merge($csvPersons, $csvPersonLeadHistories);


        uasort($csvPersons, function($a, $b) {
            return $a->getLeadCreationDate() < $b->getLeadCreationDate();
        });

        /** @var Participant $csvPerson */
        foreach($csvPersons as $csvPerson) {
            if(!$csvHeaderFlag) {
                $csvHeader = array();
                foreach($csvPerson->__toArrayLead() as $key => $value) {
                    $csvHeader[] = $this->translator->trans('admin.participant.'.$key);
                }
                array_walk($csvHeader, function(&$value, $key) {
                    $value = mb_convert_encoding($value, "Windows-1252", "UTF-8");
                });
                fputcsv($file, $csvHeader, $d, $e);
                $csvHeaderFlag = true;
            }
            $data = $csvPerson->__toArrayLead();

            array_walk($data, function(&$value, $key) {
                $value = mb_convert_encoding($value, "Windows-1252", "UTF-8");
            });

            fputcsv($file, $data, $d, $e);
        }

        fclose($file);
    }

    public function exportLead($tmpName, \DateTime $start, \DateTime $end, $leadWithoutReferent = false) {
        $em = $this->entityManager;
        $partcipantRepository = $em->getRepository(Participant::class);

        $file = fopen($tmpName, 'w');
        $d = ';';
        $e = '"';
        $leads = $partcipantRepository->findLead($start, $end, $leadWithoutReferent);

        $csv = array();

        uasort ($csv , array($this, 'myComparison'));

        $csvHeader = array(
            "Type de lead",
            "Conseiller",
            "Civiltié",
            "Prénom",
            "Nom",
            "Date de réception du lead",
            "A recontacter le",
            "Commentaire du lead",
            "Catégorie Professionnelle",
            "Spécialité Professionnelle",
            "Mode d'exercice",
            "Adresse / code postal / ville",
            "Tel",
            "Email",
            "Coordinateur",
        );

        array_walk($csvHeader, function(&$value, $key) {
            $value = mb_convert_encoding($value, "Windows-1252", "UTF-8");
        });

        fputcsv($file, $csvHeader, $d, $e);

        foreach($leads as $lead) {

            $data = array(
                $lead->getLeadState(),
                $lead->getAdvisor(),
                $lead->getCivility(),
                $lead->getFirstname(),
                $lead->getLastname(),
                $lead->getLeadCreationDate() ? $lead->getLeadCreationDate()->format("d/m/Y") : "",
                $lead->getLeadContactDate(),
                $lead->getLeadComment(),
                $lead->getCategory(),
                $lead->getSpeciality(),
                $lead->getExerciceMode(),
                $lead->getAddress() . " " . $lead->getZipCode() . ' ' . $lead->getCity(),
                $lead->getPhone(),
                $lead->getEmail(),
                $lead->getLeadReferent() ? $lead->getLeadReferent()->getFullName() : "",
            );

            array_walk($data, function(&$value, $key) {
                try {
                    $value = mb_convert_encoding($value, "Windows-1252", "UTF-8");
                } catch (\Exception $e) {}
            });

            fputcsv($file, $data, $d, $e);
        }
        fclose($file);
    }

    public function exportFormerWithoutSiret($tmpName, \DateTime $start, \DateTime $end) {
        $em = $this->entityManager;
        $formationRepository = $em->getRepository(Formation::class);

        $file = fopen($tmpName, 'w');
        $d = ';';
        $e = '"';

        $search = new SessionSearch();
        $search->querySearchStartDate = $start;
        $search->querySearchEndDate = $end;
        $search->withFormer = "without_siret";
        $search->archived = "non";
        $formations = $formationRepository->findSearchResults($search, 1, 100000);

        $formations = array_reverse($formations);

        $csv = array();

        uasort ($csv , array($this, 'myComparison'));

        $csvHeader = array(
            "NOM Prénom Formateur",
            "Honoraires",
            "Date de réunion",
            "Nombre d'heures total",
            "NOM Prénom CR",
            "Présence",
        );

        array_walk($csvHeader, function(&$value, $key) {
            $value = mb_convert_encoding($value, "Windows-1252", "UTF-8");
        });

        fputcsv($file, $csvHeader, $d, $e);

        /** @var Formation $formation */
        foreach($formations as $formation) {

            /** @var Formateur $former */
            foreach ($formation->getFormateurs() as $former) {

                if (!$former->getPerson()->isSalarie()) {
                    continue;
                }

                $coordinators = array();
                foreach ($formation->getCoordinators() as $coordinator) {
                    $name = $coordinator->getPerson()->getInvertedFullnameTitleCase();
                    if ($coordinator->isInitiator()) {
                        $coordinators = array($name);
                        break;
                    }
                    $coordinators[] = $name;
                }

                $presence = "";
                switch ($formation->getProgramme()->getPresence()) {
                    case Programme::PRESENCE_SITE:
                        $presence = "P";
                        break;
                    case Programme::PRESENCE_ELEARNING:
                        $presence = "EL";
                        break;
                    case Programme::PRESENCE_VIRTUELLE:
                        $presence = "CV";
                        break;
                }

                $data = array(
                    $former->getPerson()->getInvertedFullnameTitleCase(),
                    $former->getHonorary(),
                    $formation->getStartDate()->format("d/m/Y H:i:s"),
                    $formation->getProgramme()->getDurationTotal(),
                    implode(", ", $coordinators),
                    $presence,
                );

                array_walk($data, function(&$value, $key) {
                    try {
                        $value = mb_convert_encoding($value, "Windows-1252", "UTF-8");
                    } catch (\Exception $e) {}
                });

                fputcsv($file, $data, $d, $e);
            }
        }

        fclose($file);
    }

    public function exportCommissionnementGpm($tmpName, \DateTime $start, \DateTime $end) {
        $em = $this->entityManager;
        $participationRepository = $em->getRepository(Participation::class);

        $file = fopen($tmpName, 'w');
        $d = ';';
        $e = '"';
        $leadParticipations = $participationRepository->findLead($start, $end);

        $csv = array();

        uasort ($csv , array($this, 'myComparison'));
        $csvHeader = array(
            "ADHERENT GPM",
            "CIVILITE",
            "NOM",
            "PRENOM",
            "DATE DE NAISSANCE",
            "N° ET VOIE",
            "CP",
            "VILLE",
            "PROFESSION",
            "CA HT ANNUEL",
            "COMMISSIONNEMENT",
            "CODE APPORTEUR",
            "DATE FORMATION",
            "DUREE FORMATION EDUPRAT",
            "FORMAT FORMATION",
        );

        array_walk($csvHeader, function(&$value, $key) {
            $value = mb_convert_encoding($value, "Windows-1252", "UTF-8");
        });

        fputcsv($file, $csvHeader, $d, $e);

        foreach($leadParticipations as $participation) {

            $data = array(
                $participation->getParticipant()->getGpmMemberNumber(),
                $participation->getParticipant()->resumeCivility(),
                $participation->getParticipant()->getLastname(),
                $participation->getParticipant()->getFirstname(),
                $participation->getParticipant()->getBirthDate() ? $participation->getParticipant()->getBirthDate()->format("d/m/Y") : "",
                $participation->getParticipant()->getAddress(),
                $participation->getParticipant()->getZipCode(),
                $participation->getParticipant()->getCity(),
                $participation->getParticipant()->getCategory(),
                $participation->getPrice() + $participation->getPriceYearN1(),
                $this->calculCommissionnement($participation),
                $participation->getParticipant()->getAdvisor() ? $participation->getParticipant()->getAdvisor()->getCodeApporteur() : "",
                $participation->getFormation()->isElearning() ? $participation->getFormation()->getOpeningDate()->format("d/m/Y") : $participation->getFormation()->getStartDate()->format("d/m/Y"),
                $participation->getFormation()->getProgramme()->getDurationTotal(),
                $participation->getFormation()->getProgramme()->getPresence(),
            );

            array_walk($data, function(&$value, $key) {
                try {
                    $value = mb_convert_encoding($value, "Windows-1252", "UTF-8");
                } catch (\Exception $e) {}
            });

            fputcsv($file, $data, $d, $e);
        }
        fclose($file);
    }
    
    public function calculCommissionnement(Participation $participation) {
        $price = $participation->getPrice() + $participation->getPriceYearN1();
        $commission = 0;
        if ($participation->getParticipant()->getCategory() == Participant::CATEGORY_MEDECIN) {
            $commission = $participation->getFormation()->getProgramme()->isElearning() ? $price * 0.10 : 30;
        } else {
            $commission = $participation->getFormation()->getProgramme()->isElearning() ? $price * 0.20 : 20;
        }

        return $commission;
    }

    public function exportLeadInscrits($tmpName, \DateTime $start, \DateTime $end) {
        $em = $this->entityManager;
        $participationRepository = $em->getRepository(Participation::class);

        $file = fopen($tmpName, 'w');
        $d = ';';
        $e = '"';
        $leadParticipations = $participationRepository->findLead($start, $end, false);

        $csv = array();

        uasort ($csv , array($this, 'myComparison'));
        $csvHeader = array(
            "ADHERENT GPM",
            "CIVILITE",
            "NOM",
            "PRENOM",
            "DATE DE NAISSANCE",
            "N° ET VOIE",
            "CP",
            "VILLE",
            "PROFESSION",
            "CA HT ANNUEL",
            "CODE APPORTEUR",
            "DATE FORMATION",
            "DUREE FORMATION EDUPRAT",
            "FORMAT FORMATION",
        );

        array_walk($csvHeader, function(&$value, $key) {
            $value = mb_convert_encoding($value, "Windows-1252", "UTF-8");
        });

        fputcsv($file, $csvHeader, $d, $e);

        foreach($leadParticipations as $participation) {
            $data = array(
                $participation->getParticipant()->getGpmMemberNumber(),
                $participation->getParticipant()->resumeCivility(),
                $participation->getParticipant()->getLastname(),
                $participation->getParticipant()->getFirstname(),
                $participation->getParticipant()->getBirthDate() ? $participation->getParticipant()->getBirthDate()->format("d/m/Y") : "",
                $participation->getParticipant()->getAddress(),
                $participation->getParticipant()->getZipCode(),
                $participation->getParticipant()->getCity(),
                $participation->getParticipant()->getCategory(),
                $participation->getPrice() + $participation->getPriceYearN1(),
                $participation->getParticipant()->getAdvisor() ? $participation->getParticipant()->getAdvisor()->getCodeApporteur() : "",
                $participation->getFormation()->isElearning() ? $participation->getFormation()->getOpeningDate()->format("d/m/Y") : $participation->getFormation()->getStartDate()->format("d/m/Y"),
                $participation->getFormation()->getProgramme()->getDurationTotal(),
                $participation->getFormation()->getProgramme()->getPresence(),
            );

            array_walk($data, function(&$value, $key) {
                try {
                    $value = mb_convert_encoding($value, "Windows-1252", "UTF-8");
                } catch (\Exception $e) {}
            });

            fputcsv($file, $data, $d, $e);
        }
        fclose($file);
    }

    public function exportCoordinators($tmpName, \DateTime $start, \DateTime $end) {
        $em = $this->entityManager;

        $repo = $em->getRepository(Person::class);

        $qb = $repo->createQueryBuilder('pe');
        $coordinators = $qb
            ->innerJoin('pe.coordinators', 'c')
            ->innerJoin('c.formation', 'f')
            ->where('f.archived = false')
            // ->andWhere('pe.isArchived = false')
            ->andWhere(
                $qb->expr()->orX(
                    $qb->expr()->between('f.querySearchStartDate', ':start', ':end'),
                    $qb->expr()->andX(
                        $qb->expr()->neq('YEAR(f.openingDate)', 'YEAR(f.closingDate)'),
                        $qb->expr()->between('f.querySearchStartDate', ':startSub1Year', ':endSub1Year')
                    )
                )
            )
            ->setParameter('start', $start->format("Y-m-d"))
            ->setParameter('end', $end->format("Y-m-d"))
            ->setParameter('startSub1Year', ($start->format('Y') - 1) . "-01-01")
            ->setParameter('endSub1Year', ($start->format('Y') - 1) . "-12-31")
            ->addOrderBy("pe.lastname", "ASC")
            ->getQuery()
            ->getResult()
        ;

        $years = range((int) $start->format("Y"), (int) $end->format("Y"));

        $file = fopen($tmpName, 'w');
        $d = ';';
        $e = '"';

        $csv = array();

        uasort ($csv , array($this, 'myComparison'));

        $csvHeader = array(
            "Année",
            "Nom",
            "Prénom",

            // N
            "CA coordinateur total",
            "Cumul des marges",
            "Commissions taux de base",
            "Commission actualisée",
            "Commission exceptionnelle",
            "Commissions comptabilisées",
            "Écart commissions",
            "Avances formation",

            // N + 1
            "CA coordinateur total N+1",
            "Cumul des marges N+1",
            "Commissions taux de base N+1",
            "Commission actualisée N+1",
            "Commission exceptionnelle N+1",
            "Commissions comptabilisées N+1",
            "Écart commissions N+1",
            "Avances formation N+1",
        );

        if (count($years) === 1) {
            array_shift($csvHeader);
        }

        array_walk($csvHeader, function(&$value, $key) {
            $value = mb_convert_encoding($value, "Windows-1252", "UTF-8");
        });

        fputcsv($file, $csvHeader, $d, $e);

        /** @var Person $person */
        foreach ($years as $year) {
            foreach ($coordinators as $person) {
                $total = $person->getCommissionExceptionnelle((string)$year, true);
                $caTotal = $person->getAllCoordinatorCaSecteurByCoordinator((string)$year);
                $totalHonorary = $person->getAllCoordinatorsCommissionsComptabilisees((string)$year);

                $totalN1 = $person->getCommissionExceptionnelle((string)($year + 1), true);
                $caTotalN1 = $person->getAllCoordinatorCaSecteurByCoordinator((string)($year + 1));
                $totalHonoraryN1 = $person->getAllCoordinatorsCommissionsComptabilisees((string)($year + 1));

                $data = array(
                    $year,
                    $person->getLastname(),
                    $person->getFirstname(),
                    str_replace(".", ",", (string) round($caTotal, 2)),
                    str_replace(".", ",", (string) round($total["marges"], 2)),
                    str_replace(".", ",", (string) round($total["commissionBase"], 2)),
                    str_replace(".", ",", (string) round($total["commissionActualisee"], 2)),
                    str_replace(".", ",", (string) round($total["commissionExceptionnelle"], 2)),
                    str_replace(".", ",", (string) round($totalHonorary, 2)),
                    str_replace(".", ",", (string) round($total["commissionBase"] - $totalHonorary, 2)),
                    str_replace(".", ",", (string) round($total["avances"], 2)),

                    str_replace(".", ",", (string) round($caTotalN1, 2)),
                    str_replace(".", ",", (string) round($totalN1["marges"], 2)),
                    str_replace(".", ",", (string) round($totalN1["commissionBase"], 2)),
                    str_replace(".", ",", (string) round($totalN1["commissionActualisee"], 2)),
                    str_replace(".", ",", (string) round($totalN1["commissionExceptionnelle"], 2)),
                    str_replace(".", ",", (string) round($totalHonoraryN1, 2)),
                    str_replace(".", ",", (string) round($totalN1["commissionBase"] - $totalHonoraryN1, 2)),
                    str_replace(".", ",", (string) round($totalN1["avances"], 2)),
                );

                if (count($years) === 1) {
                    array_shift($data);
                }

                array_walk($data, function(&$value, $key) {
                    try {
                        $value = mb_convert_encoding($value, "Windows-1252", "UTF-8");
                    } catch (\Exception $e) {}
                });

                fputcsv($file, $data, $d, $e);

            }
        }

        fclose($file);
    }

    private function myComparison($a, $b){
        return (key($a) < key($b)) ? -1 : 1;
    }

    public function exportMailHistoryParticipation(Participation $participation, Person $user) {
        $coordinator = $user->isCoordinator() ? $user : null;
        $mailHistory = $this->entityManager->getRepository(MailerHistory::class)->findByParticipation($participation, $coordinator);
        return $this->exportMailHistory($mailHistory);
    }

    public function exportMailHistoryParticipant(Participant $participant) {
        $mailHistory = $this->entityManager->getRepository(MailerHistory::class)->findByParticipant($participant);
        return $this->exportMailHistory($mailHistory);
    }

    public function exportParticipationHistories(Participant $participant) {
        $participationHistories = $this->entityManager->getRepository(ParticipationHistory::class)->findByParticipant($participant);
        return $this->exportParticipationHistoriesParticipant($participationHistories);
    }

    public function exportMailHistoryFormation(Formation $formation, Person $user) {
        $coordinator = $user->isCoordinator() ? $user : null;
        $mailHistory = $this->entityManager->getRepository(MailerHistory::class)->findByFormation($formation, $coordinator);
        return $this->exportMailHistory($mailHistory);
    }

    public function exportMailHistoryProgramme(Programme $programme) {
        $mailHistory = $this->entityManager->getRepository(MailerHistory::class)->findByProgramme($programme);
        return $this->exportMailHistory($mailHistory);
    }

    public function exportMailHistory($mailHistory = array())
    {
        $csv = array(array('Référence', 'N° de session', 'Nom', 'Prénom', 'Email', "Date d'envoi", 'Objet', 'Nature'));

        /** @var MailerHistory $mailerHistory */
        foreach($mailHistory as $mailerHistory) {
            $csv[] = array(
                $mailerHistory->getParticipation()->getFormation()->getProgramme()->getReference(),
                $mailerHistory->getParticipation()->getFormation()->getSessionNumber(),
                $mailerHistory->getParticipation()->getParticipant()->getLastname(),
                $mailerHistory->getParticipation()->getParticipant()->getFirstname(),
                $mailerHistory->getParticipation()->getParticipant()->getEmail(),
                $mailerHistory->getCreatedAt()->format("d/m/Y H:i:s"),
                $mailerHistory->getSubject(),
                $mailerHistory->getType() === MailerHistory::TYPE_MANUAL ? "Manuel" : "Automatique",
            );
        }

        return $csv;
    }

    public function exportParticipationHistoriesParticipant($participationHistories = array())
    {
        $csv = array(array('Date de l’inscription / désinscription', 'Nom / Prénom du participant', 'Référence',
         'N° de la session', 'Date de début de réunion', "Date de fin de réunion", 'Date ouverture de la session',
         'Date clôture de la session', 'CR relié', 'Format de la session', 'Type de formulaire', 'Présence',
         'Origine de l’inscription / désinscription', 'Détail de l’inscription / désinscription',
         'Motif de l’inscription / désinscription', 'Commentaires', 'Abandon'));

        /** @var ParticipationHistory $participationHistory */
        foreach($participationHistories as $participationHistory) {
            $participation = $participationHistory->getParticipation();
            $session = $participation->getFormation();
            $formation = $session->getProgramme();
            $abandon = $participationHistory->getMotif() == ParticipationHistory::MOTIF_NOSHOW || $participationHistory->getMotif() == ParticipationHistory::MOTIF_COURSE_ABANDONED ? "Oui" : "Non";
            $csv[] = array(
                $participationHistory->getCreatedAt()->format("d/m/Y H:i:s"),
                $participation->getParticipant()->getInvertedFullname(),
                $formation->getReference(),
                $session->getSessionNumber(),
                $session->getStartDate()->format("d/m/Y H:i:s"),
                $session->getEndDate()->format("d/m/Y H:i:s"),
                $session->getOpeningDate()->format("d/m/Y H:i:s"),
                $session->getClosingDate()->format("d/m/Y H:i:s"),
                $participation->getCoordinator() ? $participation->getCoordinator()->getPerson()->getInvertedFullname() : "",
                $formation->getFormatByDurations(),
                $session->getForm() ? $session->getForm()->getTypeLabel() : "",
                $formation->getPresence(),
                $participationHistory->getType(),
                $participationHistory->getAction(),
                $participationHistory->getMotif(),
                $participationHistory->getCommentaire(),
                $abandon,
            );
        }



        return $csv;
    }

    public function exportParticipantHistory($tmpName, Person $person, $parameters) {
        $em = $this->entityManager;

        $parameters['user'] = $person;
         /** @var ParticipantSearch $participantSearch */
         $comptabiliteSearch = $this->searchHandler->handleArray(new ComptabiliteSearch(), $parameters);

        $file = fopen($tmpName, 'w');
        $d = ';';
        $e = '"';
        /** @var Person[] $csvPersons */
        $report = $this->comptabiliteService->findAllInscriptions($comptabiliteSearch);

        var_dump(count($report));

        $csvHeader = array('Date de l’inscription', 'Nom / Prénom du participant', 'Référence',
        'N° de la session', 'Date de début de réunion', "Date de fin de réunion", 'Date ouverture de la session',
        'Date clôture de la session', 'CR relié', 'Coût', 'Format de la session', 'Type de formulaire', 'Présence',
        'Inscription / Désinscription', 'Origine',
        'Détail', 'Motif de l’inscription / désinscription', 'Commentaires', 'Abandon');

        array_walk($csvHeader, function(&$value, $key) {
            $value = mb_convert_encoding($value, "Windows-1252", "UTF-8");
        });

        fputcsv($file, $csvHeader, $d, $e);


        /** @var ParticipationHistory $participationHistory */
        foreach($report as $participationHistory) {
            $participation = $participationHistory->getParticipation();
            $session = $participation->getFormation();
            $formation = $session->getProgramme();
            $abandon = $participationHistory->getMotif() == ParticipationHistory::MOTIF_NOSHOW || $participationHistory->getMotif() == ParticipationHistory::MOTIF_COURSE_ABANDONED ? "Oui" : "Non";
            $formType = "";

            if ($session->getForm()) {
                if ($session->isTcs()) {
                    $formType = "TCS";
                } elseif($session->getForm()->isVignetteType()) {
                    $formType = Audit::TYPE_LABEL_VIGNETTE;
                } else {
                    $session->getForm()->getTypeLabel();
                }
            }

            $motif = $participationHistory->getMotif();
            if ($motif == "Annulation session") {
                $motif = $session->getFormationHistories()[0]->getMotif();
            }

            $detail = "";
            if ($participationHistory->getMotif() == "Annulation session") {
                $detail = $participationHistory->getMotif();
            } elseif($participationHistory->getAction() == ParticipationHistory::ACTION_UNREGISTER) {
                $detail = "Désinscription PS";
            }

            $data = array(
                $participationHistory->getCreatedAt()->format("d/m/Y H:i:s"),
                $participation->getParticipant()->getInvertedFullname(),
                $formation->getReference(),
                $session->getSessionNumber(),
                $session->getStartDate()->format("d/m/Y H:i:s"),
                $session->getEndDate()->format("d/m/Y H:i:s"),
                $session->getOpeningDate()->format("d/m/Y H:i:s"),
                $session->getClosingDate()->format("d/m/Y H:i:s"),
                $participation->getCoordinator() ? $participation->getCoordinator()->getPerson()->getInvertedFullname() : "",
                $participationHistory->getPrice(),
                $formation->getFormatByDurations(),
                $formType,
                $formation->getPresence(),
                $participationHistory->getAction(),
                $participationHistory->getType(),
                $detail,
                $motif,
                $participationHistory->getCommentaire(),
                $abandon,
            );

            array_walk($data, function(&$value, $key) {
                try {
                    $value = mb_convert_encoding($value, "Windows-1252", "UTF-8");
                } catch (\Exception $e) {}
            });

            fputcsv($file, $data, $d, $e);
        }

        fclose($file);
    }



    public function exportParticipationLogs(Formation $formation)
    {
        $csv = array(array('ID Participant', 'ID Participation', 'NOM prénom', 'RPPS/ADELI', 'Action', 'Date et heure de début', 'Date et heure de fin', "Temps", "État d'avancement du module"));

        $logs = $this->entityManager->getRepository(ParticipationLog::class)->findBySession($formation);

        /** @var ParticipationLog $log */
        foreach($logs as $log) {
            $isDownload = $log->isDownloadAction();
            if($log->getAction() != ParticipationLog::ACTION_DOWNLOAD_ATTESTATION_HONNEUR && $log->getAction() != ParticipationLog::ACTION_UPLOAD_ATTESTATION_HONNEUR) {
                $time = $log->getEndDate() ? ($log->getEndDate()->getTimestamp() - $log->getStartDate()->getTimestamp()) : 0;
                $labelKey = "admin.participation.logs." . $log->getAction();
                $label = $this->translator->trans($labelKey);
                if ($label === $labelKey) {
                    $label = $this->translator->trans("front.course.steps." . $log->getAction());
                    if($log->getAction() == "elearning") {
                        $lesson = $log->getParticipation()->getFormation()->getElearning()->getLessonById($log->getLesson());
                        $activity = $lesson ? $lesson->getActivityById($log->getActivity()) : false;
                        $label = $lesson ? $lesson->getLabel() : $label;
                        $label = $activity ? $label . " - activité " . $activity->getPosition() : $label;
                    }
                }
                $csv[] = array(
                    $log->getParticipation()->getParticipant()->getId(),
                    $log->getParticipation()->getId(),
                    $log->getParticipation()->getParticipant()->getInvertedFullname(),
                    $log->getParticipation()->getParticipant()->getIdentifier(),
                    $label,
                    $log->getStartDate()->format("d/m/Y H:i:s"),
                    $isDownload ? $log->getStartDate()->format("d/m/Y H:i:s") : ($log->getEndDate() ? $log->getEndDate()->format("d/m/Y H:i:s") : ""),
                    $isDownload ? "00:00:00" : ($log->getEndDate() ? sprintf('%02d:%02d:%02d', ($time / 3600), ($time / 60 % 60), $time % 60) : ""),
                    ($isDownload ? "100" : ($log->getProgression() ? $log->getProgression()  : 0)) . "%",
                );
            }
        }

        return $csv;
    }

    public function exportParticipationLogsByUnity(Formation $formation, $unity, $withIp = false)
    {
        $csv = array();
        if ($withIp) {
            foreach ($formation->getCoordinators() as $coordinator) {
                $person = $coordinator->getPerson();
                if ($person->getIpList()) {
                    foreach($person->getIpList() as $index => $ip) {
                        $csv[] = array("Ip " . $person->getFullname() . " " . ($index + 1) . " : " . $ip);
                    }
                }
            }
            $csv[] = array("");
        }

        $unityValueTxt = $unity;
        if ($formation->isFourUnity()) {
            if ($unity === "3") {
                $unityValueTxt = "4";
            } else if ($unity === "2") {
                $unityValueTxt = "2 et 3";
            }
        }

        if ($formation->getProgramme()->isElearningOneUnity()) {
            $csv[] = array("Extraction des logs", "", "", "", "", "", "", "", "", "");
        } else {
            $csv[] = array("Extraction des logs - Unite ". $unityValueTxt , "", "", "", "", "", "", "", "", "");
        }
        $displayModuleProgression = $this->moduleProgressionUpdateDate <= $formation->getOpeningDate();
        $header = array('Nom', 'Prénom', 'Email du participant', 'Id de la session', 'Nom du module', 'Connexion - Date et heure', 'Déconnexion - Date et heure', "Temps passé en minutes", "Progression (%)");
        if ($displayModuleProgression) {
            array_push($header, "Avancement dans le module (%)");
        }
        if ($withIp) {
            array_push($header, "Ip");
        }
        $csv[] = array("", "", "", "", "", "", "", "", "", "");
        $csv[] = $header;
        $logs = $this->entityManager->getRepository(ParticipationLog::class)->findBySession($formation);
        if (!count($logs)) {
            return $csv;
        }
        $currentTime = 0;
        $course = count($logs) ? $this->courseManager->getCourse($logs[0]->getParticipation()) : null;
        $minTime = $formation->getProgramme()->getUnityByPosition($unity)->getNbHoursConnected() * 60;
        
        // if ($formation->getProgramme()->isElearningOneUnity()) {
        //     $minTime = $this->getMinTimeByStep($course, 1, $logs[0]->getParticipation()->getFormation()) + $this->getMinTimeByStep($course, 2, $logs[0]->getParticipation()->getFormation()) + $this->getMinTimeByStep($course, 3, $logs[0]->getParticipation()->getFormation());
        // } elseif ($formation->getProgramme()->isElearningTwoUnity()) {
        //     $minTime = $unity == 1 ? $this->getMinTimeByStep($course, 1, $logs[0]->getParticipation()->getFormation()) + $this->getMinTimeByStep($course, 2, $logs[0]->getParticipation()->getFormation()) : $this->getMinTimeByStep($course, 3, $logs[0]->getParticipation()->getFormation());
        // } else {
        //     $minTime = $this->getMinTimeByStep($course, $unity, $logs[0]->getParticipation()->getFormation());
        // }

        $step1Modules = [];
        if ($course && $course["step_1"]) {
            foreach ($course["step_1"] as $module) {
                $step1Modules[] = $module["id"];
            }
        }

        $currentParticipant = $logs[0]->getParticipation()->getId();
        $minTimeSec = $minTime * 60;
        $updateProgression = [];
        $takeAccountModulesInProgressionList = [];
        $moduleProgression = 0;
        
        if ($unity === "1") {
            $stepsToCheck = ["step_1"];
        } elseif ($unity === "3") {
            $stepsToCheck = ["step_3", "step_4"];
        }
        
        foreach ($stepsToCheck as $step) {
            foreach ($course[$step] as $module) {
                $excludedLabel = $unity === "1" 
                    ? CourseManager::STEP1_FORM_PRE_LABEL 
                    : CourseManager::STEP3_FORM_POST_LABEL;
        
                if ($module["id"] !== $excludedLabel && isset($module["valueProgression"])) {
                    $takeAccountModulesInProgressionList[] = $module["id"];
                }
            }
        }

        /** @var ParticipationLog $log */
        foreach($logs as $log) {
            if ($log->getParticipation()->getId() != $currentParticipant) {
                $updateProgression = [];
                $csv[] = array("", "", "", "", $minTime, "", "", round($currentTime / 60), "", "");
                $csv[] = array("", "", "", "", "", "", "", "","", "");
                $currentTime = 0;
                $currentParticipant = $log->getParticipation()->getId();
                $alreadyTakedAccount = [];
                $moduleProgression = 0;
            }
            $checkUnity2 = $unity == 2 ? in_array($log->getAction(), [ParticipationLog::ACTION_CLASSE_VIRTUELLE, ParticipationLog::ACTION_ELEARNING]) || $formation->getProgramme()->isElearningTwoUnity() : true;
            if ($formation->getProgramme()->isElearningOneUnity()) {
                $takeAccount = true;
            } elseif ($formation->getProgramme()->isElearningTwoUnity()) {
                $takeAccount = $log->getStep() ===  "step_1" || $log->getAction() == "elearning";
                // On inverse / prends le reste pour $unity == 3  => !$takeAccount
                $takeAccount = $unity == 1 ? $takeAccount : !$takeAccount;
            } else {
                $takeAccount = $unity == 3 ? $log->getStep() === "step_3" || $log->getStep() === "step_4" : $log->getStep() === "step_".$unity;
            }
            if ($takeAccount && $log->getAction() != ParticipationLog::ACTION_DOWNLOAD_ATTESTATION_HONNEUR && $log->getAction() != ParticipationLog::ACTION_UPLOAD_ATTESTATION_HONNEUR && $log->getAction() && $checkUnity2) {
                $isDownload = $log->isDownloadAction();
                $time = $log->getEndDate() ? ($log->getEndDate()->getTimestamp() - $log->getStartDate()->getTimestamp()) : 0;
                $time = 60 * ceil($time/60);
                $currentTime += $time;
                $progression = $minTimeSec > 0 ? round(($currentTime/$minTimeSec) * 100) : 100;
                $progression = $progression > 100 ? 100 : $progression;
                $label = $this->getLabel($log, $unity, $withIp, $formation);

                if ($displayModuleProgression) {
                    if (
                        ($unity == 1 && ($formation->getProgramme()->isElearningOneUnity() || $formation->getProgramme()->isElearningTwoUnity())) ||
                        ($unity == 2 && $formation->getProgramme()->isElearningThreeUnity())
                        )
                        {
                            $moduleProgression = $log->getProgression() ? $log->getProgression() : 0;
                    } else {
                        if (!isset($alreadyTakedAccount[$log->getAction()]) && ($log->getProgression() === 100 || ($log->getProgression() === 50 && $log->getAction() === CourseManager::STEP1_ETUTORAT_LABEL))) {
                            if ($log->getAction() === CourseManager::STEP1_FORM_PRE_LABEL ||
                                $log->getAction() === CourseManager::STEP3_FORM_POST_LABEL ||
                                in_array($log->getAction(), $takeAccountModulesInProgressionList)) {
                                    $moduleProgression += $log->getAction() === CourseManager::STEP1_FORM_PRE_LABEL || $log->getAction() === CourseManager::STEP3_FORM_POST_LABEL ? 85 : 15 / count($takeAccountModulesInProgressionList);
                                    $alreadyTakedAccount[$log->getAction()] = true;
                            }
                        }
                    }
                }

                $ip = null;
                if ($withIp) {
                    $ip = $log->getIp();
                }

                $csv[] = array(
                    $log->getParticipation()->getParticipant()->getLastname(),
                    $log->getParticipation()->getParticipant()->getFirstname(),
                    $log->getParticipation()->getParticipant()->getEmail(),
                    $log->getParticipation()->getFormation()->getId(),
                    $label,
                    $log->getStartDate()->format("d/m/Y H:i:s"),
                    $isDownload ? $log->getStartDate()->format("d/m/Y H:i:s") : ($log->getEndDate() ? $log->getEndDate()->format("d/m/Y H:i:s") : ""),
                    round($time/60),
                    $progression . "%",
                    floor($moduleProgression) . "%",
                    $ip
                );
            }
            if ($log == end($logs)) {
                $csv[] = array("", "", "", "", $minTime, "", "", round($currentTime / 60), "", "");
            }
        }
        return $csv;
    }

    public function getLabel(ParticipationLog $log, $unity, $withIp, Formation $formation) {
        $uniformisationFalse = ($unity == 1 && ($formation->getProgramme()->isElearningOneUnity() || $formation->getProgramme()->isElearningTwoUnity())) || ($unity == 2 && $formation->getProgramme()->isElearningThreeUnity());
        if (($unity === "1" || $unity === "3") && !$withIp && !$uniformisationFalse) {
            if ($unity == 1) {
                $nbHoursConnected = $formation->getProgramme()->getUnityByPosition($unity)->getNbHoursConnected();
                $minDuration = $nbHoursConnected > 0 ? $nbHoursConnected * 60 : 0;
                $moduleName = $formation->isTcs() ? "TCS_" : $this->translator->trans("admin.formation.type." . $log->getParticipation()->getFormation()->getFormTypePre()) . "_";

            } else {
                $nbHoursConnected = $formation->getProgramme()->getUnityByPosition($unity)->getNbHoursConnected();
                $minDuration = $nbHoursConnected > 0 ? $nbHoursConnected * 60 : 0;
                $moduleName = $this->translator->trans("admin.formation.type." . $log->getParticipation()->getFormation()->getFormTypePost()) . "_";
            }
            return $moduleName . $minDuration . "min";
        }
        $moduleTime = $log->getParticipation()->getFormation()->getModuleTimesToArray();
        if ($log->getAction() == ParticipationLog::ACTION_FORM_PRE) {
            $labelKey = "admin.formation.type." . $log->getParticipation()->getFormation()->getFormTypePre();
        } elseif ($log->getAction() == ParticipationLog::ACTION_FORM_POST) {
            $labelKey = "admin.formation.type." . $log->getParticipation()->getFormation()->getFormTypePost();
        } else {
            $labelKey = "admin.participation.logs." . $log->getAction();
        }
        $label = $this->translator->trans($labelKey);
        if ($label === $labelKey) {
            $label = $this->translator->trans("front.course.steps." . $log->getAction());
            if($log->getAction() == "elearning") {
                $lesson = $log->getParticipation()->getFormation()->getElearning()->getLessonById($log->getLesson());
                $label = $lesson ? $lesson->getLabel() : $label;
                $minDuration = $lesson->getDeclaredDuration() ? $lesson->getDeclaredDuration() : 0;
                $label = $label . "_" . $minDuration . " min";
                return $label;
            }
        }
        return $moduleTime[$log->getAction()] ? $label . "_" . $moduleTime[$log->getAction()] . " min" : $label;
    }

    public function getMinTimeByStep($course, $unity, Formation $formation) {
        $minTime = 0;
        $moduleTimes = $formation->getModuleTimesToArray();
        if ($unity == 1) {
            foreach($course["step_1"] as $module) {
                $minTime += $moduleTimes[$module["id"]];
            }
        } elseif ($unity == 2) {
            foreach($course["step_2"] as $module) {
                if ($module["id"] == "reunion" && $formation->isElearning() && $formation->getElearning()) {
                    foreach ($formation->getElearning()->getLessons() as $lesson) {
                        $minTime += $lesson->getDeclaredDuration();
                    }
                } else {
                    if ($module["id"] == "reunion" && $formation->getProgramme()->isClasseVirtuelle()) {
                        $minTime += self::MIN_TIME_BY_MODULE['classe_virtuelle'];
                    } else {
                        $minTime += $moduleTimes[$module["id"]];
                    }
                }
            }
        } elseif ($unity == 3) {
            foreach($course["step_3"] as $module) {
                $minTime += $moduleTimes[$module["id"]];
            }
            foreach($course["step_4"] as $module) {
                $minTime += $moduleTimes[$module["id"]];
            }
        }
        return $minTime;
    }

    public function exportFinanceSousModes($tmpName, \DateTime $start, \DateTime $end) {
        $file = fopen($tmpName, 'w');
        $d = ';';
        $e = '"';

        $csv = array();

        uasort ($csv , array($this, 'myComparison'));

        $csvHeader = array(
            "Mode de financement",
            "Sous-mode de financement",
            "Prise en charge",
            "Adresse 1",
            "Adresse 2",
            "Code postal",
            "Ville",
            "Représentant Adresse mail",
            "Représentant Adresse 1",
            "Représentant Adresse 2",
            "Représentant Code postal",
            "Représentant Ville",
            "Référence",
            "Session",
            "Titre",
            "Date de réunion",
            "Date de clôture",
            "nb participant",
            "Participants",
            "CR",
            "Formateurs",
            "CA",
            "CA N+1",
            "N° facture",
            "N° facture N+1",
            "Facturé",
            "Facturé N+1",
        );

        array_walk($csvHeader, function(&$value, $key) {
            $value = mb_convert_encoding($value, "Windows-1252", "UTF-8");
        });

        fputcsv($file, $csvHeader, $d, $e);

        $financeSousModes = $this->entityManager->getRepository(FinanceSousMode::class)->findByPeriod($start, $end);
        $formationRepository = $this->entityManager->getRepository(Formation::class);
        foreach($financeSousModes as $financeSousMode) {
            $formations = $formationRepository->findByPeriodAndFinanceSousMode($start, $end, $financeSousMode);
            foreach($formations as $formation) {
                $reunionDate = $formation->getStartDate()->format("Y-m-d") == $formation->getEndDate()->format("Y-m-d") ? $formation->getStartDate()->format("d-m-Y") : "Du " . $formation->getStartDate()->format("d-m-Y") . " au " . $formation->getEndDate()->format("d-m-Y");
                $nbParticipations = 0;
                $totalCa = 0;
                $totalCaN1 = 0;
                $coordinators = array();
                $formators = array();
                $participants = array();
                foreach($formation->getParticipations() as $participation) {
                    if ($participation->getFinanceSousMode() == $financeSousMode) {
                        $nbParticipations += 1;
                        $totalCa += $participation->getPrice();
                        $totalCaN1 += $participation->getPriceYearN1();
                        $coordinator = $participation->getCoordinator() ? $participation->getCoordinator()->getPerson()->getInvertedFullName() : false;
                        if ($coordinator && !in_array($coordinator, $coordinators)) {
                            array_push($coordinators, $coordinator);
                        }
                        array_push($participants, ucfirst(strtolower($participation->getParticipant()->getLastName())));
                    }
                }
                foreach($formation->getFormateurs() as $formateur) {
                    array_push($formators, $formateur->getPerson()->getInvertedFullName());
                }
                $invoice = $formation->getInvoicePerFinanceSousMode($financeSousMode, $formation->getOpeningDate()->format("Y"));
                $invoiceNumber = $invoice ? $invoice->getNumber() : "";
                $factured = $formation->isFacturedFinanceSousMode($financeSousMode->getId()) ? "Oui" : "Non";
                $facturedN1 = "Non concerné";

                $invoiceN1Number = "non concerné";
                if($formation->isPluriAnnuelle()) {
                    $invoiceN1 = $formation->getInvoicePerFinanceSousMode($financeSousMode, ($formation->getOpeningDate()->format("Y") + 1));
                    $invoiceN1Number = $invoiceN1 ? $invoiceN1->getNumber() : "";
                    $facturedN1 = $formation->isFacturedFinanceSousMode($financeSousMode->getId(), true) ? "Oui" : "Non";
                }

                sort($participants);
                sort($coordinators);
                sort($formators);

                $data = array(
                    $financeSousMode->getFinanceMode()->getName(),
                    $financeSousMode->getName() . " - " . $financeSousMode->getId(),
                    $financeSousMode->getPriseEnCharge(),
                    $financeSousMode->getAddress(),
                    $financeSousMode->getAddress2(),
                    $financeSousMode->getZipCode(),
                    $financeSousMode->getCity(),
                    $financeSousMode->getRpsMail(),
                    $financeSousMode->getRpsAddress(),
                    $financeSousMode->getRpsAddress2(),
                    $financeSousMode->getRpsZip(),
                    $financeSousMode->getRpsCity(),
                    $formation->getProgramme()->getReference(),
                    $formation->getSessionNumber(),
                    $formation->getProgramme()->getTitle(),
                    $reunionDate,
                    $formation->getClosingDate()->format("d-m-Y"),
                    $nbParticipations,
                    implode(", ", $participants),
                    implode(", ", $coordinators),
                    implode(", ", $formators),
                    $totalCa,
                    $totalCaN1,
                    $invoiceNumber,
                    $invoiceN1Number,
                    $factured,
                    $facturedN1,
                );

                array_walk($data, function(&$value, $key) {
                    try {
                        $value = mb_convert_encoding($value, "Windows-1252", "UTF-8");
                    } catch (\Exception $e) {}
                });

                fputcsv($file, $data, $d, $e);
            }
        }
        fclose($file);
    }

    public function exportLogoPartenaires($tmpName, \DateTime $start, \DateTime $end) {
        $file = fopen($tmpName, 'w');
        $d = ';';
        $e = '"';

        $csv = array();

        uasort ($csv , array($this, 'myComparison'));

        $csvHeader = array(
            "Date de génération",
            "CR",
            "Nom du partenaire",
            "Titre du fichier",
            "Référence session",
            "Date session"
        );

        array_walk($csvHeader, function(&$value, $key) {
            $value = mb_convert_encoding($value, "Windows-1252", "UTF-8");
        });

        fputcsv($file, $csvHeader, $d, $e);

        // $logoPartenaireUsages = $this->entityManager->getRepository(LogoPartenaireUsage::class)->findAll();
        $logoPartenaireUsages = $this->entityManager->getRepository(LogoPartenaireUsage::class)->findByPeriod($start, $end);

        foreach($logoPartenaireUsages as $logoPartenaireUsage) {
            $data = array(
                $logoPartenaireUsage->getCreatedAt()->format("d-m-Y H:i:s"),
                $logoPartenaireUsage->getCoordinator()->getFullName(),
                $logoPartenaireUsage->getLogoPartenaire()->getLogoName(),
                $logoPartenaireUsage->getTitre(),
                $logoPartenaireUsage->getFormation()->getProgramme()->getReference() . ' ' . $logoPartenaireUsage->getFormation()->getSessionNumber(),
                $logoPartenaireUsage->getFormation()->getStartDate()->format("d-m-Y"),
            );

            array_walk($data, function(&$value, $key) {
                try {
                    $value = mb_convert_encoding($value, "Windows-1252", "UTF-8");
                } catch (\Exception $e) {}
            });

            fputcsv($file, $data, $d, $e);
        }
        fclose($file);
    }
}
