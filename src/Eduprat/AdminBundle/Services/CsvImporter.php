<?php

namespace Eduprat\AdminBundle\Services;

use Doctrine\ORM\EntityManager;
use Doctrine\ORM\EntityManagerInterface;
use Doctrine\ORM\NonUniqueResultException;
use Eduprat\AdminBundle\Entity\Person;
use Eduprat\AuditBundle\Services\CourseManager;
use Eduprat\DomainBundle\Entity\Coordinator;
use Eduprat\DomainBundle\Entity\FinanceSousMode;
use Eduprat\DomainBundle\Entity\Formation;
use Eduprat\DomainBundle\Entity\Indemnisation;
use Eduprat\DomainBundle\Entity\Participant;
use Eduprat\DomainBundle\Entity\Participation;
use Eduprat\DomainBundle\Events\ModuleDoneAndNextAssignIfExists;
use Eduprat\DomainBundle\Services\AdressesService;
use Eduprat\DomainBundle\Services\EmailSender;
use Symfony\Component\EventDispatcher\EventDispatcherInterface;
use Symfony\Component\String\Slugger\AsciiSlugger;

use Eduprat\DomainBundle\Entity\Compensation;
use Eduprat\DomainBundle\Entity\ParticipationHistory;
use Symfony\Component\Validator\Validator\ValidatorInterface;

class CsvImporter
{
    const FIELD_RPPS = 'N° RPPS';
    const FIELD_ADELI = 'N° ADELI';
    const FIELD_PART = 'Part Orga';
    const FIELD_CIVILITY = 'Civilité';
    const FIELD_LASTNAME = 'Nom';
    const FIELD_FIRSTNAME = 'Prénom';
    const FIELD_BIRTHDAY = 'Date de naissance';
    const FIELD_ADDRESS = 'Adresse Pro';
    const FIELD_ZIP = 'Code Postal Pro';
    const FIELD_CITY = 'Ville Pro';
    const FIELD_PHONE = 'N° téléphone';
    const FIELD_BIRTHNAME = 'Nom de naissance';
    const FIELD_EMAIL = 'E-mail';
    const FIELD_CATEGORY = 'Catégorie Professionnelle';
    const FIELD_SPECIALITY = 'Spécialité';
    const FIELD_EXERCISEMODE = 'Mode d\'exercice';
    const FIELD_FINANCESOUSMODE_IDENTIFIANT = 'Identifiant sous mode de financement';
    const FIELD_FINANCESOUSMODE_NAME = 'Nom Sous mode de financement';
    const FIELD_FINANCESOUSMODE_ID = 'ID Sous mode de financement';

    const DEFAULT_PART = "1200";
    const DEFAULT_EXERCICE_MODE = "Libéral";

    /**
     * @var EntityManager
     */
    protected $em;

    /**
     * @var UserManager
     */
    protected $userManager;

    /**
     * @var EmailSender
     */
    protected $emailSender;

    /**
     * @var ValidatorInterface
     */
    protected $validator;

    /**
     * @var PriceDispatcher
     */
    protected $priceDispatcher;

    protected AdressesService $adressesService;
    private CourseManager $courseManager;
    private EventDispatcherInterface $eventDispatcher;

    /**
     * ATTENTION NE PAS AJOUTER DE PARAMETRE DANS LE CONSTRUCT SANS MODIFIER LE CONSTRUCT DE CSVIMPORTERZOOM
     */
    public function __construct(EntityManagerInterface $em, UserManager $userManager, EmailSender $emailSender, ValidatorInterface $validator, AdressesService $adressesService, CourseManager $courseManager, PriceDispatcher $priceDispatcher, EventDispatcherInterface $eventDispatcher)
    {
    /**
     * ATTENTION NE PAS AJOUTER DE PARAMETRE DANS LE CONSTRUCT SANS MODIFIER LE CONSTRUCT DE CSVIMPORTERZOOM
     */
        $this->em = $em;
        $this->userManager = $userManager;
        $this->emailSender = $emailSender;
        $this->validator = $validator;
        $this->adressesService = $adressesService;
        $this->courseManager = $courseManager;
        $this->priceDispatcher = $priceDispatcher;
        $this->eventDispatcher = $eventDispatcher;
    }

    public function import($datas, Formation $formation, $geocode = false)
    {

        if ($formation->isSedd() || $formation->isCongres()) {
            return $this->csvToDatabaseNoAccountCreation($datas, $formation, $geocode);
        } else {
            return $this->csvToDatabase($datas, $formation, true, $geocode);
        }
    }

    public function getDiff($datas, Formation $formation, $geocode = false)
    {
        $participantRepository = $this->em->getRepository(Participant::class);
        $participationRepository = $this->em->getRepository(Participation::class);
        $currentParticipationsIds = $formation->getParticipations()->map(function(Participation $p) { return $p->getId(); })->toArray();
        $existingIds = [];
        $created = [];
        $deleted = [];
        $updated = [];
        $errors = [];

        foreach($datas as $data) {

            $data = $this->sanitizeRow($data);
            $participantErrors = array();

            if ($data === false) {
                continue;
            }

            $noIdentifierAccount = is_null($data[self::FIELD_RPPS]) && is_null($data[self::FIELD_ADELI]);
            try {
                if ($noIdentifierAccount) {
                    $participant = $participantRepository->findOneByNameAndEmail($data[self::FIELD_FIRSTNAME], $data[self::FIELD_LASTNAME], $data[self::FIELD_EMAIL]);
                } else {
                    $participant = $participantRepository->findOneByRppsAndAdeli($data[self::FIELD_RPPS], $data[self::FIELD_ADELI]);
                }
            } catch (NonUniqueResultException $e) {
                $participantErrors[] =  array(
                    'error' => "admin.formation.import.error",
                    'participant' => $data
                );
            }

            if (isset($data[self::FIELD_FINANCESOUSMODE_ID])) {
                $financeSousMode = $this->em->getReference(FinanceSousMode::class, $data[self::FIELD_FINANCESOUSMODE_ID]);
                if ($formation->getFinanceSousModes()->contains($financeSousMode) === false) {
                    $participantErrors[] = array(
                        'error' => "admin.formation.import.sousModeError",
                        'participant' => $data
                    );
                }
            }


            if(!$participant) {
                $participant = new Participant();
            }
            $participant = $this->hydrateParticipant($participant, $data, $geocode);
            $validationErrors = $this->validator->validate($participant);
            if ($validationErrors->count() > 0) {
                foreach ($validationErrors as $error) {
                    $participantErrors[] = array(
                        'error' => "admin.formation.import.error_validation",
                        'message' => $validationErrors->get(0)->getMessage(),
                        'participant' => $data
                    );
                }
            }

            $errors = array_merge($errors, $participantErrors);

            if (count($participantErrors) > 0) {
                continue;
            }

            /** @var Participation|null $participation */
            $participation = $participationRepository->findOneBy(array('formation' => $formation->getId(), 'participant' => $participant->getId()));

            // Si la participation n'existe pas, on la créée
            if(!$participation) {
                $participation = $this->createParticipation($participant, $formation, $data);
                $created[] = $participation;
            } else if ($participation && !$participation->isActive()) {
                $this->hydrateParticipation($participation, $data);
                $created[] = $participation;
            } else {
                $this->hydrateParticipation($participation, $data);
                $existingIds[] = $participation->getId();
                $updated[] = $participation;
            }
        }

        $toRemoveIds = array_diff($currentParticipationsIds, $existingIds);

        foreach ($toRemoveIds as $toRemoveId) {
            $p = $this->em->getReference(Participation::class, $toRemoveId);
            $deleted[] = $p;
        }

        return array(
            "created" => $created,
            "deleted" => $deleted,
            "updated" => $updated,
            "errors"  => $errors,
        );
    }

    /**
     * record datas to database
     *
     * @param                                        $datas
     * @param Formation $formation
     * @return array
     */
    public function csvToDatabase($datas, Formation $formation, $removeMissing = true, $geocode = false, $type = ParticipationHistory::MANUAL_TYPE)
    {
        $emailsToSend =  [];
        $personRepository = $this->em->getRepository(Person::class);
        $participantRepository = $this->em->getRepository(Participant::class);
        $participationRepository = $this->em->getRepository(Participation::class);
        $currentParticipationsIds = $formation->getParticipations()->map(function(Participation $p) { return $p->getId(); })->toArray();
        $created = [];
        $existingIds = [];
        $noIdentifierParticipants = [];

        if ($datas) {
            foreach($datas as $data) {

                $data = $this->sanitizeRow($data);

                if ($data === false) {
                    continue;
                }

                $noIdentifierAccount = is_null($data[self::FIELD_RPPS]) && is_null($data[self::FIELD_ADELI]);
                try {
                    if ($noIdentifierAccount) {
                        $participant = $participantRepository->findOneByNameAndEmail($data[self::FIELD_FIRSTNAME], $data[self::FIELD_LASTNAME], $data[self::FIELD_EMAIL]);
                    } else {
                        $participant = $participantRepository->findOneByRppsAndAdeli($data[self::FIELD_RPPS], $data[self::FIELD_ADELI]);
                    }
                } catch (NonUniqueResultException $e) {
                    return array(
                        'error' => $e,
                        'participant' => $data
                    );
                }

                $sendNewEmail = "";

                if(!$participant) {
                    $participant = new Participant();
                }
                $participant = $this->hydrateParticipant($participant, $data, $geocode);
                $validationErrors = $this->validator->validate($participant);
                if ($validationErrors->count() > 0) {
                    return array(
                        'error' => $validationErrors->get(0)->getMessage(),
                        'participant' => $data
                    );
                }

                if ($participant->canCreateAccount()) {
                    $user = $this->userManager->createFromParticipant($participant);
                    // On vérifie si l'user existe dans le cas où il aurait était créé via l'interface
                    /** @var Person $existingUser */
                    if ($existingUser = $personRepository->findOneByEmail($participant->getEmail())) {
                        if (!$existingUser->getParticipant()) {
                            $user = $existingUser;
                            $user->setParticipant($participant);
                        }
                    }
                    $this->em->persist($user);
                    $participant->setUser($user);
                    $sendNewEmail = EmailSender::MAIL_ACCOUNT;
                }

                $user = $participant->getUser();

                if(!$participant->isLead()) {
                    $participant->setPartenariat($formation->getPartenariat());
                    $participant->setOts($formation->getOts());
                    $this->em->persist($participant);
                }
                $this->em->persist($participant);

                /** @var Participation|null $participation */
                $participation = $participationRepository->findOneBy(array('formation' => $formation->getId(), 'participant' => $participant->getId()));

                // Si la participation n'existe pas, on la créée
                if(!$participation) {
                    $participation = $this->createParticipation($participant, $formation, $data);
                    $this->em->persist($participation);
                    $sendNewEmail = $user && !$user->isHasCreatedPassword() ? EmailSender::MAIL_ACCOUNT : EmailSender::MAIL_PARTICIPATION;
                    $created[] = $participation;
                } else if ($participation && !$participation->isActive()) {
                    $this->hydrateParticipation($participation, $data);
                    $created[] = $participation;
                } else {
                    $this->hydrateParticipation($participation, $data);
                    $existingIds[] = $participation->getId();
                }
                if ($user) {
                    if($user->getEmail() != $participant->getEmail()) {
                        $user->setEmail($participant->getEmail());
                        $this->em->persist($user);
                        $sendNewEmail = !$user->isHasCreatedPassword() ? EmailSender::MAIL_ACCOUNT : EmailSender::MAIL_PARTICIPATION;
                    }
                    $emailsToSend[] = array(
                        "type" => $sendNewEmail,
                        "notification" => $user->isHasDownloadedApp(),
                        "participation" => $participation
                    );
                    if (is_null($user->getId()) && $participant->hasNoIdentifier()) {
                        $noIdentifierParticipants[] = $participant;
                    }
                }
            }
        }

        /** @var Participation $item */
        foreach ($created as $item) {
            $item->addParticipationHistoryRegister($type);
            $this->em->persist($item);
        }

        if ($removeMissing) {
            $toRemoveIds = array_diff($currentParticipationsIds, $existingIds);

            foreach ($toRemoveIds as $toRemoveId) {
                $participationToRemove = $participationRepository->find($toRemoveId);
                $participationToRemove->setArchived(true);
                $motif = $type == ParticipationHistory::MANUAL_TYPE ? ParticipationHistory::MOTIF_OTHER : ParticipationHistory::MOTIF_AUTO_UNSUB;
                $commentaire = $type == ParticipationHistory::MANUAL_TYPE ? "Suite à import CSV" : null;
                $participationToRemove->addParticipationHistoryUnregister($motif, $commentaire, $type);
                $this->em->persist($participationToRemove);
            }
        }

        // maj date du dernier import
        $formation->setLastImportDate(new \DateTime());

        $this->em->persist($formation);

        $this->em->flush();

        if (count($noIdentifierParticipants) > 0) {
            $this->generateLogins($noIdentifierParticipants);
        }

//        if ($formation->isActalians()) {
//            $this->em->clear();
//            /** @var FormationActalians $formation */
//            $formation = $this->em->getRepository(Formation::class)->find($formation->getId());
//            $formation->setManualParticipantCount($formation->getParticipantCount());
//            $prices = $formation->getParticipations()->map(function(Participation $participation) {
//                return $participation->getPrice();
//            })->toArray();
//            $avgPrice = count($prices) ? round(array_sum($prices) / count($prices), 2) : 0;
//            $formation->setCost($avgPrice);
//            $this->em->persist($formation);
//            $this->em->flush();
//        }

        $this->sendEmail($formation, $emailsToSend);

        return [];
    }

    public function sendEmail(Formation $formation, array $emailsToSend) {
        // On envoie les emails uniquement si la formation le permet et si elle est liée à un formulaire
        if ($formation->canSendEmail()) {
            foreach ($emailsToSend as $item) {
                if ($item['type'] == EmailSender::MAIL_ACCOUNT) {
                    $this->emailSender->sendInscriptionEmail($item["participation"]);
                } elseif ($item['type'] == EmailSender::MAIL_PARTICIPATION) {
                    $this->emailSender->sendParticipationEmail($item["participation"]);
                }
            }
        }
    }

    public function hydrateParticipant(Participant $participant, $data, $geocode = false)
    {
        $participant->setCivility($data[self::FIELD_CIVILITY])
            ->setLastname($data[self::FIELD_LASTNAME] ? $data[self::FIELD_LASTNAME] : $participant->getLastname())
            ->setFirstname($data[self::FIELD_FIRSTNAME] ? $data[self::FIELD_FIRSTNAME] : $participant->getFirstname())
            ->setAddress($data[self::FIELD_ADDRESS] ? $data[self::FIELD_ADDRESS] : $participant->getAddress())
            ->setZipCode($data[self::FIELD_ZIP] ? $data[self::FIELD_ZIP] : $participant->getZipCode())
            ->setCity($data[self::FIELD_CITY] ? $data[self::FIELD_CITY] : $participant->getCity())
            ->setPhone($data[self::FIELD_PHONE] ? $data[self::FIELD_PHONE] : $participant->getPhone())
            ->setBirthName($data[self::FIELD_BIRTHNAME] ? $data[self::FIELD_BIRTHNAME] : $participant->getBirthName())
            ->setRpps($data[self::FIELD_RPPS] ? $data[self::FIELD_RPPS] : $participant->getRpps())
            ->setAdeli($data[self::FIELD_ADELI] ? $data[self::FIELD_ADELI] : $participant->getAdeli())
            ->setEmail($data[self::FIELD_EMAIL] ? $data[self::FIELD_EMAIL] : $participant->getEmail())
            ->setCategory($data[self::FIELD_CATEGORY] ? $data[self::FIELD_CATEGORY] : $participant->getCategory())
            ->setSpeciality($data[self::FIELD_SPECIALITY] ? $data[self::FIELD_SPECIALITY] : $participant->getSpeciality())
            ->setExerciceMode($data[self::FIELD_EXERCISEMODE] ? $data[self::FIELD_EXERCISEMODE] : $participant->getExerciceMode())
        ;

        if ($geocode === true) {
            try {
                $address = sprintf("%s %s %s", $participant->getAddress(), ltrim($participant->getZipCode(), '0'), $participant->getCity());
                if (is_null($participant->getLatitude()) && !empty($address)) {
                    $result = $this->adressesService->getGeocodeAdresse($address);
                    if (count($result["adresse"])) {
                        $coordonnees = explode(",", $result["adresse"][0]["coordonnees"]);
                        $participant->setLatitude($coordonnees[0]);
                        $participant->setLongitude($coordonnees[1]);
                    }
                }
                if (is_null($participant->getUga()) && !empty($address)) {

                    $uga = $this->adressesService->getAddressUGA($participant->getAddress(), $participant->getZipCode(), $participant->getCity(), !is_null($participant->getLatitude()) ? array(
                        $participant->getLongitude(),
                        $participant->getLatitude(),
                    ) : null);
                    if (!is_null($uga)) {
                        $participant->setUga($uga["id"]);
                    }
                }
            } catch (\Exception $exception) {

            }
        }

        return $participant;
    }

    public function csvToDatabaseNoAccountCreation($datas, Formation $formation, $geocode = false)
    {
        $compensationRepository = $this->em->getRepository(Compensation::class);
        foreach($datas as $data) {
            $data = $this->sanitizeRow($data);
            if ($data === false) {
                continue;
            }
            $participant = new Participant();
            $participant = $this->hydrateParticipant($participant, $data, $geocode);
            if(!$participant->isLead()) {
                $participant->setPartenariat($formation->getPartenariat());
                $participant->setOts($formation->getOts());
            }
            $this->em->persist($participant);
            $participation = $this->createParticipation($participant, $formation, $data);
            $this->em->persist($participation);
            $formation->setLastImportDate(new \DateTime());
//            if ($formation->isActalians()) {
//                /** @var FormationActalians $formation */
//                $formation->setParticipantCount($formation->getParticipantCount());
//                $formation->setCost($formation->getParticipations()->first()->getPrice());
//            }
        }

        $this->em->flush();
        $this->em->clear();
        return [];
    }

    public function createParticipation(Participant $participant, Formation $formation, $data) {
        $participation = new Participation();
        $participation->setParticipant($participant);
        $participation->setFormation($formation);
        $this->hydrateParticipation($participation, $data, true);
        if ($participant->isProspect()) {
            $participant->setIsProspect(false);
        }
        $participation->setUga($participant->getUga());
        $participation->setPartenariat($participation->getParticipant()->isLeadOn() ? $participation->getParticipant()->getPartenariat() : $formation->getPartenariat());
        $participation->setOts($participation->getParticipant()->isLeadOn() ? $participation->getParticipant()->getOts() : $formation->getOts());
        return $participation;
    }

    public function hydrateParticipation(Participation $participation, $data, $comeFromCreation = false) {
        $participation->setArchived(false);
        $formation = $participation->getFormation();
        $participant = $participation->getParticipant();

        $participation->setPartenariat($participation->getParticipant()->isLeadOn() ? $participation->getParticipant()->getPartenariat() : $formation->getPartenariat());
        $participation->setOts($participation->getParticipant()->isLeadOn() ? $participation->getParticipant()->getOts() : $formation->getOts());

        if (isset($data[self::FIELD_EXERCISEMODE])) {
            $participation->setExerciseMode($data[self::FIELD_EXERCISEMODE]);
        }
        else {
            $formation = $participation->getFormation();
            if (count($formation->getProgramme()->getExercisesMode()) == 1) {
                $participation->setExerciseMode($formation->getProgramme()->getExercisesMode()[0]);
            } else {
                $participation->setExerciseMode(self::DEFAULT_EXERCICE_MODE);
            }
        }

        if (isset($data[self::FIELD_FINANCESOUSMODE_ID])) {
            $participation->setFinanceSousMode($this->em->getReference(FinanceSousMode::class, $data[self::FIELD_FINANCESOUSMODE_ID]));
        }
        else {

            if ($formation->getFinanceSousModes()->count()) {
                $participation->setFinanceSousMode($formation->getFinanceSousModes()->first());
            }
        }

        $partANDPC = $data[self::FIELD_PART];
        $partANDPC = is_null($partANDPC) ? self::DEFAULT_PART : str_replace(",", ".", $partANDPC);
        if ($partANDPC === "NC") {
            $partANDPC = null;
        }
        $participation->setPrice($partANDPC);
        $this->priceDispatcher->dispatchPluriannualPrice($participation);

        $indemnisationRepository = $this->em->getRepository(Indemnisation::class);
        /** @var Indemnisation $compensation */
        $compensation = $indemnisationRepository->findNbHoursSpeciality($data[self::FIELD_PART], $data[self::FIELD_SPECIALITY], $formation->getProgramme());

        if($compensation) {
            $participation->setNbHour($compensation->getNbHours());
        } else {
            $participation->setNbHour(0);
        }

        // Session privée : pas d'ajout auto de coordinateur et association seulement de la participation
        // au coordinateur de la session si unique, sauf si prospect ou pas de coordinateur associé au participant
        if ($formation->isPrivate()) {
            if (!$participation->getCoordinator() && $formation->getCoordinators()->count() === 1) {
                $participation->setCoordinator($formation->getCoordinators()->first());
                if ($participant->isProspect() || $participation->getParticipant()->getCoordinator() === null) {
                    $participant->setCoordinator($formation->getCoordinators()->first()->getPerson());
                }
            }
        } else {
            if (!$participant->isProspect() && !$participation->getCoordinator()) {
                if ($participant->isLead()) {
                    if ($participant->hasLeadReferent()) {
                        $coordinator = null;
                        $existingCoordinator = $formation->getCoordinators()->filter(function(Coordinator $c) use ($participant) {
                            return $c->getPerson()->getId() === $participant->getLeadReferent()->getId();
                        })->first();
                        // Si la formation ne contient pas le coordinateur relié au participant, on l'ajoute automatiquement
                        if (!$existingCoordinator && !$formation->getCoordinatorsPerson()->contains($participant->getLeadReferent())) {
                            $coordinator = (new Coordinator())->setFormation($formation)->setPerson($participant->getLeadReferent());
                            $formation->addCoordinator($coordinator);
                        } else {
                            $coordinator = $existingCoordinator;
                        }
                        $participation->setCoordinator($coordinator);
                    } else if ($formation->getCoordinators()->count() === 1) {
                        $participation->setCoordinator($formation->getCoordinators()->first());
                        $participant->setCoordinator($formation->getCoordinators()->first()->getPerson());
                    }
                } else {
                    if ($participant->hasCoordinator()) {
                        $coordinator = null;
                        $existingCoordinator = $formation->getCoordinators()->filter(function(Coordinator $c) use ($participant) {
                            return $c->getPerson()->getId() === $participant->getCoordinator()->getId();
                        })->first();
                        // Si la formation ne contient pas le coordinateur relié au participant, on l'ajoute automatiquement
                        if (!$existingCoordinator && !$formation->getCoordinatorsPerson()->contains($participant->getCoordinator())) {
                            $coordinator = (new Coordinator())->setFormation($formation)->setPerson($participant->getCoordinator());
                            $formation->addCoordinator($coordinator);
                        } else {
                            $coordinator = $existingCoordinator;
                        }
                        $participation->setCoordinator($coordinator);
                    } else if ($formation->getCoordinators()->count() === 1) {
                        $participation->setCoordinator($formation->getCoordinators()->first());
                        $participant->setCoordinator($formation->getCoordinators()->first()->getPerson());
                    }
                }
            }

            // Lors de l'import d'un prospect, on ajoute tous les coordinateurs liés à son uga dans la session
            // Si il n'y a qu'un seul coordinateur dans l'uga, on lui associe le participant
            if ($participant->isProspect() && $participant->getUga()) {
                $persons = $this->em->getRepository(Person::class)->findCoordinatorsByUga($participant->getUga());
                foreach ($persons as $person) {
                    if (!$formation->getCoordinatorsPerson()->contains($person)) {
                        $coordinator = (new Coordinator())->setFormation($formation)->setPerson($person);
                        $formation->addCoordinator($coordinator);
                    }
                    if (count($persons) === 1) {
                        $existingCoordinator = $formation->getCoordinators()->filter(function(Coordinator $c) use ($person) {
                            return $c->getPerson()->getId() === $person->getId();
                        })->first();
                        if ($existingCoordinator) {
                            $participation->setCoordinator($existingCoordinator);
                            $participant->setCoordinator($person);
                        }
                    }
                }
            }

            // Si le prospect n'a pas d'uga ni de coordinateur, on lui associe le coordinateur de la session
            if ($participant->isProspect() && !$participation->getCoordinator()) {
                $coordinators = $formation->getCoordinators();
                if (count($coordinators) === 1) {
                    if (!$coordinators[0]->isArchived()) {
                        $participant->setCoordinator($coordinators[0]->getPerson());
                    }
                    $participation->setCoordinator($coordinators[0]);
                }
            }

            if($participant->isLeadOn()) {
                if ($participant->hasLeadReferent()) {
                    $coordinator = null;
                    $existingCoordinator = $formation->getCoordinators()->filter(function(Coordinator $c) use ($participant) {
                        return $c->getPerson()->getId() === $participant->getLeadReferent()->getId();
                    })->first();
                    // Si la formation ne contient pas le coordinateur relié au participant, on l'ajoute automatiquement
                    if (!$existingCoordinator && !$formation->getCoordinatorsPerson()->contains($participant->getLeadReferent())) {
                        $coordinator = (new Coordinator())->setFormation($formation)->setPerson($participant->getLeadReferent());
                        $formation->addCoordinator($coordinator);
                    } else {
                        $coordinator = $existingCoordinator;
                    }
                    $participation->setCoordinator($coordinator);
                } else if ($formation->getCoordinators()->count() === 1) {
                    $participation->setCoordinator($formation->getCoordinators()->first());
                    // $participant->setCoordinator($formation->getCoordinators()->first()->getPerson());
                }
            }
        }

        if($participant->isLeadOn()) {
            $participation->setIsLead(true);
        }

        if ($comeFromCreation) {
            if ($formation->hasCourse()) {
                $this->courseManager->setParticipationNextModule($participation);
            } elseif (!$participation->getNextModule() && $participation->getFormation()->isFormVignetteAudit()) {
                $participation->setNextModule(CourseManager::STEP1_VIDEO_LABEL);
            } else if(!$participation->getNextModule() && count($participation->getFormation()->getProgramme()->getDocumentsPedagogiquesFiles()) && !$participation->isStepCompleted(CourseManager::STEP1_DOC_PEDAGOGIQUE_LABEL)) {
                $participation->setNextModule(CourseManager::STEP1_DOC_PEDAGOGIQUE_LABEL);
            } else if(!$participation->getNextModule() && !$participation->isStepCompleted(CourseManager::STEP1_FORM_PRE_LABEL)) {
                $participation->setNextModule(CourseManager::STEP1_FORM_PRE_LABEL);
            }
            if ($participation->getNextModule() && !$formation->hasCourse()) {
                $this->eventDispatcher->dispatch(new ModuleDoneAndNextAssignIfExists($participation));
            }
        }

        $participation->saveUga();
    }

    public function sanitizeRow(array $data) {
        $sanitizedData = array();
        foreach ($data as $key => $value) {
            if($key == self::FIELD_ZIP || $key == self::FIELD_RPPS || $key == self::FIELD_ADELI) {
                if(trim($value) === "") {
                    $sanitizedData[$key] = null;
                }
                else {
                    if ($key == self::FIELD_ADELI) {
                        $value = str_pad($value, 9, "0", STR_PAD_LEFT);
                    } else if ($key == self::FIELD_ZIP) {
                        $value = str_pad($value, 5, "0", STR_PAD_LEFT);
                    }
                    // Ne pas faire de trim sur zipcode, rpps et adeli
                    // Serveur supprime les 0...
                    $sanitizedData[$key] = $value;
                }
            }
            else {
                if(trim($value) === "") {
                    $sanitizedData[$key] = null;
                }
                else {
                    $sanitizedData[$key] = trim($value);
                }
            }
        }
        // A garder si trim à nouveau nécessaire
        // $data = array_map(function ($str) {
        //     return trim($str) === "" ? null : $str;
        // }, $data);
        // $data[self::FIELD_RPPS] = trim($data[self::FIELD_RPPS], '\'');
        // $data[self::FIELD_ADELI] = trim($data[self::FIELD_ADELI], '\'');

        if (!array_filter($sanitizedData)) {
            return false;
        }

        return $sanitizedData;
    }

    /**
     * @param Participant[] $participants
     */
    public function generateLogins(array $participants) {
        foreach ($participants as $participant) {
            $participant->setLogin(sprintf("%s_%s", $participant->getId(), $participant->getLastname()));
            $this->em->persist($participant);
        }
        $this->em->flush();
    }

    /**
     * Parse a csv file
     *
     * @return array
     */
    public function parseCSV($filename, $nbLinesIgnored = 1, $delimiter = ';', $slugifyHeaders = false)
    {
        if(!$filename || !file_exists($filename) || !is_readable($filename)) {
            return false;
        }
        $i = 1;
        $header = null;
        $data = array();
        $slugger = new AsciiSlugger("fr");
        if (($handle = fopen($filename, 'r')) !== false) {
            while (($row = fgetcsv($handle, 10000, $delimiter)) !== false) {
                foreach ($row as &$item) {
                    $isUtf8 = mb_detect_encoding($item, 'UTF-8', true);
                    if (!$isUtf8) {
                        $item = mb_convert_encoding($item, "UTF-8",  "Windows-1252");
                    }
                }
                if($i >= $nbLinesIgnored) {
                    if(!$header) {
                        $header = array_map(function($str) use ($slugifyHeaders, $slugger) {
                            // Remove BOM characters
                            $bom = pack("CCC", 0xef, 0xbb, 0xbf);
                            if (0 == strncmp($str, $bom, 3)) {
                                $str = substr($str, 3);
                            }
                            if ($slugifyHeaders) {
                                $str = strtolower($slugger->slug($str));
                            }
                            return $str;
                        }, $row);
                    } else {
                        $data[] = array_combine($header, $row);
                    }
                }
                $i++;
            }
            fclose($handle);
        }
        return $data;
    }

    public function export(Formation $formation, $coordinator = false) {
        $headers = array(
            "Réponse Ps",
            "Votre Réponse",
            self::FIELD_EXERCISEMODE,
            self::FIELD_CIVILITY,
            self::FIELD_LASTNAME,
            self::FIELD_FIRSTNAME,
            self::FIELD_PART,
            self::FIELD_ADDRESS,
            self::FIELD_ZIP,
            self::FIELD_CITY,
            self::FIELD_PHONE,
            self::FIELD_BIRTHNAME,
            self::FIELD_RPPS,
            self::FIELD_ADELI,
            self::FIELD_EMAIL,
            self::FIELD_CATEGORY,
            self::FIELD_SPECIALITY,
            "Date action Ps",
            "Date action Organisme",
            "Coordinateur",
            self::FIELD_FINANCESOUSMODE_IDENTIFIANT,
            self::FIELD_FINANCESOUSMODE_NAME,
            self::FIELD_FINANCESOUSMODE_ID,
        );
        $data = array($headers);
        /** @var Participation $participation */
        foreach ($formation->getParticipations() as $participation) {
            $price = !is_null($participation->getPrice()) ? $participation->getPrice() : 0;
            $priceN1 = !is_null($participation->getPriceYearN1()) ? $participation->getPriceYearN1() : 0;
            $totalPrice = $price + $priceN1;
            $participant = array(
                "",
                "",
                $participation->getExerciseMode(),
                $participation->getParticipant()->getCivility(),
                $participation->getParticipant()->getLastname(),
                $participation->getParticipant()->getFirstname(),
                !is_null($participation->getPrice()) || !is_null($participation->getPriceYearN1()) ? $totalPrice : "NC",
                $participation->getParticipant()->getAddress(),
                $participation->getParticipant()->getZipCode(),
                $participation->getParticipant()->getCity(),
                $participation->getParticipant()->getPhone(),
                $participation->getParticipant()->getBirthName(),
                $participation->getParticipant()->getRpps(),
                $participation->getParticipant()->getAdeli(),
                $participation->getParticipant()->getEmail(),
                $participation->getParticipant()->getCategory(),
                $participation->getParticipant()->getSpeciality(),
                "",
                "",
                $participation->getCoordinator() ? $participation->getCoordinator()->getPerson()->getInvertedFullname() : "",
                $participation->getFinanceSousMode()->getIdentifiant(),
                $participation->getFinanceSousMode()->getName(),
                $participation->getFinanceSousMode()->getId(),
            );

            if($coordinator) {
                if($participation->getCoordinator()->getPerson() == $coordinator) {
                    $data[] = $participant;
                }
            } else {
                $data[] = $participant;
            }

        }
        return $data;
    }
}
