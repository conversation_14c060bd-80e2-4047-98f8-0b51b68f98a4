<?php

namespace Eduprat\AdminBundle\Twig;

use Eduprat\AdminBundle\Entity\Person;
use Eduprat\AdminBundle\Services\DashboardGenerator;
use Twig\Extension\AbstractExtension;
use Twig\TwigFunction;

class DashboardExtension extends AbstractExtension
{
    /**
     * @var DashboardGenerator
     */
    private $dashboardGenerator;

    public function __construct(DashboardGenerator $dashboardGenerator)
    {
        $this->dashboardGenerator = $dashboardGenerator;
    }

    public function getFunctions(): array
    {
        return array(
            new TwigFunction('dashboardList', $this->getDashboardList(...)),
            new TwigFunction('hasDashboard', $this->hasDashboard(...)),
        );
    }

    public function getDashboardList(Person $person) {
        return $this->dashboardGenerator->getDashboardList($person);
    }

    public function hasDashboard(Person $person): bool
    {
        return $this->dashboardGenerator->hasDashboard($person);
    }

    public function getName(): string
    {
        return 'dashboard_extension';
    }
}