<?php

namespace Eduprat\AdminBundle\Twig;

use Twig\Extension\AbstractExtension;
use Twig\TwigFunction;

class EvaluationDispatcherExtension extends AbstractExtension
{
    /**
     * @var \DateTime
     */
    private $migrationDate;

    public function __construct($evaluationMigrationDate)
    {
        $this->migrationDate = new \DateTime($evaluationMigrationDate);
    }

    public function getFunctions(): array
    {
        return array(
            new TwigFunction('isNewEvaluation', $this->isNewEvaluation(...)),
        );
    }

    public function isNewEvaluation($date): bool
    {
        if (!$date instanceof \DateTime) {
            $date = new \DateTime($date);
        }
        return $date > $this->migrationDate;
    }

    public function getName(): string
    {
        return 'evaluation_dispatcher_extension';
    }
}