<?php

namespace Eduprat\AdminBundle\Twig;

use <PERSON>ymfony\Component\Routing\RouterInterface;
use Twig\Extension\AbstractExtension;
use Twig\TwigFilter;

class DownloadLinkModuleExtension extends AbstractExtension
{
    private RouterInterface $router;

    public function __construct(RouterInterface $router)
    {
        $this->router = $router;
    }

    public function getFilters(): array
    {
        return array(
            new TwigFilter('loadLinkDownload', $this->loadLinkDownload(...)),
            new TwigFilter('loadDataAttributesForModule', $this->loadDataAttributesForModule(...)),
        );
    }

    public function loadLinkDownload($participation, $module): ?string
    {
        switch ($module['id']) {
            case 'topos':
                if (count($participation->getFormation()->getTopoFiles()) > 0) {
                    return $this->router->generate('eduprat_audit_formation_topo', [
                        'id' => $participation->getFormation()->getTopoFiles()[0]->getId(),
                        'token' => $participation->getFormation()->getTopoFiles()[0]->getToken(),
                    ]);
                }
            case 'form_presession':
                return $this->generateUrlFormSession($participation, 1);
            case 'form_postsession':
                return $this->generateUrlFormSession($participation, 2);
            case 'fiche_action_1':
                if ($participation->isStepCompleted("fiche_action_1")) {
                    return $this->router->generate('eduprat_front_module_fiche_action_download', ['id' => $participation->getId()]);
                } else {
                    return $this->router->generate('pdf_action_empty_pdf', ['id' => $participation->getFormation()->getId(), 'token' => $participation->getFormation()->getToken()]);
                }
            case 'fiche_action_2':
                return $this->router->generate('eduprat_front_module_fiche_action_download', ['id' => $participation->getId()]);
            case 'prerestitution':
                return $this->router->generate('pdf_audit_restitution_groupe_individuelle_pdf', ['id' => $participation->getId(), 'token' => $participation->getToken()]);
            case 'restitution':
                return $this->router->generate('pdf_restitution_audit_pdf', ['id' => $participation->getId(), 'token' => $participation->getToken()]);
            case 'synthese':
                if ($participation->isStepCompleted("synthese")) {
                    return $this->router->generate('pdf_synthese_full_pdf', ['participation' => $participation->getId(), 'token' => $participation->getFormation()->getToken(), 'id' => $participation->getFormation()->getId()]);
                }
                return $this->router->generate('pdf_synthese_empty_pdf', ['participation' => $participation->getId(), 'token' => $participation->getFormation()->getToken(), 'id' => $participation->getFormation()->getId()]);
            default:
                return null;
        }
    }

    public function loadDataAttributesForModule($participation, $module): array
    {
        switch ($module['id']) {
            case 'form_presession':
            case 'form_postsession':
                if (! $participation->getFormation()->isFormPresentielle()) {
                    return ['data-type' => 'prerestitution', 'data-participation' => $participation->getId()];
                } else {
                    return ['data-participation' => $participation->getId()];
                }
            case 'prerestitution':
                return ['data-participation' => $participation->getId(), 'data-type' => 'prerestitution'];
            case 'restitution':
                return ['data-type' => 'prerestitution', 'data-participation' => $participation->getId()];
        }
        return [];
    }


    public function getName()
    {
        return 'download_link_module_extension';
    }

    private function generateUrlFormSession($participation, int $auditId): string
    {
        if ($participation->getFormation()->isTcs()) {
            return $this->router->generate('pdf_tcs_pdf', ['id' => $participation->getId(), 'token' => $participation->getToken()]);
        }
        if (!$participation->getFormation()->isFormPresentielle()) {
            return $this->router->generate('pdf_audit_pdf', ['id' => $participation->getId(), 'auditId' => $auditId, 'token' => $participation->getToken()]);
        }
        return $this->router->generate('pdf_survey_pdf', ['id' => $participation->getId(), 'surveyId' => $auditId, 'token' => $participation->getToken()]);
    }
}
